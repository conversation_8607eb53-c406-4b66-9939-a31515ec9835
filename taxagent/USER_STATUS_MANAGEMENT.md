# 用户状态管理功能

## 功能概述

新增了4个用户状态管理接口的前端实现，支持对用户进行激活、停用、锁定和解锁操作。

## 新增接口

1. **激活用户**: `POST /api/v1/sys/users/{userId}/activate`
2. **停用用户**: `POST /api/v1/sys/users/{userId}/deactivate`
3. **锁定用户**: `POST /api/v1/sys/users/{userId}/lock`
4. **解锁用户**: `POST /api/v1/sys/users/{userId}/unlock`

## 用户状态说明

- **正常 (1)**: 用户可以正常登录和使用系统
- **禁用 (0)**: 用户被停用，无法登录系统
- **锁定 (-1)**: 用户被锁定，无法登录系统

## 前端实现

### 1. API 接口层 (`src/api/user.js`)

```javascript
// 激活用户
activateUser(userId) {
  return request({
    url: `/v1/sys/users/${userId}/activate`,
    method: 'post'
  })
}

// 停用用户
deactivateUser(userId) {
  return request({
    url: `/v1/sys/users/${userId}/deactivate`,
    method: 'post'
  })
}

// 锁定用户
lockUser(userId) {
  return request({
    url: `/v1/sys/users/${userId}/lock`,
    method: 'post'
  })
}

// 解锁用户
unlockUser(userId) {
  return request({
    url: `/v1/sys/users/${userId}/unlock`,
    method: 'post'
  })
}
```

### 2. 组合式函数 (`src/composables/useUserStatus.js`)

提供了用户状态操作的统一管理：

- `activateUser()` - 激活用户
- `deactivateUser()` - 停用用户
- `lockUser()` - 锁定用户
- `unlockUser()` - 解锁用户
- `batchStatusOperation()` - 批量状态操作
- `getUserStatusText()` - 获取状态文本
- `getUserStatusColor()` - 获取状态颜色

### 3. 状态操作组件 (`src/components/UserStatusActions.vue`)

可复用的用户状态操作按钮组件：

```vue
<UserStatusActions
  :user="userRow"
  @success="handleStatusActionSuccess"
  @error="handleStatusActionError"
/>
```

### 4. 用户管理页面功能

#### 单个用户操作
- 在用户列表的操作列中，根据用户当前状态显示相应的操作按钮
- 激活按钮：仅对禁用用户显示
- 停用按钮：仅对正常用户显示
- 锁定按钮：对正常和禁用用户显示
- 解锁按钮：仅对锁定用户显示

#### 批量操作
- 在工具栏添加了"批量操作"下拉菜单
- 支持批量激活、停用、锁定、解锁操作
- 操作前会进行权限检查和确认提示

## 权限控制

所有状态操作都需要 `platform:user:manage` 权限，并且会进行租户权限检查：

```javascript
// 检查租户权限
if (!tenantPermission.canManageUser(user)) {
  ElMessage.warning('您没有权限操作该用户')
  return
}
```

## 使用方法

### 1. 访问用户管理页面
打开 http://localhost:3000 并导航到"系统管理" -> "用户管理"

### 2. 单个用户状态操作
- 在用户列表中找到目标用户
- 点击相应的状态操作按钮（激活/停用/锁定/解锁）
- 确认操作后，系统会执行相应的状态变更

### 3. 批量状态操作
- 选择多个用户（使用表格左侧的复选框）
- 点击工具栏的"批量操作"下拉菜单
- 选择要执行的操作（批量激活/停用/锁定/解锁）
- 确认操作后，系统会对所有选中用户执行相应操作

### 4. 权限调试
- 如果看不到操作按钮，点击页面右下角的 🐛 按钮打开权限调试面板
- 检查当前用户是否有 `platform:user:manage` 权限
- 如需要，可以点击"临时授权测试权限"按钮进行测试

## 状态变更逻辑

| 当前状态 | 可执行操作 | 操作后状态 |
|---------|-----------|-----------|
| 正常 (1) | 停用、锁定 | 禁用 (0)、锁定 (-1) |
| 禁用 (0) | 激活、锁定 | 正常 (1)、锁定 (-1) |
| 锁定 (-1) | 解锁 | 正常 (1) |

## 错误处理

- 网络错误：显示相应的错误提示
- 权限不足：显示权限不足提示
- 操作失败：显示具体的失败原因
- 批量操作：会统计成功和失败的数量并分别提示

## 注意事项

1. 所有状态操作都会显示确认对话框，防止误操作
2. 批量操作会检查每个用户的权限，跳过无权限操作的用户
3. 操作完成后会自动刷新用户列表以显示最新状态
4. 状态按钮会根据用户当前状态动态显示/隐藏
