# TaxAgent 管理系统前端

基于 Vue3 + Element Plus 构建的现代化管理系统前端，支持多租户权限控制。

## 功能特性

### 🔐 认证与授权
- 用户登录/登出
- JWT Token 管理
- 路由守卫
- 权限验证
- 多租户权限控制

### 👥 用户管理
- 用户列表分页查询
- 用户新增、编辑、删除
- 用户角色分配
- 租户级别的用户管理权限控制

### 🌐 在线用户管理
- 在线用户列表查询
- 用户会话信息展示
- 强制用户下线
- 批量强制下线

### 🎭 角色管理
- 角色列表管理
- 角色新增、编辑、删除
- 角色权限分配
- 租户级别的角色权限过滤

### 🔑 权限管理
- 权限列表管理
- 权限新增、编辑、删除
- 权限编码规范管理

### 📋 资源管理
- 资源树形结构展示
- 菜单、按钮、接口资源管理
- 资源层级关系管理
- 图标选择支持

### 🏢 多租户支持
- 租户级别的数据隔离
- 租户管理员权限控制
- 超级管理员全局管理
- 租户信息展示

## 技术栈

- **框架**: Vue 3.4+
- **构建工具**: Vite 5.0+
- **UI 组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **HTTP 客户端**: Axios 1.6+
- **国际化**: Vue I18n 9.14+
- **样式**: SCSS
- **图标**: Element Plus Icons
- **工具库**: Day.js, js-cookie, nprogress

## 项目结构

```
src/
├── api/                    # API 接口定义
│   ├── auth.js            # 认证相关接口
│   ├── user.js            # 用户管理接口
│   ├── role.js            # 角色管理接口
│   ├── permission.js      # 权限管理接口
│   ├── resource.js        # 资源管理接口
│   ├── onlineUser.js      # 在线用户接口
│   └── index.js           # 接口统一导出
├── components/            # 公共组件
│   ├── TenantInfo.vue     # 租户信息组件
│   └── LanguageSwitcher.vue # 语言切换器
├── composables/           # 组合函数
│   └── usePageI18n.js     # 页面国际化组合函数
├── layout/                # 布局组件
│   ├── index.vue          # 主布局
│   └── components/        # 布局子组件
│       ├── Header.vue     # 顶部导航
│       ├── Sidebar.vue    # 侧边栏
│       ├── SidebarItem.vue # 菜单项
│       └── Breadcrumb.vue # 面包屑
├── locales/               # 国际化语言包
│   ├── index.js           # i18n 配置
│   ├── zh-CN.js           # 中文语言包
│   └── en-US.js           # 英文语言包
├── router/                # 路由配置
│   └── index.js           # 路由定义
├── stores/                # 状态管理
│   └── auth.js            # 认证状态
├── styles/                # 样式文件
│   ├── index.scss         # 主样式文件
│   ├── variables.scss     # 变量定义
│   ├── reset.scss         # 重置样式
│   └── common.scss        # 通用样式类
├── utils/                 # 工具函数
│   ├── request.js         # HTTP 请求封装
│   ├── auth.js            # 认证工具
│   ├── common.js          # 通用工具
│   ├── tenant.js          # 租户权限控制
│   ├── menu.js            # 菜单国际化工具
│   └── i18n-helper.js     # 国际化辅助工具
├── views/                 # 页面组件
│   ├── auth/              # 认证页面
│   │   └── Login.vue      # 登录页
│   ├── dashboard/         # 仪表盘
│   │   └── index.vue      # 仪表盘首页
│   ├── error/             # 错误页面
│   │   └── 404.vue        # 404页面
│   └── system/            # 系统管理
│       ├── users/         # 用户管理
│       ├── roles/         # 角色管理
│       ├── permissions/   # 权限管理
│       ├── resources/     # 资源管理
│       └── online-users/  # 在线用户
└── main.js                # 应用入口
```

## 快速开始

### 环境要求

- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+

### 后端 API 配置

在启动前端之前，请确保后端 API 服务已经启动并运行在正确的端口上。

#### 开发环境
默认后端 API 地址：`http://localhost:8080/api`

如需修改，请编辑 `.env.development` 文件：
```bash
VITE_API_BASE_URL=http://your-backend-host:port/api
```

#### 生产环境
默认后端 API 地址：`/api`（相对路径）

如需修改，请编辑 `.env.production` 文件：
```bash
VITE_API_BASE_URL=https://your-domain.com/api
```

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 开发环境运行

```bash
npm run dev
# 或
yarn dev
```

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

## 🌍 多语言支持

系统完全支持多语言功能，目前支持：

- 🇨🇳 **简体中文** (zh-CN) - 默认语言
- 🇺🇸 **English** (en-US)

### 语言切换

- **登录页面**：右上角语言切换器
- **主界面**：头部导航栏语言切换器
- **自动保存**：用户选择的语言会保存到本地存储

### 开发者指南

详细的多语言开发指南请参考：[MULTILINGUAL_GUIDE.md](./MULTILINGUAL_GUIDE.md)

#### 快速使用

```vue
<template>
  <div>
    <h1>{{ t('user.title', '用户管理') }}</h1>
    <el-button>{{ t('actions.save', '保存') }}</el-button>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
const { t } = useI18n()
</script>
```

## 🔌 后端 API 接口

前端需要后端提供以下 API 接口：

### 认证接口
- `POST /v1/auth/login` - 用户登录
- `POST /v1/auth/logout` - 用户登出
- `GET /v1/auth/permissions` - 获取当前用户权限

### 用户管理接口
- `GET /v1/users` - 获取用户列表
- `POST /v1/users` - 创建用户
- `PUT /v1/users/{id}` - 更新用户
- `DELETE /v1/users/{id}` - 删除用户

### 角色管理接口
- `GET /v1/roles` - 获取角色列表
- `POST /v1/roles` - 创建角色
- `PUT /v1/roles/{id}` - 更新角色
- `DELETE /v1/roles/{id}` - 删除角色

### 权限管理接口
- `GET /v1/permissions` - 获取权限列表
- `POST /v1/permissions` - 创建权限
- `PUT /v1/permissions/{id}` - 更新权限
- `DELETE /v1/permissions/{id}` - 删除权限

### 资源管理接口
- `GET /v1/resources` - 获取资源列表
- `POST /v1/resources` - 创建资源
- `PUT /v1/resources/{id}` - 更新资源
- `DELETE /v1/resources/{id}` - 删除资源

### 在线用户接口
- `GET /v1/online-users` - 获取在线用户列表
- `POST /v1/online-users/{id}/kick` - 强制用户下线

### 预览生产版本

```bash
npm run preview
# 或
yarn preview
```

## 配置说明

### 环境变量

创建 `.env.local` 文件配置本地环境变量：

```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:8080/api

# 应用标题
VITE_APP_TITLE=TaxAgent 管理系统
```

### 代理配置

在 `vite.config.js` 中配置开发服务器代理：

```javascript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      secure: false
    }
  }
}
```

## 权限控制

### 角色层级

1. **super_admin**: 超级管理员
   - 可以管理所有租户和用户
   - 可以访问所有系统功能
   - 可以分配所有角色和权限

2. **tenant_admin**: 租户管理员
   - 只能管理当前租户下的用户
   - 可以访问部分系统管理功能
   - 不能分配平台级别的角色

3. **user**: 普通用户
   - 只能查看和操作自己的数据
   - 不能访问管理功能

### 权限编码规范

权限编码采用三级结构：`模块:资源:操作`

示例：
- `platform:user:manage` - 平台用户管理
- `tenant:role:assign` - 租户角色分配
- `system:resource:view` - 系统资源查看

### 租户隔离

- 数据查询自动添加租户过滤条件
- 用户操作权限基于租户关系验证
- 角色和权限按租户级别过滤显示

## API 接口

### 认证接口

- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/permissions` - 获取当前用户权限

### 用户管理接口

- `GET /api/v1/sys/users/page` - 分页查询用户
- `POST /api/v1/sys/users` - 创建用户
- `PUT /api/v1/sys/users/{id}` - 更新用户
- `DELETE /api/v1/sys/users/{id}` - 删除用户
- `POST /api/v1/sys/users/{id}/roles` - 分配角色

### 在线用户接口

- `GET /api/v1/online-users` - 获取在线用户列表
- `DELETE /api/v1/online-users/{id}` - 强制用户下线
- `DELETE /api/v1/online-users/batch` - 批量强制下线

## 开发指南

### 新增页面

1. 在 `src/views` 下创建页面组件
2. 在 `src/router/index.js` 中添加路由配置
3. 设置页面权限和租户控制
4. 添加菜单项（如需要）

### 新增API

1. 在 `src/api` 下创建或更新API文件
2. 使用统一的请求封装
3. 处理错误和加载状态
4. 添加租户过滤（如需要）

### 样式开发

1. 使用 SCSS 预处理器
2. 遵循 BEM 命名规范
3. 使用 CSS 变量定义主题
4. 响应式设计支持

## 部署说明

### 构建配置

生产环境构建会自动：
- 压缩代码和资源
- 生成 source map（可配置）
- 优化资源加载
- 分离第三方库

### 服务器配置

推荐使用 Nginx 作为静态文件服务器：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理
    location /api {
        proxy_pass http://backend-server:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 许可证

MIT License
