# 用户管理功能测试指南

## 🧪 新增用户功能测试

### 测试步骤

1. **访问用户管理页面**
   - 登录系统后，点击侧边栏的"用户管理"菜单
   - 或直接访问：`http://localhost:3000/users`

2. **打开新增用户对话框**
   - 点击页面右上角的"新增用户"按钮
   - 应该弹出一个标题为"新增用户"的对话框

3. **填写用户信息**
   ```
   用户名: testuser001
   密码: 12345678
   邮箱: <EMAIL>
   姓名: 测试用户001
   电话: 13800138001
   状态: 正常
   ```

4. **提交表单**
   - 点击"确定"按钮
   - 系统应该显示"用户创建成功"的提示消息
   - 对话框应该自动关闭
   - 用户列表应该刷新并显示新创建的用户

### 表单验证测试

#### 必填字段验证
1. **用户名验证**
   - 留空：应显示"请输入用户名"
   - 少于3个字符：应显示"用户名长度在 3 到 50 个字符"
   - 超过50个字符：应显示"用户名长度在 3 到 50 个字符"

2. **密码验证**
   - 留空：应显示"请输入密码"
   - 少于8个字符：应显示"密码长度在 8 到 32 个字符"
   - 超过32个字符：应显示"密码长度在 8 到 32 个字符"

3. **邮箱验证**
   - 留空：应显示"请输入邮箱"
   - 格式错误：应显示"请输入正确的邮箱格式"

#### 可选字段验证
1. **姓名验证**
   - 可以留空
   - 超过128个字符：应显示"姓名长度不能超过 128 个字符"

2. **电话验证**
   - 可以留空
   - 格式错误：应显示"请输入正确的手机号格式"

### 多语言测试

1. **切换到英文**
   - 点击右上角的语言切换器
   - 选择"🇺🇸 English"
   - 对话框标题应变为"Create User"
   - 所有表单标签和按钮应变为英文

2. **切换回中文**
   - 选择"🇨🇳 简体中文"
   - 所有文本应恢复为中文

### API 调用测试

#### 成功场景
- 填写正确的用户信息
- 点击提交
- 检查浏览器开发者工具的 Network 标签页
- 应该看到一个 `POST /api/v1/sys/users` 的请求
- 响应状态应该是 200 或 201

#### 失败场景
- 使用已存在的用户名
- 应该显示相应的错误消息
- 对话框不应该关闭

### 权限测试

1. **有权限的用户**
   - 应该能看到"新增用户"按钮
   - 应该能成功创建用户

2. **无权限的用户**
   - "新增用户"按钮应该被隐藏或禁用
   - 即使通过其他方式访问，也应该被拒绝

## 🔧 故障排除

### 常见问题

1. **对话框不显示**
   - 检查浏览器控制台是否有 JavaScript 错误
   - 确认 UserForm 组件是否正确导入

2. **表单验证不工作**
   - 检查 validateUtils 是否正确导入
   - 确认表单规则是否正确定义

3. **API 调用失败**
   - 检查后端服务是否运行在 `http://localhost:8080`
   - 确认 API 路径是否正确：`/api/v1/sys/users`
   - 检查请求头是否包含正确的 Authorization token

4. **多语言不工作**
   - 确认 Vue I18n 是否正确配置
   - 检查语言包是否包含所需的翻译键

### 调试步骤

1. **打开浏览器开发者工具**
   - 按 F12 或右键选择"检查"

2. **查看 Console 标签页**
   - 检查是否有 JavaScript 错误
   - 查看调试日志

3. **查看 Network 标签页**
   - 监控 API 请求和响应
   - 检查请求参数和响应数据

4. **查看 Vue DevTools**
   - 如果安装了 Vue DevTools 扩展
   - 可以查看组件状态和数据流

## 📋 测试清单

- [ ] 新增用户按钮显示正常
- [ ] 点击按钮弹出对话框
- [ ] 对话框标题正确显示
- [ ] 所有表单字段显示正常
- [ ] 必填字段验证工作正常
- [ ] 可选字段验证工作正常
- [ ] 邮箱格式验证正确
- [ ] 手机号格式验证正确
- [ ] 提交成功显示成功消息
- [ ] 对话框自动关闭
- [ ] 用户列表自动刷新
- [ ] 多语言切换正常
- [ ] 权限控制正常
- [ ] API 调用正确
- [ ] 错误处理正常

## 🎯 预期结果

完成所有测试后，新增用户功能应该：

1. **界面完整**：所有必要的表单字段都存在
2. **验证有效**：客户端验证能正确阻止无效数据
3. **提交成功**：能成功创建新用户
4. **反馈及时**：操作结果有明确的用户反馈
5. **多语言支持**：中英文切换正常
6. **权限控制**：按权限显示或隐藏功能
7. **错误处理**：能优雅处理各种错误情况

如果所有测试都通过，说明新增用户功能已经完全实现并正常工作！
