<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useThemeStore } from '@/stores/theme'

// App.vue 主应用组件
const authStore = useAuthStore()
const themeStore = useThemeStore()

let cleanupTheme = null

// 应用初始化
onMounted(async () => {
  // 初始化主题
  cleanupTheme = themeStore.initTheme()

  // 如果有token但没有用户信息，尝试恢复认证状态
  if (authStore.token && !authStore.userInfo) {
    console.log('🔄 应用启动时检查认证状态')
    await authStore.checkAuth()
  }
})

// 清理资源
onUnmounted(() => {
  if (cleanupTheme) {
    cleanupTheme()
  }
})
</script>

<style>
#app {
  height: 100vh;
  width: 100vw;
}
</style>
