// 菜单国际化配置
import i18n from '@/locales'

// 菜单标题映射
export const MENU_TITLES = {
  'Dashboard': 'menu.dashboard',
  'System': 'menu.system',
  'Users': 'menu.users',
  'OnlineUsers': 'menu.onlineUsers',
  'Roles': 'menu.roles',
  'Permissions': 'menu.permissions',
  'Resources': 'menu.resources'
}

// 获取菜单标题
export function getMenuTitle(routeName) {
  const titleKey = MENU_TITLES[routeName]
  if (titleKey && i18n.global) {
    try {
      return i18n.global.t(titleKey)
    } catch (error) {
      console.warn('Failed to translate menu title:', error)
    }
  }
  return routeName
}

// 更新路由元信息的标题
export function updateRouteTitle(route) {
  if (route.name && MENU_TITLES[route.name] && i18n.global) {
    const titleKey = MENU_TITLES[route.name]
    try {
      return {
        ...route,
        meta: {
          ...route.meta,
          title: i18n.global.t(titleKey)
        }
      }
    } catch (error) {
      console.warn('Failed to update route title:', error)
    }
  }
  return route
}

// 递归更新路由树的标题
export function updateRoutesTitle(routes) {
  return routes.map(route => {
    const updatedRoute = updateRouteTitle(route)
    
    if (updatedRoute.children) {
      updatedRoute.children = updateRoutesTitle(updatedRoute.children)
    }
    
    return updatedRoute
  })
}
