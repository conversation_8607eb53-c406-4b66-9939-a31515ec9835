// 国际化辅助工具
import { useI18n } from 'vue-i18n'

// 创建一个可复用的国际化组合函数
export function useI18nHelper() {
  const { t, locale } = useI18n()
  
  // 带默认值的翻译函数
  const $t = (key, defaultValue = '') => {
    try {
      const result = t(key)
      return result !== key ? result : defaultValue
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error)
      return defaultValue
    }
  }
  
  // 表格列配置的国际化
  const getTableColumns = (columns) => {
    return columns.map(column => ({
      ...column,
      label: $t(column.labelKey, column.label)
    }))
  }
  
  // 表单验证规则的国际化
  const getValidationRules = (rules) => {
    const translatedRules = {}
    
    Object.keys(rules).forEach(field => {
      translatedRules[field] = rules[field].map(rule => ({
        ...rule,
        message: rule.messageKey ? $t(rule.messageKey, rule.message) : rule.message
      }))
    })
    
    return translatedRules
  }
  
  // 状态选项的国际化
  const getStatusOptions = (options) => {
    return options.map(option => ({
      ...option,
      label: $t(option.labelKey, option.label)
    }))
  }
  
  // 操作按钮的国际化
  const getActionButtons = (buttons) => {
    return buttons.map(button => ({
      ...button,
      text: $t(button.textKey, button.text),
      tooltip: button.tooltipKey ? $t(button.tooltipKey, button.tooltip) : button.tooltip
    }))
  }
  
  return {
    t: $t,
    locale,
    getTableColumns,
    getValidationRules,
    getStatusOptions,
    getActionButtons
  }
}

// 常用的表格列配置
export const commonTableColumns = {
  index: {
    type: 'index',
    label: '序号',
    labelKey: 'table.index',
    width: 60,
    align: 'center'
  },
  actions: {
    label: '操作',
    labelKey: 'table.actions',
    width: 200,
    align: 'center',
    fixed: 'right'
  },
  createTime: {
    prop: 'createTime',
    label: '创建时间',
    labelKey: 'common.createTime',
    width: 180,
    sortable: true
  },
  updateTime: {
    prop: 'updateTime',
    label: '更新时间',
    labelKey: 'common.updateTime',
    width: 180,
    sortable: true
  },
  status: {
    prop: 'status',
    label: '状态',
    labelKey: 'common.status',
    width: 100,
    align: 'center'
  }
}

// 常用的表单验证规则
export const commonValidationRules = {
  required: {
    required: true,
    message: '此字段为必填项',
    messageKey: 'validation.required',
    trigger: 'blur'
  },
  email: {
    type: 'email',
    message: '请输入有效的邮箱地址',
    messageKey: 'validation.email',
    trigger: 'blur'
  },
  phone: {
    pattern: /^1[3-9]\d{9}$/,
    message: '请输入有效的手机号码',
    messageKey: 'validation.phone',
    trigger: 'blur'
  },
  username: {
    min: 3,
    max: 50,
    message: '用户名长度在 3 到 50 个字符',
    messageKey: 'login.usernameLength',
    trigger: 'blur'
  },
  password: {
    min: 6,
    max: 100,
    message: '密码长度在 6 到 100 个字符',
    messageKey: 'login.passwordLength',
    trigger: 'blur'
  }
}

// 常用的状态选项
export const commonStatusOptions = [
  {
    value: 1,
    label: '启用',
    labelKey: 'status.active',
    type: 'success'
  },
  {
    value: 0,
    label: '禁用',
    labelKey: 'status.inactive',
    type: 'danger'
  }
]

// 常用的操作按钮
export const commonActionButtons = {
  add: {
    type: 'primary',
    text: '新增',
    textKey: 'actions.add',
    icon: 'Plus'
  },
  edit: {
    type: 'primary',
    text: '编辑',
    textKey: 'actions.edit',
    icon: 'Edit',
    size: 'small'
  },
  delete: {
    type: 'danger',
    text: '删除',
    textKey: 'actions.delete',
    icon: 'Delete',
    size: 'small'
  },
  view: {
    type: 'info',
    text: '查看',
    textKey: 'actions.view',
    icon: 'View',
    size: 'small'
  },
  search: {
    type: 'primary',
    text: '搜索',
    textKey: 'actions.search',
    icon: 'Search'
  },
  reset: {
    text: '重置',
    textKey: 'actions.reset',
    icon: 'Refresh'
  },
  export: {
    type: 'success',
    text: '导出',
    textKey: 'actions.export',
    icon: 'Download'
  },
  import: {
    type: 'warning',
    text: '导入',
    textKey: 'actions.import',
    icon: 'Upload'
  }
}
