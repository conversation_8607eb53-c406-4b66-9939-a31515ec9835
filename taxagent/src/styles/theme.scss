// 主题样式
:root {
  // 浅色主题（默认）
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f7fa;
  --theme-bg-tertiary: #ebeef5;
  --theme-text-primary: #303133;
  --theme-text-secondary: #606266;
  --theme-text-tertiary: #909399;
  --theme-border-primary: #dcdfe6;
  --theme-border-secondary: #e4e7ed;
  --theme-shadow: rgba(0, 0, 0, 0.1);
}

// 深色主题
.theme-dark {
  --theme-bg-primary: #1a1a1a;
  --theme-bg-secondary: #141414;
  --theme-bg-tertiary: #0f0f0f;
  --theme-text-primary: #e5eaf3;
  --theme-text-secondary: #cfd3dc;
  --theme-text-tertiary: #a3a6ad;
  --theme-border-primary: #4c4d4f;
  --theme-border-secondary: #414243;
  --theme-shadow: rgba(0, 0, 0, 0.3);

  // Element Plus 组件深色主题适配
  .el-card {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .el-table {
    background-color: var(--theme-bg-primary);
    color: var(--theme-text-primary);

    th.el-table__cell {
      background-color: var(--theme-bg-secondary);
      color: var(--theme-text-primary);
      border-color: var(--theme-border-primary);
    }

    td.el-table__cell {
      border-color: var(--theme-border-secondary);
    }

    .el-table__row:hover > td {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .el-menu {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);

    .el-menu-item {
      color: var(--theme-text-secondary);

      &:hover {
        background-color: var(--theme-bg-tertiary);
        color: var(--theme-text-primary);
      }

      &.is-active {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
    }

    .el-sub-menu__title {
      color: var(--theme-text-secondary);

      &:hover {
        background-color: var(--theme-bg-tertiary);
        color: var(--theme-text-primary);
      }
    }
  }

  .el-header {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .el-aside {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .el-main {
    background-color: var(--theme-bg-secondary);
  }

  .el-form-item__label {
    color: var(--theme-text-primary);
  }

  .el-input__wrapper {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .el-select .el-input__wrapper {
    background-color: var(--theme-bg-primary);
  }

  .el-pagination {
    .el-pagination__total,
    .el-pagination__jump {
      color: var(--theme-text-secondary);
    }
  }

  // 自定义组件样式
  .page-container {
    background-color: var(--theme-bg-secondary);
  }

  .page-header {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .page-title {
    color: var(--theme-text-primary);
  }

  .toolbar-card {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .table-card {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  .search-card {
    background-color: var(--theme-bg-primary);
    border-color: var(--theme-border-primary);
  }

  // 滚动条样式
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--theme-bg-secondary);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--theme-border-primary);
    border-radius: 4px;

    &:hover {
      background: var(--theme-text-tertiary);
    }
  }
}

// 浅色主题
.theme-light {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f7fa;
  --theme-bg-tertiary: #ebeef5;
  --theme-text-primary: #303133;
  --theme-text-secondary: #606266;
  --theme-text-tertiary: #909399;
  --theme-border-primary: #dcdfe6;
  --theme-border-secondary: #e4e7ed;
  --theme-shadow: rgba(0, 0, 0, 0.1);
}

// 主题过渡动画
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

// 禁用某些元素的过渡动画以提高性能
.el-table,
.el-table__body,
.el-scrollbar__view {
  transition: none !important;
}
