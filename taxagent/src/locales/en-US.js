// English language pack
export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    reset: 'Reset',
    refresh: 'Refresh',
    submit: 'Submit',
    back: 'Back',
    close: 'Close',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    remark: 'Remark',
    yes: 'Yes',
    no: 'No',
    enable: 'Enable',
    disable: 'Disable',
    success: 'Success',
    failed: 'Failed',
    warning: 'Warning',
    info: 'Info',
    error: 'Error',
    activate: 'Activate',
    deactivate: 'Deactivate',
    lock: 'Lock',
    unlock: 'Unlock',
    batchOperation: 'Batch Operation',
    batchActivate: 'Batch Activate',
    batchDeactivate: 'Batch Deactivate',
    batchLock: 'Batch Lock',
    batchUnlock: 'Batch Unlock',
    batchDelete: 'Batch Delete',
    selectAll: 'Select All',
    selected: 'Selected',
    items: 'Items'
  },

  // System
  system: {
    title: 'TaxAgent Management System',
    welcome: 'Welcome',
    logout: 'Logout',
    profile: 'Profile',
    settings: 'Settings',
    language: 'Language',
    theme: 'Theme',
    fullscreen: 'Fullscreen',
    exitFullscreen: 'Exit Fullscreen'
  },

  // Login
  login: {
    title: 'User Login',
    username: 'Username',
    password: 'Password',
    loginBtn: 'Login',
    forgotPassword: 'Forgot Password?',
    rememberMe: 'Remember Me',
    loginSuccess: 'Login Successful',
    loginFailed: 'Login Failed',
    usernameRequired: 'Please enter username',
    passwordRequired: 'Please enter password',
    usernameLength: 'Username length should be 3 to 20 characters',
    passwordLength: 'Password length should be 6 to 20 characters'
  },

  // Menu
  menu: {
    dashboard: 'Dashboard',
    system: 'System Management',
    users: 'User Management',
    roles: 'Role Management',
    permissions: 'Permission Management',
    resources: 'Resource Management',
    onlineUsers: 'Online Users',
    logs: 'Log Management',
    settings: 'System Settings'
  },

  // User Management
  user: {
    title: 'User Management',
    username: 'Username',
    password: 'Password',
    fullName: 'Full Name',
    email: 'Email',
    phone: 'Phone',
    status: 'Status',
    roles: 'Roles',
    tenant: 'Tenant',
    lastLoginTime: 'Last Login Time',
    createUser: 'Create User',
    editUser: 'Edit User',
    deleteUser: 'Delete User',
    assignRoles: 'Assign Roles',
    resetPassword: 'Reset Password',
    batchDelete: 'Batch Delete',
    exportUsers: 'Export Users',
    importUsers: 'Import Users',
    usernamePlaceholder: 'Please enter username',
    passwordPlaceholder: 'Please enter password',
    fullNamePlaceholder: 'Please enter full name',
    emailPlaceholder: 'Please enter email',
    phonePlaceholder: 'Please enter phone',
    confirmPasswordPlaceholder: 'Please confirm password',
    selectRoles: 'Please select roles',
    selectTenant: 'Please select tenant',
    deleteConfirm: 'Are you sure to delete the selected users?',
    batchDeleteConfirm: 'Are you sure to batch delete the selected users?',
    resetPasswordConfirm: 'Are you sure to reset the password for this user?'
  },

  // Role Management
  role: {
    title: 'Role Management',
    roleName: 'Role Name',
    roleCode: 'Role Code',
    roleDesc: 'Role Description',
    permissions: 'Permissions',
    createRole: 'Create Role',
    editRole: 'Edit Role',
    deleteRole: 'Delete Role',
    assignPermissions: 'Assign Permissions',
    roleNamePlaceholder: 'Please enter role name',
    roleCodePlaceholder: 'Please enter role code',
    roleDescPlaceholder: 'Please enter role description',
    selectPermissions: 'Please select permissions',
    deleteConfirm: 'Are you sure to delete this role?',
    systemRole: 'System Role',
    customRole: 'Custom Role'
  },

  // Permission Management
  permission: {
    title: 'Permission Management',
    permissionName: 'Permission Name',
    permissionCode: 'Permission Code',
    permissionDesc: 'Permission Description',
    permissionType: 'Permission Type',
    parentPermission: 'Parent Permission',
    createPermission: 'Create Permission',
    editPermission: 'Edit Permission',
    deletePermission: 'Delete Permission',
    permissionNamePlaceholder: 'Please enter permission name',
    permissionCodePlaceholder: 'Please enter permission code',
    permissionDescPlaceholder: 'Please enter permission description',
    selectParentPermission: 'Please select parent permission',
    deleteConfirm: 'Are you sure to delete this permission?',
    menuPermission: 'Menu Permission',
    buttonPermission: 'Button Permission',
    apiPermission: 'API Permission'
  },

  // Resource Management
  resource: {
    title: 'Resource Management',
    resourceName: 'Resource Name',
    resourceType: 'Resource Type',
    resourcePath: 'Resource Path',
    resourceIcon: 'Resource Icon',
    parentResource: 'Parent Resource',
    sortOrder: 'Sort Order',
    createResource: 'Create Resource',
    editResource: 'Edit Resource',
    deleteResource: 'Delete Resource',
    resourceNamePlaceholder: 'Please enter resource name',
    resourcePathPlaceholder: 'Please enter resource path',
    selectParentResource: 'Please select parent resource',
    selectIcon: 'Select Icon',
    deleteConfirm: 'Are you sure to delete this resource?',
    menuResource: 'Menu',
    buttonResource: 'Button',
    apiResource: 'API'
  },

  // Online Users
  onlineUser: {
    title: 'Online Users',
    username: 'Username',
    fullName: 'Full Name',
    ipAddress: 'IP Address',
    location: 'Location',
    browser: 'Browser',
    os: 'Operating System',
    loginTime: 'Login Time',
    lastActiveTime: 'Last Active Time',
    kickOut: 'Kick Out',
    kickOutConfirm: 'Are you sure to kick out this user?',
    kickOutSuccess: 'User has been kicked out',
    refreshList: 'Refresh List'
  },

  // Theme
  theme: {
    light: 'Light',
    dark: 'Dark',
    auto: 'Follow System'
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome Back',
    totalUsers: 'Total Users',
    onlineUsers: 'Online Users',
    totalRoles: 'Total Roles',
    totalPermissions: 'Total Permissions',
    todayLogins: 'Today Logins',
    systemStatus: 'System Status',
    cpuUsage: 'CPU Usage',
    memoryUsage: 'Memory Usage',
    diskUsage: 'Disk Usage',
    networkTraffic: 'Network Traffic',
    recentActivities: 'Recent Activities',
    quickActions: 'Quick Actions',
    systemInfo: 'System Info',
    serverTime: 'Server Time',
    uptime: 'Uptime',
    version: 'Version'
  },

  // Messages
  message: {
    saveSuccess: 'Save successful',
    saveFailed: 'Save failed',
    deleteSuccess: 'Delete successful',
    deleteFailed: 'Delete failed',
    updateSuccess: 'Update successful',
    updateFailed: 'Update failed',
    createSuccess: 'Create successful',
    createFailed: 'Create failed',
    operationSuccess: 'Operation successful',
    operationFailed: 'Operation failed',
    networkError: 'Network error',
    serverError: 'Server error',
    permissionDenied: 'Permission denied',
    loginExpired: 'Login expired, please login again',
    confirmDelete: 'Are you sure to delete?',
    confirmOperation: 'Are you sure to perform this operation?',
    noPermission: 'You do not have permission to perform this operation',
    pleaseSelectData: 'Please select data to operate',
    dataNotFound: 'Data not found',
    parameterError: 'Parameter error'
  },

  // Form Validation
  validation: {
    required: 'This field is required',
    email: 'Please enter a valid email address',
    phone: 'Please enter a valid phone number',
    url: 'Please enter a valid URL',
    number: 'Please enter a valid number',
    integer: 'Please enter a valid integer',
    minLength: 'Length cannot be less than {min} characters',
    maxLength: 'Length cannot exceed {max} characters',
    min: 'Value cannot be less than {min}',
    max: 'Value cannot be greater than {max}',
    pattern: 'Format is incorrect',
    confirm: 'The two inputs are inconsistent'
  },

  // Pagination
  pagination: {
    total: 'Total {total} items',
    page: 'Page {current}',
    pageSize: '{size} items per page',
    goto: 'Go to',
    prev: 'Previous',
    next: 'Next',
    first: 'First',
    last: 'Last'
  },

  // Table
  table: {
    index: 'Index',
    actions: 'Actions',
    noData: 'No Data',
    loading: 'Loading...',
    selectAll: 'Select All',
    selected: '{count} items selected',
    expand: 'Expand',
    collapse: 'Collapse'
  },

  // Dialog
  dialog: {
    title: 'Notice',
    confirmTitle: 'Confirm Operation',
    deleteTitle: 'Delete Confirmation',
    close: 'Close',
    maximize: 'Maximize',
    restore: 'Restore'
  },

  // Date Time
  datetime: {
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week',
    lastWeek: 'Last Week',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    thisYear: 'This Year',
    lastYear: 'Last Year',
    selectDate: 'Select Date',
    selectTime: 'Select Time',
    selectDateTime: 'Select Date Time',
    startDate: 'Start Date',
    endDate: 'End Date',
    startTime: 'Start Time',
    endTime: 'End Time'
  },

  // File Upload
  upload: {
    selectFile: 'Select File',
    dragFile: 'Drop files here or click to upload',
    uploadSuccess: 'Upload successful',
    uploadFailed: 'Upload failed',
    fileSize: 'File size cannot exceed {size}',
    fileType: 'File type is incorrect',
    preview: 'Preview',
    download: 'Download',
    remove: 'Remove'
  },

  // Status
  status: {
    active: 'Active',
    inactive: 'Inactive',
    online: 'Online',
    offline: 'Offline',
    pending: 'Pending',
    processing: 'Processing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    expired: 'Expired',
    draft: 'Draft',
    published: 'Published'
  },

  // Action Buttons
  actions: {
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    add: 'Add',
    create: 'Create',
    update: 'Update',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    submit: 'Submit',
    reset: 'Reset',
    clear: 'Clear',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    import: 'Import',
    print: 'Print',
    copy: 'Copy',
    move: 'Move',
    sort: 'Sort',
    refresh: 'Refresh',
    reload: 'Reload',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    close: 'Close'
  }
}
