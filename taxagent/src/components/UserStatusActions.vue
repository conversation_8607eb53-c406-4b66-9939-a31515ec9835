<template>
  <div class="user-status-actions">
    <!-- 激活按钮 -->
    <el-button
      v-if="canActivateUser(user)"
      v-permission="permission"
      type="success"
      :size="size"
      :loading="loading.activate"
      @click="handleActivate"
    >
      <el-icon><Check /></el-icon>
      激活
    </el-button>

    <!-- 停用按钮 -->
    <el-button
      v-if="canDeactivateUser(user)"
      v-permission="permission"
      type="warning"
      :size="size"
      :loading="loading.deactivate"
      @click="handleDeactivate"
    >
      <el-icon><Close /></el-icon>
      停用
    </el-button>

    <!-- 锁定按钮 -->
    <el-button
      v-if="canLockUser(user)"
      v-permission="permission"
      type="danger"
      :size="size"
      :loading="loading.lock"
      @click="handleLock"
    >
      <el-icon><Lock /></el-icon>
      锁定
    </el-button>

    <!-- 解锁按钮 -->
    <el-button
      v-if="canUnlockUser(user)"
      v-permission="permission"
      type="info"
      :size="size"
      :loading="loading.unlock"
      @click="handleUnlock"
    >
      <el-icon><Unlock /></el-icon>
      解锁
    </el-button>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Check, Close, Lock, Unlock } from '@element-plus/icons-vue'
import { useUserStatus } from '@/composables/useUserStatus'
import { useTenantPermission } from '@/utils/tenant'

// 定义属性
const props = defineProps({
  // 用户数据
  user: {
    type: Object,
    required: true
  },
  // 按钮大小
  size: {
    type: String,
    default: 'small'
  },
  // 权限要求
  permission: {
    type: String,
    default: 'platform:user:manage'
  },
  // 是否显示确认对话框
  showConfirm: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['success', 'error'])

// 租户权限控制
const tenantPermission = useTenantPermission()

// 用户状态操作
const {
  canActivateUser,
  canDeactivateUser,
  canLockUser,
  canUnlockUser,
  activateUser,
  deactivateUser,
  lockUser,
  unlockUser
} = useUserStatus()

// 加载状态
const loading = reactive({
  activate: false,
  deactivate: false,
  lock: false,
  unlock: false
})

// 检查权限
const checkPermission = (user) => {
  return tenantPermission.canManageUser(user)
}

// 激活用户
const handleActivate = async () => {
  if (!checkPermission(props.user)) {
    return
  }

  loading.activate = true
  try {
    const success = await activateUser(props.user, {
      showConfirm: props.showConfirm,
      onSuccess: (user) => {
        emit('success', { action: 'activate', user })
      },
      onError: (error, user) => {
        emit('error', { action: 'activate', error, user })
      }
    })
  } finally {
    loading.activate = false
  }
}

// 停用用户
const handleDeactivate = async () => {
  if (!checkPermission(props.user)) {
    return
  }

  loading.deactivate = true
  try {
    const success = await deactivateUser(props.user, {
      showConfirm: props.showConfirm,
      onSuccess: (user) => {
        emit('success', { action: 'deactivate', user })
      },
      onError: (error, user) => {
        emit('error', { action: 'deactivate', error, user })
      }
    })
  } finally {
    loading.deactivate = false
  }
}

// 锁定用户
const handleLock = async () => {
  if (!checkPermission(props.user)) {
    return
  }

  loading.lock = true
  try {
    const success = await lockUser(props.user, {
      showConfirm: props.showConfirm,
      onSuccess: (user) => {
        emit('success', { action: 'lock', user })
      },
      onError: (error, user) => {
        emit('error', { action: 'lock', error, user })
      }
    })
  } finally {
    loading.lock = false
  }
}

// 解锁用户
const handleUnlock = async () => {
  if (!checkPermission(props.user)) {
    return
  }

  loading.unlock = true
  try {
    const success = await unlockUser(props.user, {
      showConfirm: props.showConfirm,
      onSuccess: (user) => {
        emit('success', { action: 'unlock', user })
      },
      onError: (error, user) => {
        emit('error', { action: 'unlock', error, user })
      }
    })
  } finally {
    loading.unlock = false
  }
}
</script>

<style lang="scss" scoped>
.user-status-actions {
  display: inline-flex;
  gap: 4px;
  
  .el-button {
    margin: 0;
  }
}
</style>
