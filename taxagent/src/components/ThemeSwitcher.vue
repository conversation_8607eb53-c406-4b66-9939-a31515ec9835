<template>
  <el-dropdown @command="handleThemeChange" trigger="click">
    <el-button :icon="themeIcon" circle />
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          v-for="theme in themes" 
          :key="theme.value"
          :command="theme.value"
          :class="{ 'is-active': currentTheme === theme.value }"
        >
          <el-icon>
            <component :is="theme.icon" />
          </el-icon>
          {{ theme.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useThemeStore } from '@/stores/theme'
import { Sunny, Moon, Monitor } from '@element-plus/icons-vue'

const { t } = useI18n()
const themeStore = useThemeStore()

// 当前主题
const currentTheme = computed(() => themeStore.currentTheme)

// 主题选项
const themes = computed(() => [
  {
    value: themeStore.THEMES.LIGHT,
    label: t('theme.light', '浅色'),
    icon: <PERSON>
  },
  {
    value: themeStore.THEMES.DARK,
    label: t('theme.dark', '深色'),
    icon: Moon
  },
  {
    value: themeStore.THEMES.AUTO,
    label: t('theme.auto', '跟随系统'),
    icon: Monitor
  }
])

// 当前主题图标
const themeIcon = computed(() => {
  const themeMap = {
    [themeStore.THEMES.LIGHT]: Sunny,
    [themeStore.THEMES.DARK]: Moon,
    [themeStore.THEMES.AUTO]: Monitor
  }
  return themeMap[currentTheme.value] || Sunny
})

// 处理主题切换
const handleThemeChange = (theme) => {
  themeStore.setTheme(theme)
}
</script>

<style lang="scss" scoped>
.el-dropdown-menu__item.is-active {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
</style>
