import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import Cookies from 'js-cookie'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(Cookies.get('token') || '')
  const userInfo = ref(null)
  const permissions = ref([])
  const roles = ref([])
  const menus = ref([])
  const buttons = ref([])

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!userInfo.value)
  
  // 检查是否有指定权限
  const hasPermission = computed(() => (permission) => {
    if (!permission) return true
    return permissions.value.includes(permission)
  })
  
  // 检查是否有指定角色
  const hasRole = computed(() => (role) => {
    if (!role) return true
    return roles.value.includes(role)
  })

  // 登录
  const login = async (loginForm) => {
    try {
      console.log('🚀 开始登录请求:', loginForm)
      const response = await authApi.login(loginForm)

      console.log('📥 登录响应:', response)

      // 处理不同的响应格式
      let loginData = response

      // 如果响应有 data 字段，使用 data
      if (response.data) {
        loginData = response.data
      }

      // 如果没有 token，可能是直接返回的用户信息
      if (!loginData.token && response.token) {
        loginData = response
      }

      console.log('📝 处理后的登录数据:', loginData)

      // 保存token
      if (loginData.token) {
        token.value = loginData.token
        Cookies.set('token', loginData.token, { expires: 7 })
        console.log('✅ Token 已保存:', loginData.token)
      } else {
        console.warn('⚠️ 响应中没有找到 token')
      }

      // 保存用户信息
      if (loginData.userId || loginData.username) {
        userInfo.value = {
          userId: loginData.userId,
          username: loginData.username,
          fullName: loginData.fullName || loginData.name,
          email: loginData.email,
          phone: loginData.phone,
          tenantId: loginData.tenantId,
          tenant: loginData.tenant
        }
        console.log('✅ 用户信息已保存:', userInfo.value)
      }

      // 保存权限信息
      roles.value = loginData.roles?.map(role => role.roleName || role) || []
      permissions.value = loginData.permissions?.map(perm => perm.permCode || perm) || []
      menus.value = loginData.menus || []
      buttons.value = loginData.buttons || []

      console.log('✅ 权限信息已保存:', {
        roles: roles.value,
        permissions: permissions.value
      })

      return response
    } catch (error) {
      console.error('❌ 登录失败:', error)
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      clearAuth()
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    token.value = ''
    userInfo.value = null
    permissions.value = []
    roles.value = []
    menus.value = []
    buttons.value = []
    Cookies.remove('token')
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) {
      clearAuth()
      return false
    }

    try {
      const response = await authApi.getCurrentUserPermissions()
      const { data } = response

      // 更新用户信息
      userInfo.value = data.user_info
      roles.value = data.roles || []
      permissions.value = data.permissions || []
      menus.value = data.menus || []
      buttons.value = data.buttons || []

      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)
      clearAuth()
      return false
    }
  }

  // 刷新用户权限
  const refreshPermissions = async () => {
    try {
      const response = await authApi.getCurrentUserPermissions()
      const { data } = response

      roles.value = data.roles || []
      permissions.value = data.permissions || []
      menus.value = data.menus || []
      buttons.value = data.buttons || []

      return data
    } catch (error) {
      console.error('刷新权限失败:', error)
      throw error
    }
  }



  return {
    // 状态
    token,
    userInfo,
    permissions,
    roles,
    menus,
    buttons,
    
    // 计算属性
    isAuthenticated,
    hasPermission,
    hasRole,
    
    // 方法
    login,
    logout,
    clearAuth,
    checkAuth,
    refreshPermissions
  }
})
