import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 主题类型
  const THEMES = {
    LIGHT: 'light',
    DARK: 'dark',
    AUTO: 'auto'
  }

  // 从localStorage恢复主题设置
  const getStoredTheme = () => {
    try {
      return localStorage.getItem('theme') || THEMES.LIGHT
    } catch {
      return THEMES.LIGHT
    }
  }

  // 当前主题
  const currentTheme = ref(getStoredTheme())

  // 检测系统主题
  const getSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? THEMES.DARK : THEMES.LIGHT
    }
    return THEMES.LIGHT
  }

  // 实际应用的主题（考虑auto模式）
  const actualTheme = ref(currentTheme.value === THEMES.AUTO ? getSystemTheme() : currentTheme.value)

  // 应用主题到DOM
  const applyTheme = (theme) => {
    const root = document.documentElement
    
    // 移除所有主题类
    root.classList.remove('theme-light', 'theme-dark')
    
    // 添加当前主题类
    root.classList.add(`theme-${theme}`)
    
    // 设置CSS变量
    if (theme === THEMES.DARK) {
      root.style.setProperty('--el-bg-color', '#1a1a1a')
      root.style.setProperty('--el-bg-color-page', '#0a0a0a')
      root.style.setProperty('--el-text-color-primary', '#e5eaf3')
      root.style.setProperty('--el-text-color-regular', '#cfd3dc')
      root.style.setProperty('--el-text-color-secondary', '#a3a6ad')
      root.style.setProperty('--el-text-color-placeholder', '#8d9095')
      root.style.setProperty('--el-border-color', '#4c4d4f')
      root.style.setProperty('--el-border-color-light', '#414243')
      root.style.setProperty('--el-border-color-lighter', '#363637')
      root.style.setProperty('--el-border-color-extra-light', '#2b2b2c')
      root.style.setProperty('--el-fill-color', '#303133')
      root.style.setProperty('--el-fill-color-light', '#262727')
      root.style.setProperty('--el-fill-color-lighter', '#1d1e1f')
      root.style.setProperty('--el-fill-color-extra-light', '#191a1b')
      root.style.setProperty('--el-fill-color-blank', '#141414')
      root.style.setProperty('--el-color-primary', '#409eff')
      root.style.setProperty('--el-color-success', '#67c23a')
      root.style.setProperty('--el-color-warning', '#e6a23c')
      root.style.setProperty('--el-color-danger', '#f56c6c')
      root.style.setProperty('--el-color-info', '#909399')
    } else {
      // 恢复默认的浅色主题变量
      root.style.removeProperty('--el-bg-color')
      root.style.removeProperty('--el-bg-color-page')
      root.style.removeProperty('--el-text-color-primary')
      root.style.removeProperty('--el-text-color-regular')
      root.style.removeProperty('--el-text-color-secondary')
      root.style.removeProperty('--el-text-color-placeholder')
      root.style.removeProperty('--el-border-color')
      root.style.removeProperty('--el-border-color-light')
      root.style.removeProperty('--el-border-color-lighter')
      root.style.removeProperty('--el-border-color-extra-light')
      root.style.removeProperty('--el-fill-color')
      root.style.removeProperty('--el-fill-color-light')
      root.style.removeProperty('--el-fill-color-lighter')
      root.style.removeProperty('--el-fill-color-extra-light')
      root.style.removeProperty('--el-fill-color-blank')
      root.style.removeProperty('--el-color-primary')
      root.style.removeProperty('--el-color-success')
      root.style.removeProperty('--el-color-warning')
      root.style.removeProperty('--el-color-danger')
      root.style.removeProperty('--el-color-info')
    }
  }

  // 设置主题
  const setTheme = (theme) => {
    if (!Object.values(THEMES).includes(theme)) {
      console.warn('Invalid theme:', theme)
      return
    }

    currentTheme.value = theme
    
    // 计算实际主题
    const newActualTheme = theme === THEMES.AUTO ? getSystemTheme() : theme
    actualTheme.value = newActualTheme
    
    // 应用主题
    applyTheme(newActualTheme)
    
    // 保存到localStorage
    try {
      localStorage.setItem('theme', theme)
    } catch (error) {
      console.warn('Failed to save theme to localStorage:', error)
    }
  }

  // 切换主题
  const toggleTheme = () => {
    const themes = [THEMES.LIGHT, THEMES.DARK, THEMES.AUTO]
    const currentIndex = themes.indexOf(currentTheme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }

  // 监听系统主题变化
  const setupSystemThemeListener = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      const handleSystemThemeChange = (e) => {
        if (currentTheme.value === THEMES.AUTO) {
          const newTheme = e.matches ? THEMES.DARK : THEMES.LIGHT
          actualTheme.value = newTheme
          applyTheme(newTheme)
        }
      }
      
      mediaQuery.addEventListener('change', handleSystemThemeChange)
      
      // 返回清理函数
      return () => {
        mediaQuery.removeEventListener('change', handleSystemThemeChange)
      }
    }
    
    return () => {}
  }

  // 初始化主题
  const initTheme = () => {
    applyTheme(actualTheme.value)
    return setupSystemThemeListener()
  }

  // 获取主题显示名称
  const getThemeDisplayName = (theme, t) => {
    const names = {
      [THEMES.LIGHT]: t ? t('theme.light') : '浅色',
      [THEMES.DARK]: t ? t('theme.dark') : '深色',
      [THEMES.AUTO]: t ? t('theme.auto') : '跟随系统'
    }
    return names[theme] || theme
  }

  // 判断是否为深色主题
  const isDark = () => {
    return actualTheme.value === THEMES.DARK
  }

  return {
    // 状态
    currentTheme,
    actualTheme,
    THEMES,
    
    // 方法
    setTheme,
    toggleTheme,
    initTheme,
    getThemeDisplayName,
    isDark
  }
})
