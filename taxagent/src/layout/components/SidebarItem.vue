<template>
  <!-- 有子菜单的情况 -->
  <el-sub-menu
    v-if="hasChildren && hasAccess"
    :index="route.path"
  >
    <template #title>
      <el-icon v-if="route.meta.icon">
        <component :is="route.meta.icon" />
      </el-icon>
      <span>{{ getDisplayTitle() }}</span>
    </template>

    <!-- 递归渲染子菜单 -->
    <SidebarItem
      v-for="child in route.children"
      :key="child.path"
      :route="child"
      :base-path="fullPath"
    />
  </el-sub-menu>

  <!-- 没有子菜单的情况 -->
  <el-menu-item
    v-else-if="hasAccess"
    :index="fullPath"
  >
    <el-icon v-if="route.meta.icon">
      <component :is="route.meta.icon" />
    </el-icon>
    <template #title>
      <span>{{ getDisplayTitle() }}</span>
    </template>
  </el-menu-item>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { usePermission } from '@/composables/usePermission'
import { getMenuTitle } from '@/utils/menu'

// 接收路由信息
const props = defineProps({
  route: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

const { hasPermission, hasRole } = usePermission()
const { t } = useI18n()

// 检查是否有子菜单
const hasChildren = computed(() => {
  return props.route.children && props.route.children.length > 0
})

// 路径解析函数
const resolvePath = (basePath, path) => {
  if (path.startsWith('/')) {
    return path
  }
  if (basePath) {
    return `${basePath}/${path}`
  }
  return `/${path}`
}

// 计算完整路径
const fullPath = computed(() => {
  return resolvePath(props.basePath, props.route.path)
})

// 获取显示标题
const getDisplayTitle = () => {
  // 优先使用国际化标题
  if (props.route.name) {
    const i18nTitle = getMenuTitle(props.route.name)
    if (i18nTitle !== props.route.name) {
      return i18nTitle
    }
  }

  // 回退到原始标题
  return props.route.meta?.title || props.route.name
}

// 检查单个路由的权限
const checkRoutePermission = (route) => {
  const { permission, role } = route.meta || {}

  // 如果没有设置权限要求，默认允许访问
  if (!permission && !role) {
    return true
  }

  // 检查权限
  if (permission && !hasPermission(permission)) {
    return false
  }

  // 检查角色
  if (role && !hasRole(role)) {
    return false
  }

  return true
}

// 检查是否有访问权限
const hasAccess = computed(() => {
  // 如果有子菜单，检查是否至少有一个子菜单有权限
  if (hasChildren.value) {
    return props.route.children.some(child => checkRoutePermission(child))
  }

  // 没有子菜单，检查当前路由权限
  return checkRoutePermission(props.route)
})
</script>

<style lang="scss" scoped>
// 样式已在父组件中定义
</style>
