<template>
  <el-breadcrumb separator="/">
    <el-breadcrumb-item
      v-for="(item, index) in breadcrumbList"
      :key="item.path"
      :to="index === breadcrumbList.length - 1 ? undefined : item.path"
    >
      <el-icon v-if="item.icon && index === 0">
        <component :is="item.icon" />
      </el-icon>
      {{ getDisplayTitle(item) }}
    </el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { getMenuTitle } from '@/utils/menu'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// 获取显示标题
const getDisplayTitle = (item) => {
  // 优先使用国际化标题
  if (item.name) {
    const i18nTitle = getMenuTitle(item.name)
    if (i18nTitle !== item.name) {
      return i18nTitle
    }
  }

  // 回退到原始标题
  return item.title
}

// 生成面包屑列表
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  const breadcrumbs = []
  
  // 添加首页
  if (route.path !== '/dashboard') {
    breadcrumbs.push({
      path: '/dashboard',
      title: t('menu.dashboard', '首页'),
      name: 'Dashboard',
      icon: 'House'
    })
  }
  
  // 添加当前路由的面包屑
  matched.forEach((item, index) => {
    // 跳过 Layout 路由
    if (item.name === 'Layout') return
    
    breadcrumbs.push({
      path: item.path,
      title: item.meta.title,
      name: item.name,
      icon: index === 0 ? item.meta.icon : undefined
    })
  })
  
  return breadcrumbs
})
</script>

<style lang="scss" scoped>
:deep(.el-breadcrumb) {
  .el-breadcrumb__item {
    .el-breadcrumb__inner {
      display: flex;
      align-items: center;
      gap: 4px;
      font-weight: normal;
      
      &.is-link {
        color: var(--el-text-color-regular);
        
        &:hover {
          color: var(--el-color-primary);
        }
      }
    }
    
    &:last-child {
      .el-breadcrumb__inner {
        color: var(--el-text-color-primary);
        font-weight: 500;
      }
    }
  }
}
</style>
