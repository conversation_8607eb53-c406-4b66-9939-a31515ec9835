<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <el-button
        type="text"
        size="large"
        @click="$emit('toggle-sidebar')"
      >
        <el-icon><Expand /></el-icon>
      </el-button>
    </div>
    
    <!-- 右侧区域 -->
    <div class="header-right">
      <!-- 主题切换器 -->
      <ThemeSwitcher class="theme-switcher" />

      <!-- 语言切换器 -->
      <LanguageSwitcher class="language-switcher" />

      <!-- 用户信息下拉菜单 -->
      <el-dropdown @command="handleCommand">
        <div class="user-info">
          <el-avatar :size="32" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <span class="username">{{ userDisplayName }}</span>
          <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              {{ t('system.profile') }}
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              {{ t('system.settings') }}
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              {{ t('system.logout') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Expand,
  User,
  ArrowDown,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { userUtils } from '@/utils/auth'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'

// 定义事件
const emit = defineEmits(['toggle-sidebar'])

const router = useRouter()
const authStore = useAuthStore()
const { t } = useI18n()

// 用户显示名称
const userDisplayName = computed(() => {
  return userUtils.formatUserDisplayName(authStore.userInfo)
})

// 用户头像
const userAvatar = computed(() => {
  return userUtils.getUserAvatar(authStore.userInfo)
})

// 处理下拉菜单命令
const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      // 跳转到个人信息页面
      ElMessage.info(t('message.operationSuccess'))
      break
    case 'settings':
      // 跳转到系统设置页面
      ElMessage.info(t('message.operationSuccess'))
      break
    case 'logout':
      await handleLogout()
      break
  }
}

// 处理退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      t('message.confirmOperation'),
      t('common.confirm'),
      {
        confirmButtonText: t('common.confirm'),
        cancelButtonText: t('common.cancel'),
        type: 'warning'
      }
    )
    
    await authStore.logout()
    ElMessage.success('退出登录成功')
    router.push('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-switcher {
  margin-right: 8px;
}

.language-switcher {
  margin-right: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: var(--el-fill-color-light);
  }
  
  .username {
    font-size: 14px;
    color: var(--el-text-color-primary);
  }
  
  .dropdown-icon {
    font-size: 12px;
    color: var(--el-text-color-regular);
    transition: transform 0.3s ease;
  }
}

:deep(.el-dropdown) {
  .el-dropdown__popper {
    .el-dropdown-menu__item {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}
</style>
