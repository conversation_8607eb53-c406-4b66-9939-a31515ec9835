import request from '@/utils/request'

export const permissionApi = {
  // 创建权限
  createPermission(data) {
    return request({
      url: '/v1/sys/permissions',
      method: 'post',
      data
    })
  },

  // 更新权限
  updatePermission(permId, data) {
    return request({
      url: `/v1/sys/permissions/${permId}`,
      method: 'put',
      data
    })
  },

  // 删除权限
  deletePermission(permId) {
    return request({
      url: `/v1/sys/permissions/${permId}`,
      method: 'delete'
    })
  },

  // 根据ID获取权限
  getPermissionById(permId) {
    return request({
      url: `/v1/sys/permissions/${permId}`,
      method: 'get'
    })
  },

  // 获取所有权限
  getAllPermissions() {
    return request({
      url: '/v1/sys/permissions',
      method: 'get'
    })
  },

  // 根据权限编码获取权限
  getPermissionByCode(permCode) {
    return request({
      url: `/v1/sys/permissions/code/${permCode}`,
      method: 'get'
    })
  },

  // 获取权限关联的资源
  getPermissionResources(permId) {
    return request({
      url: `/v1/sys/permissions/${permId}/resources`,
      method: 'get'
    })
  },

  // 权限关联资源
  associateResources(permId, data) {
    return request({
      url: `/v1/sys/permissions/${permId}/resources`,
      method: 'post',
      data
    })
  }
}
