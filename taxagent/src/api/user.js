import request from '@/utils/request'

export const userApi = {
  // 创建用户
  createUser(data) {
    return request({
      url: '/v1/sys/users',
      method: 'post',
      data
    })
  },

  // 获取所有用户
  getAllUsers() {
    return request({
      url: '/v1/sys/users',
      method: 'get'
    })
  },

  // 分页查询用户列表
  getUserPage(params) {
    return request({
      url: '/v1/sys/users/page',
      method: 'get',
      params
    })
  },

  // 根据ID获取用户
  getUserById(userId) {
    return request({
      url: `/v1/sys/users/${userId}`,
      method: 'get'
    })
  },

  // 更新用户信息
  updateUser(userId, data) {
    return request({
      url: `/v1/sys/users/${userId}`,
      method: 'put',
      data
    })
  },

  // 删除用户
  deleteUser(userId) {
    return request({
      url: `/v1/sys/users/${userId}`,
      method: 'delete'
    })
  },

  // 为用户分配角色
  assignRoles(userId, roleIds) {
    return request({
      url: `/v1/sys/users/${userId}/roles`,
      method: 'post',
      data: roleIds
    })
  },

  // 激活用户
  activateUser(userId) {
    return request({
      url: `/v1/sys/users/${userId}/activate`,
      method: 'post'
    })
  },

  // 停用用户
  deactivateUser(userId) {
    return request({
      url: `/v1/sys/users/${userId}/deactivate`,
      method: 'post'
    })
  },

  // 锁定用户
  lockUser(userId) {
    return request({
      url: `/v1/sys/users/${userId}/lock`,
      method: 'post'
    })
  },

  // 解锁用户
  unlockUser(userId) {
    return request({
      url: `/v1/sys/users/${userId}/unlock`,
      method: 'post'
    })
  }
}
