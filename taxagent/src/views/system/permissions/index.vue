<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">权限管理</h1>
    </div>
    
    <div class="page-content">
      <!-- 搜索表单 -->
      <el-card class="search-card">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          class="search-form"
          :inline="true"
        >
          <el-form-item label="权限名称" prop="permName">
            <el-input
              v-model="searchForm.permName"
              placeholder="请输入权限名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="权限编码" prop="permCode">
            <el-input
              v-model="searchForm.permCode"
              placeholder="请输入权限编码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 数据表格 -->
      <el-card class="table-card">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button
              v-permission="'platform:role:manage'"
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增权限
            </el-button>
            <el-button
              v-permission="'platform:role:manage'"
              type="danger"
              :disabled="!selectedRows.length"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <!-- 表格 -->
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="filteredTableData"
          @selection-change="handleSelectionChange"
          stripe
          border
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="permId" label="权限ID" width="80" />
          
          <el-table-column prop="permName" label="权限名称" min-width="150" />
          
          <el-table-column prop="permCode" label="权限编码" min-width="200">
            <template #default="{ row }">
              <el-tag size="small">{{ row.permCode }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="description" label="权限描述" min-width="200" />
          
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.updateTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button
                v-permission="'platform:role:manage'"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-permission="'platform:role:manage'"
                type="danger"
                size="small"
                :disabled="isSystemPermission(row)"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 权限表单对话框 -->
    <PermissionForm
      v-model="formVisible"
      :form-data="formData"
      :form-type="formType"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete } from '@element-plus/icons-vue'
import { permissionApi } from '@/api'
import { dateUtils } from '@/utils/common'
import PermissionForm from './components/PermissionForm.vue'

// 表格引用
const tableRef = ref()
const searchFormRef = ref()

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  permName: '',
  permCode: ''
})

// 表单对话框
const formVisible = ref(false)
const formType = ref('add') // add | edit
const formData = ref({})

// 系统权限列表（不允许删除）
const systemPermissions = [
  'platform:user:manage',
  'platform:role:manage',
  'platform:resource:manage'
]

// 过滤后的表格数据
const filteredTableData = computed(() => {
  let data = tableData.value
  
  if (searchForm.permName) {
    data = data.filter(item => 
      item.permName.toLowerCase().includes(searchForm.permName.toLowerCase())
    )
  }
  
  if (searchForm.permCode) {
    data = data.filter(item => 
      item.permCode.toLowerCase().includes(searchForm.permCode.toLowerCase())
    )
  }
  
  return data
})

// 格式化日期时间
const formatDateTime = (date) => {
  return dateUtils.formatDateTime(date)
}

// 判断是否为系统权限
const isSystemPermission = (permission) => {
  return systemPermissions.includes(permission.permCode)
}

// 加载权限列表
const loadPermissionList = async () => {
  try {
    loading.value = true
    const response = await permissionApi.getAllPermissions()
    tableData.value = response.data || []
  } catch (error) {
    console.error('加载权限列表失败:', error)
    ElMessage.error('加载权限列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  // 由于使用了计算属性进行过滤，这里不需要额外操作
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
}

// 刷新
const handleRefresh = () => {
  loadPermissionList()
}

// 新增权限
const handleAdd = () => {
  formType.value = 'add'
  formData.value = {}
  formVisible.value = true
}

// 编辑权限
const handleEdit = (row) => {
  formType.value = 'edit'
  formData.value = { ...row }
  formVisible.value = true
}

// 删除权限
const handleDelete = async (row) => {
  if (isSystemPermission(row)) {
    ElMessage.warning('系统权限不允许删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除权限 "${row.permName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await permissionApi.deletePermission(row.permId)
    ElMessage.success('删除成功')
    loadPermissionList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除权限失败:', error)
      ElMessage.error('删除权限失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  const systemPermissionRows = selectedRows.value.filter(row => isSystemPermission(row))
  if (systemPermissionRows.length > 0) {
    ElMessage.warning('选中的权限中包含系统权限，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个权限吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const promises = selectedRows.value.map(row => permissionApi.deletePermission(row.permId))
    await Promise.all(promises)
    
    ElMessage.success('批量删除成功')
    loadPermissionList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 表单提交成功
const handleFormSuccess = () => {
  formVisible.value = false
  loadPermissionList()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPermissionList()
})
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 20px;
}

.table-card {
  .toolbar {
    margin-bottom: 16px;
  }
}
</style>
