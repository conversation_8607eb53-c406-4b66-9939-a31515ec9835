<template>
  <el-dialog
    v-model="visible"
    title="关联资源"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="permission-info">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="权限名称">{{ permissionData.permName }}</el-descriptions-item>
        <el-descriptions-item label="权限编码">{{ permissionData.permCode }}</el-descriptions-item>
        <el-descriptions-item label="权限描述">{{ permissionData.description }}</el-descriptions-item>
      </el-descriptions>
    </div>
    
    <div class="resource-selection">
      <h4>选择关联的资源</h4>
      <div class="resource-tree" v-loading="loading">
        <el-tree
          ref="treeRef"
          :data="resourceTree"
          :props="treeProps"
          show-checkbox
          node-key="resourceId"
          :default-checked-keys="selectedResources"
          :check-strictly="true"
          :default-expand-all="true"
          @check="handleTreeCheck"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="node-label">{{ data.resourceName }}</span>
              <span class="node-code">{{ data.resourceCode }}</span>
              <span class="node-type">{{ getResourceTypeLabel(data.resourceType) }}</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { permissionApi, resourceApi } from '@/api'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  permissionData: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 树形组件引用
const treeRef = ref()

// 对话框显示状态
const visible = ref(false)

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 资源列表和树形数据
const resourceList = ref([])
const resourceTree = ref([])

// 选中的资源
const selectedResources = ref([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'resourceName'
}

// 资源类型标签映射
const getResourceTypeLabel = (type) => {
  const typeMap = {
    'menu': '菜单',
    'button': '按钮',
    'api': '接口',
    'page': '页面'
  }
  return typeMap[type] || type
}

// 监听对话框显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    await loadResourceTree()
    await initSelectedResources()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 转换数据结构，将 children 和 buttons 合并
const transformTreeData = (nodes) => {
  if (!Array.isArray(nodes)) return []
  
  return nodes.map(node => {
    const transformedNode = { ...node }
    
    const children = node.children || []
    
    if (children.length > 0) {
      transformedNode.children = [
        ...transformTreeData(children),
      ]
    }
    
    return transformedNode
  })
}

// 加载资源树
const loadResourceTree = async () => {
  try {
    loading.value = true
    const response = await resourceApi.getResourceTree()
    
    console.log('原始资源树数据:', response.data)
    
    resourceTree.value = transformTreeData(response.data || [])
    
    console.log('转换后资源树数据:', resourceTree.value)
    console.log('资源节点总数:', countTreeNodes(resourceTree.value))

    await nextTick()
  } catch (error) {
    console.error('加载资源树失败:', error)
    ElMessage.error('加载资源树失败')
  } finally {
    loading.value = false
  }
}

// 添加节点计数函数
const countTreeNodes = (nodes) => {
  if (!Array.isArray(nodes)) return 0
  let count = nodes.length
  nodes.forEach(node => {
    if (node.children) {
      count += countTreeNodes(node.children)
    }
  })
  return count
}

// 初始化选中的资源
const initSelectedResources = async () => {
  if (!props.permissionData || !props.permissionData.permId) {
    selectedResources.value = []
    return
  }

  try {
    const response = await permissionApi.getPermissionResources(props.permissionData.permId)
    const permissionResources = response.data || []

    console.log('权限关联资源原始数据:', permissionResources)
    console.log('权限关联资源数量:', permissionResources.length)

    // 提取资源ID并去重
    const resourceIds = [...new Set(permissionResources.map(resource =>
      typeof resource === 'string' ? resource : resource.resourceId
    ))]

    console.log('去重后资源ID:', resourceIds)
    console.log('去重后资源数量:', resourceIds.length)

    selectedResources.value = resourceIds

    const setCheckedKeys = async (retries = 3) => {
      await nextTick()
      if (treeRef.value && resourceTree.value.length > 0) {
        try {
          treeRef.value.setCheckedKeys(resourceIds)
          console.log('已设置权限资源选中状态:', resourceIds)
          
          // 验证设置结果
          const actualChecked = treeRef.value.getCheckedKeys()
          const actualHalfChecked = treeRef.value.getHalfCheckedKeys()
          console.log('实际选中的节点:', actualChecked)
          console.log('实际半选中的节点:', actualHalfChecked)
          console.log('总选中节点数:', actualChecked.length + actualHalfChecked.length)
        } catch (error) {
          console.warn('设置资源选中状态失败:', error)
          if (retries > 0) {
            setTimeout(() => setCheckedKeys(retries - 1), 100)
          }
        }
      } else if (retries > 0) {
        setTimeout(() => setCheckedKeys(retries - 1), 100)
      }
    }

    await setCheckedKeys()

  } catch (error) {
    console.error('获取权限关联资源失败:', error)
    ElMessage.error('获取权限关联资源失败')
    selectedResources.value = []
  }
}

// 树形选择变化
const handleTreeCheck = (data, checked) => {
  // 获取所有选中的资源ID（包括半选中状态）
  const checkedKeys = treeRef.value.getCheckedKeys()
  const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
  selectedResources.value = [...checkedKeys, ...halfCheckedKeys]
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交资源关联
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 获取所有选中的资源ID（包括父节点和子节点）
    const checkedKeys = treeRef.value.getCheckedKeys()
    const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
    const allSelectedKeys = [...checkedKeys, ...halfCheckedKeys]
    
    await permissionApi.associateResources(props.permissionData.permId, {
      permId: props.permissionData.permId,
      resourceIds: allSelectedKeys
    })
    
    ElMessage.success('关联资源成功')
    emit('success')
  } catch (error) {
    console.error('关联资源失败:', error)
    ElMessage.error('关联资源失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.permission-info {
  margin-bottom: 20px;
}

.resource-selection {
  h4 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }
  
  .resource-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 8px;
    
    .tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      .node-label {
        flex: 1;
      }
      
      .node-code {
        font-size: 12px;
        color: var(--el-text-color-regular);
        background: var(--el-fill-color-light);
        padding: 2px 6px;
        border-radius: 2px;
        margin-right: 8px;
      }
      
      .node-type {
        font-size: 11px;
        color: var(--el-text-color-placeholder);
        background: var(--el-color-info-light-8);
        padding: 1px 4px;
        border-radius: 2px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>