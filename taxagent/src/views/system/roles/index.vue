<template>
  <div class="page-container">
    <!-- <div class="page-header">
      <h1 class="page-title">角色管理</h1>
    </div> -->
    
    <div class="page-content">
      <!-- 数据表格 -->
      <el-card class="table-card">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button
              v-permission="'platform:role:manage'"
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              新增角色
            </el-button>
            <el-button
              v-permission="'platform:role:manage'"
              type="danger"
              :disabled="!selectedRows.length"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              批量删除
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <!-- 表格 -->
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          stripe
          border
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="roleId" label="角色ID" width="80" />
          
          <el-table-column prop="roleName" label="角色名称" min-width="150" />
          
          <el-table-column prop="description" label="角色描述" min-width="200" />
          
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.updateTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="300" fixed="right">
            <template #default="{ row }">
              <el-button
                v-permission="'platform:role:manage'"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-permission="'platform:role:manage'"
                type="warning"
                size="small"
                @click="handleAssignPermissions(row)"
              >
                分配权限
              </el-button>
              <el-button
                v-permission="'platform:role:manage'"
                type="danger"
                size="small"
                :disabled="isSystemRole(row)"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 角色表单对话框 -->
    <RoleForm
      v-model="formVisible"
      :form-data="formData"
      :form-type="formType"
      @success="handleFormSuccess"
    />
    
    <!-- 权限分配对话框 -->
    <PermissionAssignDialog
      v-model="permissionAssignVisible"
      :role-data="currentRole"
      @success="handlePermissionAssignSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Refresh } from '@element-plus/icons-vue'
import { roleApi } from '@/api'
import { dateUtils } from '@/utils/common'
import RoleForm from './components/RoleForm.vue'
import PermissionAssignDialog from './components/PermissionAssignDialog.vue'

// 表格引用
const tableRef = ref()

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 表单对话框
const formVisible = ref(false)
const formType = ref('add') // add | edit
const formData = ref({})

// 权限分配对话框
const permissionAssignVisible = ref(false)
const currentRole = ref({})

// 系统角色列表（不允许删除）
const systemRoles = ['super_admin', 'admin']

// 格式化日期时间
const formatDateTime = (date) => {
  return dateUtils.formatDateTime(date)
}

// 判断是否为系统角色
const isSystemRole = (role) => {
  return systemRoles.includes(role.roleName)
}

// 加载角色列表
const loadRoleList = async () => {
  try {
    loading.value = true
    const response = await roleApi.getAllRoles()
    tableData.value = response.data || []
  } catch (error) {
    console.error('加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新
const handleRefresh = () => {
  loadRoleList()
}

// 新增角色
const handleAdd = () => {
  formType.value = 'add'
  formData.value = {}
  formVisible.value = true
}

// 编辑角色
const handleEdit = (row) => {
  formType.value = 'edit'
  formData.value = { ...row }
  formVisible.value = true
}

// 删除角色
const handleDelete = async (row) => {
  if (isSystemRole(row)) {
    ElMessage.warning('系统角色不允许删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除角色 "${row.roleName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await roleApi.deleteRole(row.roleId)
    ElMessage.success('删除成功')
    loadRoleList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除角色失败:', error)
      ElMessage.error('删除角色失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  const systemRoleRows = selectedRows.value.filter(row => isSystemRole(row))
  if (systemRoleRows.length > 0) {
    ElMessage.warning('选中的角色中包含系统角色，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个角色吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const promises = selectedRows.value.map(row => roleApi.deleteRole(row.roleId))
    await Promise.all(promises)
    
    ElMessage.success('批量删除成功')
    loadRoleList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 分配权限
const handleAssignPermissions = (row) => {
  currentRole.value = { ...row }
  permissionAssignVisible.value = true
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 表单提交成功
const handleFormSuccess = () => {
  formVisible.value = false
  loadRoleList()
}

// 权限分配成功
const handlePermissionAssignSuccess = () => {
  permissionAssignVisible.value = false
  loadRoleList()
}

// 组件挂载时加载数据
onMounted(() => {
  loadRoleList()
})
</script>

<style lang="scss" scoped>
.table-card {
  .toolbar {
    margin-bottom: 16px;
  }
}
</style>
