<template>
  <div class="page-container">
    <!-- <div class="page-header">
      <h1 class="page-title">{{ t('onlineUser.title', '在线用户管理') }}</h1>
    </div> -->
    
    <div class="page-content">
      <!-- 统计信息 -->
      <el-card class="stats-card">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ onlineUsers.length }}</div>
              <div class="stat-label">在线用户总数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ adminCount }}</div>
              <div class="stat-label">管理员在线</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ pcCount }}</div>
              <div class="stat-label">PC端用户</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ mobileCount }}</div>
              <div class="stat-label">移动端用户</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 数据表格 -->
      <el-card class="table-card">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button 
              type="danger" 
              :disabled="!selectedRows.length"
              @click="handleBatchForceOffline"
            >
              <el-icon><SwitchButton /></el-icon>
              批量强制下线
            </el-button>
          </div>
          
          <div class="toolbar-right">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <!-- 表格 -->
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="onlineUsers"
          @selection-change="handleSelectionChange"
          stripe
          border
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="userId" label="用户ID" width="80" />
          
          <el-table-column prop="username" label="用户名" min-width="120" />
          
          <el-table-column label="角色" min-width="120">
            <template #default="{ row }">
              <el-tag
                v-for="role in (row.roles || '').split(',').filter(r => r.trim())"
                :key="role"
                size="small"
                class="role-tag"
              >
                {{ role.trim() }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="ipAddress" label="IP地址" width="140" />
          
          <el-table-column prop="loginTime" label="登录时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.loginTime) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="lastActiveTime" label="最后活跃" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.lastActiveTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="在线时长" width="120">
            <template #default="{ row }">
              {{ formatDuration(row.sessionDuration) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button
                type="danger"
                size="small"
                @click="handleForceOffline(row)"
              >
                强制下线
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { SwitchButton, Refresh } from '@element-plus/icons-vue'
import { onlineUserApi } from '@/api'
import { dateUtils } from '@/utils/common'

const { t } = useI18n()

// 表格引用
const tableRef = ref()

// 加载状态
const loading = ref(false)

// 在线用户列表
const onlineUsers = ref([])
const selectedRows = ref([])

// 定时器
let refreshTimer = null

// 统计数据
const adminCount = computed(() => {
  return onlineUsers.value.filter(user => 
    user.roles.includes('super_admin') || user.roles.includes('admin')
  ).length
})

const pcCount = computed(() => {
  return onlineUsers.value.filter(user => user.deviceType === 'PC').length
})

const mobileCount = computed(() => {
  return onlineUsers.value.filter(user => user.deviceType === 'Mobile').length
})

// 格式化日期时间
const formatDateTime = (date) => {
  return dateUtils.formatDateTime(date)
}

// 格式化持续时间
const formatDuration = (seconds) => {
  return dateUtils.formatDuration(seconds)
}

// 获取设备类型颜色
const getDeviceTypeColor = (deviceType) => {
  switch (deviceType) {
    case 'PC':
      return 'primary'
    case 'Mobile':
      return 'success'
    default:
      return 'info'
  }
}

// 加载在线用户列表
const loadOnlineUsers = async () => {
  try {
    loading.value = true
    const response = await onlineUserApi.getOnlineUsers()
    onlineUsers.value = response.data || []
  } catch (error) {
    console.error('加载在线用户列表失败:', error)
    ElMessage.error('加载在线用户列表失败')
  } finally {
    loading.value = false
  }
}

// 刷新
const handleRefresh = () => {
  loadOnlineUsers()
}

// 强制用户下线
const handleForceOffline = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要强制用户 "${row.username}" 下线吗？`,
      '强制下线确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await onlineUserApi.forceUserOffline(row.userId)
    ElMessage.success('强制下线成功')
    loadOnlineUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('强制下线失败:', error)
      ElMessage.error('强制下线失败')
    }
  }
}

// 批量强制下线
const handleBatchForceOffline = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要强制选中的 ${selectedRows.value.length} 个用户下线吗？`,
      '批量强制下线确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const userIds = selectedRows.value.map(row => row.userId)
    await onlineUserApi.batchForceUserOffline(userIds)
    
    ElMessage.success('批量强制下线成功')
    loadOnlineUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量强制下线失败:', error)
      ElMessage.error('批量强制下线失败')
    }
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 开始自动刷新
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadOnlineUsers()
  }, 30000) // 每30秒刷新一次
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadOnlineUsers()
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style lang="scss" scoped>
.stats-card {
  margin-bottom: 20px;
  
  .stat-item {
    text-align: center;
    
    .stat-value {
      font-size: 32px;
      font-weight: 600;
      color: var(--el-color-primary);
      line-height: 1;
      margin-bottom: 8px;
    }
    
    .stat-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
    }
  }
}

.table-card {
  .toolbar {
    margin-bottom: 16px;
  }
}

.role-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
