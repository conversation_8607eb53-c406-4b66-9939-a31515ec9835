<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">{{ t('user.title', '用户管理') }}</h1>
    </div>
    
    <div class="page-content">
      <!-- 搜索表单 -->
      <el-card class="search-card">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          class="search-form"
          :inline="true"
        >
          <el-form-item :label="t('user.username', '用户名')" prop="username">
            <el-input
              v-model="searchForm.username"
              :placeholder="t('user.usernamePlaceholder', '请输入用户名')"
              clearable
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item :label="t('user.email', '邮箱')" prop="email">
            <el-input
              v-model="searchForm.email"
              :placeholder="t('user.emailPlaceholder', '请输入邮箱')"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="正常" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              {{ t('actions.search', '搜索') }}
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              {{ t('actions.reset', '重置') }}
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
      
      <!-- 数据表格 -->
      <el-card class="table-card">
        <!-- 工具栏 -->
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button
              v-permission="'platform:user:create'"
              type="primary"
              @click="handleAdd"
            >
              <el-icon><Plus /></el-icon>
              {{ t('user.createUser', '新增用户') }}
            </el-button>
            <el-button
              v-permission="'platform:user:delete'"
              type="danger"
              :disabled="!selectedRows.length"
              @click="handleBatchDelete"
            >
              <el-icon><Delete /></el-icon>
              {{ t('user.batchDelete', '批量删除') }}
            </el-button>
          </div>

          <div class="toolbar-right">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
        
        <!-- 表格 -->
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableData"
          @selection-change="handleSelectionChange"
          stripe
          border
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="userId" label="用户ID" width="80" />
          
          <el-table-column prop="username" label="用户名" min-width="120" />
          
          <el-table-column prop="fullName" label="姓名" min-width="120" />
          
          <el-table-column prop="email" label="邮箱" min-width="180" />
          
          <el-table-column prop="phone" label="电话" min-width="120" />
          
          <el-table-column label="状态" width="80">
            <template #default="{ row }">
              <el-tag
                :type="getUserStatusType(row)"
                size="small"
              >
                {{ getUserStatusText(row) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="角色" min-width="150">
            <template #default="{ row }">
              <el-tag
                v-for="role in row.roles"
                :key="role.roleId"
                size="small"
                class="role-tag"
              >
                {{ role.roleName }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="250" fixed="right">
            <template #default="{ row }">
              <el-button
                v-permission="'platform:user:update'"
                type="primary"
                size="small"
                @click="handleEdit(row)"
              >
                编辑
              </el-button>
              <el-button
                v-permission="'platform:role:assign'"
                type="warning"
                size="small"
                @click="handleAssignRoles(row)"
              >
                分配角色
              </el-button>
              <el-button
                v-permission="'platform:user:delete'"
                type="danger"
                size="small"
                @click="handleDelete(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.pageNum"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 用户表单对话框 -->
    <UserForm
      v-model="formVisible"
      :form-data="formData"
      :form-type="formType"
      @success="handleFormSuccess"
    />
    
    <!-- 角色分配对话框 -->
    <RoleAssignDialog
      v-model="roleAssignVisible"
      :user-data="currentUser"
      @success="handleRoleAssignSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete } from '@element-plus/icons-vue'
import { userApi } from '@/api'
import { dateUtils } from '@/utils/common'
import { userUtils } from '@/utils/auth'
import { useTenantPermission } from '@/utils/tenant'
import UserForm from './components/UserForm.vue'
import RoleAssignDialog from './components/RoleAssignDialog.vue'

const { t } = useI18n()

// 表格引用
const tableRef = ref()
const searchFormRef = ref()

// 租户权限控制
const tenantPermission = useTenantPermission()

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 搜索表单
const searchForm = reactive({
  username: '',
  email: '',
  status: null
})

// 分页信息
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

// 表单对话框
const formVisible = ref(false)
const formType = ref('add') // add | edit
const formData = ref({})

// 角色分配对话框
const roleAssignVisible = ref(false)
const currentUser = ref({})

// 格式化日期时间
const formatDateTime = (date) => {
  return dateUtils.formatDateTime(date)
}

// 获取用户状态类型
const getUserStatusType = (user) => {
  if (user.isLocked === 1) return 'danger'
  if (user.isActivated === 0) return 'warning'
  return 'success'
}

// 获取用户状态文本
const getUserStatusText = (user) => {
  return userUtils.formatUserStatus(user)
}

// 加载用户列表
const loadUserList = async () => {
  try {
    loading.value = true

    const params = {
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
      ...searchForm,
      // 添加租户过滤条件
      ...tenantPermission.getTenantFilter()
    }

    // 过滤空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null) {
        delete params[key]
      }
    })

    const response = await userApi.getUserPage(params)
    const { data } = response

    // 根据租户权限过滤用户列表
    let userList = data.records || []
    userList = tenantPermission.filterUserList(userList)

    tableData.value = userList
    pagination.total = data.total || 0
    pagination.pageNum = data.pageNum || 1
    pagination.pageSize = data.pageSize || 10
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageNum = 1
  loadUserList()
}

// 重置搜索
const handleReset = () => {
  searchFormRef.value?.resetFields()
  pagination.pageNum = 1
  loadUserList()
}

// 刷新
const handleRefresh = () => {
  loadUserList()
}

// 新增用户
const handleAdd = () => {
  formType.value = 'add'
  formData.value = {}
  formVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  formType.value = 'edit'
  formData.value = { ...row }
  formVisible.value = true
}

// 删除用户
const handleDelete = async (row) => {
  // 检查租户权限
  if (!tenantPermission.canManageUser(row)) {
    ElMessage.warning('您没有权限删除该用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${row.username}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await userApi.deleteUser(row.userId)
    ElMessage.success('删除成功')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  // 检查租户权限
  const unauthorizedUsers = selectedRows.value.filter(row => !tenantPermission.canManageUser(row))
  if (unauthorizedUsers.length > 0) {
    ElMessage.warning('选中的用户中包含您无权限管理的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个用户吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const promises = selectedRows.value.map(row => userApi.deleteUser(row.userId))
    await Promise.all(promises)

    ElMessage.success('批量删除成功')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 分配角色
const handleAssignRoles = (row) => {
  // 检查租户权限
  if (!tenantPermission.canManageUser(row)) {
    ElMessage.warning('您没有权限为该用户分配角色')
    return
  }

  currentUser.value = { ...row }
  roleAssignVisible.value = true
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.pageSize = size
  pagination.pageNum = 1
  loadUserList()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.pageNum = page
  loadUserList()
}

// 表单提交成功
const handleFormSuccess = () => {
  formVisible.value = false
  loadUserList()
}

// 角色分配成功
const handleRoleAssignSuccess = () => {
  roleAssignVisible.value = false
  loadUserList()
}

// 组件挂载时加载数据
onMounted(() => {
  loadUserList()
})
</script>

<style lang="scss" scoped>
.search-card {
  margin-bottom: 20px;
}

.table-card {
  .toolbar {
    margin-bottom: 16px;
  }
}

.role-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
