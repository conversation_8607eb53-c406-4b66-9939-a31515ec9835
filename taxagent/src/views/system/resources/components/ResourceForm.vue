<template>
  <el-dialog
    v-model="visible"
    :title="getDialogTitle()"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="父级资源名称" v-if="parentResource">
        <el-input
          :value="parentResource.resourceName"
          disabled
        />
      </el-form-item>
      <el-form-item label="父级资源编码" v-if="parentResource">
        <el-input
          :value="parentResource.resourceCode"
          disabled
        />
      </el-form-item>
      
      <el-form-item label="资源名称" prop="resourceName">
        <el-input
          v-model="form.resourceName"
          placeholder="请输入资源名称"
        />
      </el-form-item>
      
      <el-form-item label="资源编码" prop="resourceCode">
        <el-input
          v-model="form.resourceCode"
          placeholder="请输入资源编码"
          :disabled="formType === 'edit'"
        />
      </el-form-item>
      
      <el-form-item label="资源类型" prop="resourceType">
        <el-select
          v-model="form.resourceType"
          placeholder="请选择资源类型"
          style="width: 100%"
        >
          <el-option label="菜单" value="menu" />
          <el-option label="按钮" value="button" />
          <el-option label="接口" value="api" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="路径" prop="path" v-if="form.resourceType === 'menu'">
        <el-input
          v-model="form.path"
          placeholder="请输入路径，如：/system/users"
        />
      </el-form-item>
      
      <el-form-item label="图标" prop="icon" v-if="form.resourceType === 'menu'">
        <el-select
          v-model="form.icon"
          placeholder="请选择图标"
          style="width: 100%"
          filterable
        >
          <el-option
            v-for="icon in iconOptions"
            :key="icon.value"
            :label="icon.label"
            :value="icon.value"
          >
            <div class="icon-option">
              <el-icon><component :is="icon.value" /></el-icon>
              <span>{{ icon.label }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          v-model="form.sortOrder"
          :min="0"
          :max="9999"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="是否可见" prop="isVisible">
        <el-radio-group v-model="form.isVisible">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入资源描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { resourceApi } from '@/api'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  formType: {
    type: String,
    default: 'add' // add | edit | addChild
  },
  parentResource: {
    type: Object,
    default: null
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const formRef = ref()

// 对话框显示状态
const visible = ref(false)

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive({
  resourceName: '',
  resourceCode: '',
  resourceType: 'menu',
  path: '',
  icon: '',
  sortOrder: 0,
  isVisible: true,
  description: '',
  parentId: null
})

// 图标选项
const iconOptions = [
  { label: '首页', value: 'Dashboard' },
  { label: '用户', value: 'User' },
  { label: '用户组', value: 'UserFilled' },
  { label: '角色', value: 'Avatar' },
  { label: '权限', value: 'Key' },
  { label: '菜单', value: 'Menu' },
  { label: '设置', value: 'Setting' },
  { label: '文档', value: 'Document' },
  { label: '文件夹', value: 'Folder' },
  { label: '链接', value: 'Link' },
  { label: '工具', value: 'Tools' },
  { label: '监控', value: 'Monitor' },
  { label: '数据', value: 'DataAnalysis' },
  { label: '图表', value: 'PieChart' },
  { label: '消息', value: 'Message' },
  { label: '通知', value: 'Bell' },
  { label: '搜索', value: 'Search' },
  { label: '刷新', value: 'Refresh' },
  { label: '编辑', value: 'Edit' },
  { label: '删除', value: 'Delete' },
  { label: '添加', value: 'Plus' },
  { label: '下载', value: 'Download' },
  { label: '上传', value: 'Upload' },
  { label: '导出', value: 'Export' },
  { label: '导入', value: 'Import' }
]

// 表单验证规则
const formRules = {
  resourceName: [
    { required: true, message: '请输入资源名称', trigger: 'blur' },
    { min: 2, max: 50, message: '资源名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  resourceCode: [
    { required: true, message: '请输入资源编码', trigger: 'blur' },
    { min: 2, max: 100, message: '资源编码长度在 2 到 100 个字符', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9_:]+$/,
      message: '资源编码只能包含字母、数字、下划线和冒号',
      trigger: 'blur'
    }
  ],
  resourceType: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  path: [
    {
      validator: (rule, value, callback) => {
        if (form.resourceType === 'menu' && !value) {
          callback(new Error('菜单类型资源必须填写路径'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  sortOrder: [
    { required: true, message: '请输入排序值', trigger: 'blur' }
  ]
}

// 获取对话框标题
const getDialogTitle = () => {
  switch (props.formType) {
    case 'add':
      return '新增资源'
    case 'edit':
      return '编辑资源'
    case 'addChild':
      return '新增子资源'
    default:
      return '资源表单'
  }
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
    if (props.formType === 'edit' && props.formData) {
      Object.assign(form, props.formData)
    }
    if (props.formType === 'addChild' && props.parentResource) {
      form.parentId = props.parentResource.resourceId
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    resourceName: '',
    resourceCode: '',
    resourceType: 'menu',
    path: '',
    icon: '',
    sortOrder: 0,
    isVisible: true,
    description: '',
    parentId: null
  })
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    loading.value = true

    const submitData = { ...form }

    // 如果不是菜单类型，清除路径和图标
    if (submitData.resourceType !== 'menu') {
      submitData.path = ''
      submitData.icon = ''
    }

    if (props.formType === 'add' || props.formType === 'addChild') {
      await resourceApi.createResource(submitData)
      ElMessage.success('资源创建成功')
    } else {
      await resourceApi.updateResource(props.formData.resourceId, submitData)
      ElMessage.success('资源更新成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.icon-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
