// 用户状态操作组合式函数
import { ElMessage, ElMessageBox } from 'element-plus'
import { userApi } from '@/api'
import { USER_STATUS, USER_STATUS_TEXT, USER_STATUS_COLOR } from '@/constants'

export function useUserStatus() {
  // 获取用户状态文本
  const getUserStatusText = (status) => {
    return USER_STATUS_TEXT[status] || '未知'
  }

  // 获取用户状态颜色
  const getUserStatusColor = (status) => {
    return USER_STATUS_COLOR[status] || 'info'
  }

  // 检查用户是否可以激活
  const canActivateUser = (user) => {
    return user.isActivated === USER_STATUS.INACTIVE
  }

  // 检查用户是否可以停用
  const canDeactivateUser = (user) => {
    return user.isActivated === USER_STATUS.ACTIVE
  }

  // 检查用户是否可以锁定
  const canLockUser = (user) => {
    return user.isActivated !== USER_STATUS.LOCKED
  }

  // 检查用户是否可以解锁
  const canUnlockUser = (user) => {
    return user.isActivated === USER_STATUS.LOCKED
  }

  // 激活用户
  const activateUser = async (user, options = {}) => {
    const { 
      showConfirm = true, 
      onSuccess, 
      onError,
      confirmMessage = `确定要激活用户 "${user.username}" 吗？`
    } = options

    try {
      if (showConfirm) {
        await ElMessageBox.confirm(
          confirmMessage,
          '激活确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
      }

      await userApi.activateUser(user.userId)
      ElMessage.success('用户激活成功')
      
      if (onSuccess) {
        onSuccess(user)
      }
      
      return true
    } catch (error) {
      if (error !== 'cancel') {
        console.error('激活用户失败:', error)
        ElMessage.error('激活用户失败')
        
        if (onError) {
          onError(error, user)
        }
      }
      return false
    }
  }

  // 停用用户
  const deactivateUser = async (user, options = {}) => {
    const { 
      showConfirm = true, 
      onSuccess, 
      onError,
      confirmMessage = `确定要停用用户 "${user.username}" 吗？停用后该用户将无法登录系统。`
    } = options

    try {
      if (showConfirm) {
        await ElMessageBox.confirm(
          confirmMessage,
          '停用确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      }

      await userApi.deactivateUser(user.userId)
      ElMessage.success('用户停用成功')
      
      if (onSuccess) {
        onSuccess(user)
      }
      
      return true
    } catch (error) {
      if (error !== 'cancel') {
        console.error('停用用户失败:', error)
        ElMessage.error('停用用户失败')
        
        if (onError) {
          onError(error, user)
        }
      }
      return false
    }
  }

  // 锁定用户
  const lockUser = async (user, options = {}) => {
    const { 
      showConfirm = true, 
      onSuccess, 
      onError,
      confirmMessage = `确定要锁定用户 "${user.username}" 吗？锁定后该用户将无法登录系统。`
    } = options

    try {
      if (showConfirm) {
        await ElMessageBox.confirm(
          confirmMessage,
          '锁定确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
      }

      await userApi.lockUser(user.userId)
      ElMessage.success('用户锁定成功')
      
      if (onSuccess) {
        onSuccess(user)
      }
      
      return true
    } catch (error) {
      if (error !== 'cancel') {
        console.error('锁定用户失败:', error)
        ElMessage.error('锁定用户失败')
        
        if (onError) {
          onError(error, user)
        }
      }
      return false
    }
  }

  // 解锁用户
  const unlockUser = async (user, options = {}) => {
    const { 
      showConfirm = true, 
      onSuccess, 
      onError,
      confirmMessage = `确定要解锁用户 "${user.username}" 吗？`
    } = options

    try {
      if (showConfirm) {
        await ElMessageBox.confirm(
          confirmMessage,
          '解锁确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        )
      }

      await userApi.unlockUser(user.userId)
      ElMessage.success('用户解锁成功')
      
      if (onSuccess) {
        onSuccess(user)
      }
      
      return true
    } catch (error) {
      if (error !== 'cancel') {
        console.error('解锁用户失败:', error)
        ElMessage.error('解锁用户失败')
        
        if (onError) {
          onError(error, user)
        }
      }
      return false
    }
  }

  // 批量状态操作
  const batchStatusOperation = async (users, operation, options = {}) => {
    const { onSuccess, onError } = options
    const results = []

    for (const user of users) {
      let result = false
      
      switch (operation) {
        case 'activate':
          result = await activateUser(user, { showConfirm: false, onSuccess: null, onError: null })
          break
        case 'deactivate':
          result = await deactivateUser(user, { showConfirm: false, onSuccess: null, onError: null })
          break
        case 'lock':
          result = await lockUser(user, { showConfirm: false, onSuccess: null, onError: null })
          break
        case 'unlock':
          result = await unlockUser(user, { showConfirm: false, onSuccess: null, onError: null })
          break
      }
      
      results.push({ user, success: result })
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.length - successCount

    if (successCount > 0) {
      ElMessage.success(`成功操作 ${successCount} 个用户`)
      if (onSuccess) {
        onSuccess(results.filter(r => r.success).map(r => r.user))
      }
    }

    if (failCount > 0) {
      ElMessage.warning(`${failCount} 个用户操作失败`)
      if (onError) {
        onError(results.filter(r => !r.success).map(r => r.user))
      }
    }

    return results
  }

  return {
    // 状态检查
    getUserStatusText,
    getUserStatusColor,
    canActivateUser,
    canDeactivateUser,
    canLockUser,
    canUnlockUser,
    
    // 状态操作
    activateUser,
    deactivateUser,
    lockUser,
    unlockUser,
    batchStatusOperation,
    
    // 常量
    USER_STATUS
  }
}
