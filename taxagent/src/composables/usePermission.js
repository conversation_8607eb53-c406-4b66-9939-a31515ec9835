// 权限控制组合式函数
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

export function usePermission() {
  const authStore = useAuthStore()

  // 检查是否有指定权限
  const hasPermission = (permission) => {
    if (!permission) return true
    return authStore.hasPermission(permission)
  }

  // 检查是否有指定角色
  const hasRole = (role) => {
    if (!role) return true
    return authStore.hasRole(role)
  }

  // 检查是否有任意一个权限
  const hasAnyPermission = (permissions) => {
    if (!permissions || permissions.length === 0) return true
    return permissions.some(permission => authStore.hasPermission(permission))
  }

  // 检查是否有所有权限
  const hasAllPermissions = (permissions) => {
    if (!permissions || permissions.length === 0) return true
    return permissions.every(permission => authStore.hasPermission(permission))
  }

  // 检查是否有任意一个角色
  const hasAnyRole = (roles) => {
    if (!roles || roles.length === 0) return true
    return roles.some(role => authStore.hasRole(role))
  }

  // 检查是否有所有角色
  const hasAllRoles = (roles) => {
    if (!roles || roles.length === 0) return true
    return roles.every(role => authStore.hasRole(role))
  }

  // 检查是否为超级管理员
  const isSuperAdmin = computed(() => {
    return authStore.hasRole('super_admin')
  })

  // 检查是否为租户管理员
  const isTenantAdmin = computed(() => {
    return authStore.hasRole('tenant_admin')
  })

  // 检查是否为管理员（超级管理员或租户管理员）
  const isAdmin = computed(() => {
    return isSuperAdmin.value || isTenantAdmin.value
  })

  // 获取当前用户信息
  const currentUser = computed(() => {
    return authStore.userInfo
  })

  // 获取当前用户角色
  const currentRoles = computed(() => {
    return authStore.roles
  })

  // 获取当前用户权限
  const currentPermissions = computed(() => {
    return authStore.permissions
  })

  return {
    // 权限检查方法
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    hasAnyRole,
    hasAllRoles,
    
    // 计算属性
    isSuperAdmin,
    isTenantAdmin,
    isAdmin,
    currentUser,
    currentRoles,
    currentPermissions
  }
}
