// 页面国际化组合函数
import { useI18n } from 'vue-i18n'

export function usePageI18n() {
  const { t, locale } = useI18n()
  
  // 带默认值的翻译函数
  const $t = (key, defaultValue = '') => {
    try {
      const result = t(key)
      return result !== key ? result : defaultValue
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error)
      return defaultValue
    }
  }
  
  // 页面标题翻译
  const getPageTitle = (titleKey, defaultTitle) => {
    return $t(titleKey, defaultTitle)
  }
  
  // 表格列翻译
  const getColumnLabel = (labelKey, defaultLabel) => {
    return $t(labelKey, defaultLabel)
  }
  
  // 按钮文本翻译
  const getButtonText = (textKey, defaultText) => {
    return $t(textKey, defaultText)
  }
  
  // 表单标签翻译
  const getFormLabel = (labelKey, defaultLabel) => {
    return $t(labelKey, defaultLabel)
  }
  
  // 占位符翻译
  const getPlaceholder = (placeholderKey, defaultPlaceholder) => {
    return $t(placeholderKey, defaultPlaceholder)
  }
  
  // 状态文本翻译
  const getStatusText = (statusKey, defaultText) => {
    return $t(statusKey, defaultText)
  }
  
  // 消息翻译
  const getMessage = (messageKey, defaultMessage) => {
    return $t(messageKey, defaultMessage)
  }
  
  // 菜单标题翻译
  const getMenuTitle = (menuKey, defaultTitle) => {
    return $t(menuKey, defaultTitle)
  }
  
  return {
    t: $t,
    locale,
    getPageTitle,
    getColumnLabel,
    getButtonText,
    getFormLabel,
    getPlaceholder,
    getStatusText,
    getMessage,
    getMenuTitle
  }
}

// 常用的翻译键映射
export const PAGE_TITLES = {
  dashboard: { key: 'dashboard.title', default: '仪表盘' },
  users: { key: 'user.title', default: '用户管理' },
  roles: { key: 'role.title', default: '角色管理' },
  permissions: { key: 'permission.title', default: '权限管理' },
  resources: { key: 'resource.title', default: '资源管理' },
  onlineUsers: { key: 'onlineUser.title', default: '在线用户' }
}

export const COMMON_LABELS = {
  username: { key: 'user.username', default: '用户名' },
  fullName: { key: 'user.fullName', default: '姓名' },
  email: { key: 'user.email', default: '邮箱' },
  phone: { key: 'user.phone', default: '手机号' },
  status: { key: 'common.status', default: '状态' },
  createTime: { key: 'common.createTime', default: '创建时间' },
  updateTime: { key: 'common.updateTime', default: '更新时间' },
  operation: { key: 'common.operation', default: '操作' },
  remark: { key: 'common.remark', default: '备注' }
}

export const COMMON_BUTTONS = {
  add: { key: 'actions.add', default: '新增' },
  edit: { key: 'actions.edit', default: '编辑' },
  delete: { key: 'actions.delete', default: '删除' },
  view: { key: 'actions.view', default: '查看' },
  search: { key: 'actions.search', default: '搜索' },
  reset: { key: 'actions.reset', default: '重置' },
  save: { key: 'actions.save', default: '保存' },
  cancel: { key: 'actions.cancel', default: '取消' },
  confirm: { key: 'actions.confirm', default: '确认' },
  refresh: { key: 'actions.refresh', default: '刷新' },
  export: { key: 'actions.export', default: '导出' },
  import: { key: 'actions.import', default: '导入' }
}

export const COMMON_PLACEHOLDERS = {
  username: { key: 'user.usernamePlaceholder', default: '请输入用户名' },
  fullName: { key: 'user.fullNamePlaceholder', default: '请输入姓名' },
  email: { key: 'user.emailPlaceholder', default: '请输入邮箱' },
  phone: { key: 'user.phonePlaceholder', default: '请输入手机号' },
  password: { key: 'login.passwordPlaceholder', default: '请输入密码' },
  search: { key: 'common.search', default: '请输入搜索关键词' }
}

export const COMMON_MESSAGES = {
  saveSuccess: { key: 'message.saveSuccess', default: '保存成功' },
  saveFailed: { key: 'message.saveFailed', default: '保存失败' },
  deleteSuccess: { key: 'message.deleteSuccess', default: '删除成功' },
  deleteFailed: { key: 'message.deleteFailed', default: '删除失败' },
  updateSuccess: { key: 'message.updateSuccess', default: '更新成功' },
  updateFailed: { key: 'message.updateFailed', default: '更新失败' },
  createSuccess: { key: 'message.createSuccess', default: '创建成功' },
  createFailed: { key: 'message.createFailed', default: '创建失败' },
  operationSuccess: { key: 'message.operationSuccess', default: '操作成功' },
  operationFailed: { key: 'message.operationFailed', default: '操作失败' },
  confirmDelete: { key: 'message.confirmDelete', default: '确定要删除吗？' },
  confirmOperation: { key: 'message.confirmOperation', default: '确定要执行此操作吗？' }
}
