/*!
  * vue-i18n v9.14.5
  * (c) 2025 ka<PERSON><PERSON> kawa<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";function a(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const n="undefined"!=typeof window,r=(e,t=!1)=>t?Symbol.for(e):Symbol(e),l=(e,t,a)=>o({l:e,k:t,s:a}),o=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"==typeof e&&isFinite(e),i=e=>"[object Date]"===y(e),c=e=>"[object RegExp]"===y(e),u=e=>N(e)&&0===Object.keys(e).length,m=Object.assign,f=Object.create,_=(e=null)=>f(e);function g(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/\//g,"&#x2F;").replace(/=/g,"&#x3D;")}function p(e){return e.replace(/&(?![a-zA-Z0-9#]{2,6};)/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}const v=Object.prototype.hasOwnProperty;function d(e,t){return v.call(e,t)}const b=Array.isArray,E=e=>"function"==typeof e,h=e=>"string"==typeof e,k=e=>"boolean"==typeof e,L=e=>null!==e&&"object"==typeof e,T=e=>L(e)&&E(e.then)&&E(e.catch),F=Object.prototype.toString,y=e=>F.call(e),N=e=>{if(!L(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function I(e){let t=e;return()=>++t}const R=e=>!L(e)||b(e);function O(e,t){if(R(e)||R(t))throw new Error("Invalid value");const a=[{src:e,des:t}];for(;a.length;){const{src:e,des:t}=a.pop();Object.keys(e).forEach((n=>{"__proto__"!==n&&(L(e[n])&&!L(t[n])&&(t[n]=Array.isArray(e[n])?[]:_()),R(t[n])||R(e[n])?t[n]=e[n]:a.push({src:e[n],des:t[n]}))}))}}function M(e,t,a={}){const{domain:n,messages:r,args:l}=a,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=n,o}function W(e){return L(e)&&0===function(e,t,a){for(let n=0;n<t.length;n++){const a=t[n];if(d(e,a)&&null!=e[a])return e[a]}return a}(e,w)&&(d(e,"b")||d(e,"body"))}const w=["t","type"];const P=["b","body","c","cases","s","static","i","items","k","key","m","modifier","v","value",...w],C=[];C[0]={w:[0],i:[3,0],"[":[4],o:[7]},C[1]={w:[1],".":[2],"[":[4],o:[7]},C[2]={w:[2],i:[3,0],0:[3,0]},C[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},C[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},C[5]={"'":[4,0],o:8,l:[5,0]},C[6]={'"':[4,0],o:8,l:[6,0]};const D=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function A(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function S(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(a=t,D.test(a)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var a}const $=new Map;function U(e,t){return L(e)?e[t]:null}const x=e=>e,H=e=>"",j="text",V=e=>0===e.length?"":function(e,t=""){return e.reduce(((e,a,n)=>0===n?e+a:e+t+a),"")}(e),G=e=>null==e?"":b(e)||N(e)&&e.toString===F?JSON.stringify(e,null,2):String(e);function Y(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function B(e={}){const t=e.locale,a=function(e){const t=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:t:t}(e),n=L(e.pluralRules)&&h(t)&&E(e.pluralRules[t])?e.pluralRules[t]:Y,r=L(e.pluralRules)&&h(t)&&E(e.pluralRules[t])?Y:void 0,l=e.list||[],o=e.named||_();s(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,o);function i(t){const a=E(e.messages)?e.messages(t):!!L(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):H)}const c=N(e.processor)&&E(e.processor.normalize)?e.processor.normalize:V,u=N(e.processor)&&E(e.processor.interpolate)?e.processor.interpolate:G,f={list:e=>l[e],named:e=>o[e],plural:e=>e[n(a,e.length,r)],linked:(t,...a)=>{const[n,r]=a;let l="text",o="";1===a.length?L(n)?(o=n.modifier||o,l=n.type||l):h(n)&&(o=n||o):2===a.length&&(h(n)&&(o=n||o),h(r)&&(l=r||l));const s=i(t)(f),c="vnode"===l&&b(s)&&o?s[0]:s;return o?(u=o,e.modifiers?e.modifiers[u]:x)(c,l):c;var u},message:i,type:N(e.processor)&&h(e.processor.type)?e.processor.type:j,interpolate:u,normalize:c,values:m(_(),l,o)};return f}const X=I(17),z={INVALID_ARGUMENT:17,INVALID_DATE_ARGUMENT:X(),INVALID_ISO_DATE_ARGUMENT:X(),NOT_SUPPORT_NON_STRING_MESSAGE:X(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:X(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:X(),NOT_SUPPORT_LOCALE_TYPE:X(),__EXTEND_POINT__:X()};function J(e,t){return null!=t.locale?Z(t.locale):Z(e.locale)}let q;function Z(e){if(h(e))return e;if(E(e)){if(e.resolvedOnce&&null!=q)return q;if("Function"===e.constructor.name){const t=e();if(T(t))throw Error(z.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return q=t}throw Error(z.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(z.NOT_SUPPORT_LOCALE_TYPE)}function K(e,t,a){return[...new Set([a,...b(t)?t:L(t)?Object.keys(t):h(t)?[t]:[a]])]}function Q(e,t,a){const n=h(a)?a:le,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let l=r.__localeChainCache.get(n);if(!l){l=[];let e=[a];for(;b(e);)e=ee(l,e,t);const o=b(t)||!N(t)?t:t.default?t.default:null;e=h(o)?[o]:o,b(e)&&ee(l,e,!1),r.__localeChainCache.set(n,l)}return l}function ee(e,t,a){let n=!0;for(let r=0;r<t.length&&k(n);r++){const l=t[r];h(l)&&(n=te(e,t[r],a))}return n}function te(e,t,a){let n;const r=t.split("-");do{n=ae(e,r.join("-"),a),r.splice(-1,1)}while(r.length&&!0===n);return n}function ae(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(b(a)||N(a))&&a[r]&&(n=a[r])}return n}const ne="9.14.5",re=-1,le="en-US",oe="",se=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let ie,ce,ue;let me=null;const fe=e=>{me=e},_e=()=>me;let ge=0;function pe(e={}){const t=E(e.onWarn)?e.onWarn:a,n=h(e.version)?e.version:ne,r=h(e.locale)||E(e.locale)?e.locale:le,l=E(r)?le:r,o=b(e.fallbackLocale)||N(e.fallbackLocale)||h(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:l,s=N(e.messages)?e.messages:ve(l),i=N(e.datetimeFormats)?e.datetimeFormats:ve(l),u=N(e.numberFormats)?e.numberFormats:ve(l),f=m(_(),e.modifiers,{upper:(e,t)=>"text"===t&&h(e)?e.toUpperCase():"vnode"===t&&L(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&h(e)?e.toLowerCase():"vnode"===t&&L(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&h(e)?se(e):"vnode"===t&&L(e)&&"__v_isVNode"in e?se(e.children):e}),g=e.pluralRules||_(),p=E(e.missing)?e.missing:null,v=!k(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,d=!k(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,T=!!e.fallbackFormat,F=!!e.unresolving,y=E(e.postTranslation)?e.postTranslation:null,I=N(e.processor)?e.processor:null,R=!k(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter,M=E(e.messageCompiler)?e.messageCompiler:ie,W=E(e.messageResolver)?e.messageResolver:ce||U,w=E(e.localeFallbacker)?e.localeFallbacker:ue||K,P=L(e.fallbackContext)?e.fallbackContext:void 0,C=e,D=L(C.__datetimeFormatters)?C.__datetimeFormatters:new Map,A=L(C.__numberFormatters)?C.__numberFormatters:new Map,S=L(C.__meta)?C.__meta:{};ge++;const $={version:n,cid:ge,locale:r,fallbackLocale:o,messages:s,modifiers:f,pluralRules:g,missing:p,missingWarn:v,fallbackWarn:d,fallbackFormat:T,unresolving:F,postTranslation:y,processor:I,warnHtmlMessage:R,escapeParameter:O,messageCompiler:M,messageResolver:W,localeFallbacker:w,fallbackContext:P,onWarn:t,__meta:S};return $.datetimeFormats=i,$.numberFormats=u,$.__datetimeFormatters=D,$.__numberFormatters=A,$}const ve=e=>({[e]:_()});function de(e,t,a,n,r){const{missing:l,onWarn:o}=e;if(null!==l){const n=l(e,a,t,r);return h(n)?n:t}return t}function be(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}function Ee(e,t){const a=t.indexOf(e);if(-1===a)return!1;for(let l=a+1;l<t.length;l++)if(n=e,r=t[l],n!==r&&n.split("-")[0]===r.split("-")[0])return!0;var n,r;return!1}const he=()=>"",ke=e=>E(e);function Le(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:r,messageCompiler:l,fallbackLocale:o,messages:i}=e,[c,u]=ye(...t),m=k(u.missingWarn)?u.missingWarn:e.missingWarn,f=k(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,v=k(u.escapeParameter)?u.escapeParameter:e.escapeParameter,d=!!u.resolvedMessage,E=h(u.default)||k(u.default)?k(u.default)?l?c:()=>c:u.default:a?l?c:()=>c:"",T=a||""!==E,F=J(e,u);v&&function(e){b(e.list)?e.list=e.list.map((e=>h(e)?g(e):e)):L(e.named)&&Object.keys(e.named).forEach((t=>{h(e.named[t])&&(e.named[t]=g(e.named[t]))}))}(u);let[y,N,I]=d?[c,F,i[F]||_()]:Te(e,c,F,o,f,m),R=y,O=c;if(d||h(R)||W(R)||ke(R)||T&&(R=E,O=R),!(d||(h(R)||W(R)||ke(R))&&h(N)))return r?re:c;let M=!1;const w=ke(R)?R:Fe(e,c,N,R,O,(()=>{M=!0}));if(M)return R;const P=function(e,t,a,n){const{modifiers:r,pluralRules:l,messageResolver:o,fallbackLocale:i,fallbackWarn:c,missingWarn:u,fallbackContext:m}=e,f=n=>{let r=o(a,n);if(null==r&&m){const[,,e]=Te(m,n,t,i,c,u);r=o(e,n)}if(h(r)||W(r)){let a=!1;const l=Fe(e,n,t,r,n,(()=>{a=!0}));return a?he:l}return ke(r)?r:he},_={locale:t,modifiers:r,pluralRules:l,messages:f};e.processor&&(_.processor=e.processor);n.list&&(_.list=n.list);n.named&&(_.named=n.named);s(n.plural)&&(_.pluralIndex=n.plural);return _}(e,N,I,u),C=function(e,t,a){const n=t(a);return n}(0,w,B(P));let D=n?n(C,c):C;var A;return v&&h(D)&&(A=(A=(A=D).replace(/(\w+)\s*=\s*"([^"]*)"/g,((e,t,a)=>`${t}="${p(a)}"`))).replace(/(\w+)\s*=\s*'([^']*)'/g,((e,t,a)=>`${t}='${p(a)}'`)),/\s*on\w+\s*=\s*["']?[^"'>]+["']?/gi.test(A)&&(A=A.replace(/(\s+)(on)(\w+\s*=)/gi,"$1&#111;n$3")),[/(\s+(?:href|src|action|formaction)\s*=\s*["']?)\s*javascript:/gi,/(style\s*=\s*["'][^"']*url\s*\(\s*)javascript:/gi].forEach((e=>{A=A.replace(e,"$1javascript&#58;")})),D=A),D}function Te(e,t,a,n,r,l){const{messages:o,onWarn:s,messageResolver:i,localeFallbacker:c}=e,u=c(e,n,a);let m,f=_(),g=null;for(let p=0;p<u.length&&(m=u[p],f=o[m]||_(),null===(g=i(f,t))&&(g=f[t]),!(h(g)||W(g)||ke(g)));p++)if(!Ee(m,u)){const a=de(e,t,m,0,"translate");a!==t&&(g=a)}return[g,m,f]}function Fe(e,t,a,n,r,o){const{messageCompiler:s,warnHtmlMessage:i}=e;if(ke(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==s){const e=()=>n;return e.locale=a,e.key=t,e}const c=s(n,function(e,t,a,n,r,o){return{locale:t,key:a,warnHtmlMessage:r,onError:e=>{throw o&&o(e),e},onCacheKey:e=>l(t,a,e)}}(0,a,r,0,i,o));return c.locale=a,c.key=t,c.source=n,c}function ye(...e){const[t,a,n]=e,r=_();if(!(h(t)||s(t)||ke(t)||W(t)))throw Error(z.INVALID_ARGUMENT);const l=s(t)?String(t):(ke(t),t);return s(a)?r.plural=a:h(a)?r.default=a:N(a)&&!u(a)?r.named=a:b(a)&&(r.list=a),s(n)?r.plural=n:h(n)?r.default=n:N(n)&&m(r,n),[l,r]}function Ne(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:r,onWarn:l,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[i,c,f,_]=Re(...t);k(f.missingWarn)?f.missingWarn:e.missingWarn;k(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const g=!!f.part,p=J(e,f),v=o(e,r,p);if(!h(i)||""===i)return new Intl.DateTimeFormat(p,_).format(c);let d,b={},E=null;for(let u=0;u<v.length&&(d=v[u],b=a[d]||{},E=b[i],!N(E));u++)de(e,i,d,0,"datetime format");if(!N(E)||!h(d))return n?re:i;let L=`${d}__${i}`;u(_)||(L=`${L}__${JSON.stringify(_)}`);let T=s.get(L);return T||(T=new Intl.DateTimeFormat(d,m({},E,_)),s.set(L,T)),g?T.formatToParts(c):T.format(c)}const Ie=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Re(...e){const[t,a,n,r]=e,l=_();let o,c=_();if(h(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(z.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();o=new Date(a);try{o.toISOString()}catch(u){throw Error(z.INVALID_ISO_DATE_ARGUMENT)}}else if(i(t)){if(isNaN(t.getTime()))throw Error(z.INVALID_DATE_ARGUMENT);o=t}else{if(!s(t))throw Error(z.INVALID_ARGUMENT);o=t}return h(a)?l.key=a:N(a)&&Object.keys(a).forEach((e=>{Ie.includes(e)?c[e]=a[e]:l[e]=a[e]})),h(n)?l.locale=n:N(n)&&(c=n),N(r)&&(c=r),[l.key||"",o,l,c]}function Oe(e,t,a){const n=e;for(const r in a){const e=`${t}__${r}`;n.__datetimeFormatters.has(e)&&n.__datetimeFormatters.delete(e)}}function Me(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:r,onWarn:l,localeFallbacker:o}=e,{__numberFormatters:s}=e,[i,c,f,_]=we(...t);k(f.missingWarn)?f.missingWarn:e.missingWarn;k(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const g=!!f.part,p=J(e,f),v=o(e,r,p);if(!h(i)||""===i)return new Intl.NumberFormat(p,_).format(c);let d,b={},E=null;for(let u=0;u<v.length&&(d=v[u],b=a[d]||{},E=b[i],!N(E));u++)de(e,i,d,0,"number format");if(!N(E)||!h(d))return n?re:i;let L=`${d}__${i}`;u(_)||(L=`${L}__${JSON.stringify(_)}`);let T=s.get(L);return T||(T=new Intl.NumberFormat(d,m({},E,_)),s.set(L,T)),g?T.formatToParts(c):T.format(c)}const We=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function we(...e){const[t,a,n,r]=e,l=_();let o=_();if(!s(t))throw Error(z.INVALID_ARGUMENT);const i=t;return h(a)?l.key=a:N(a)&&Object.keys(a).forEach((e=>{We.includes(e)?o[e]=a[e]:l[e]=a[e]})),h(n)?l.locale=n:N(n)&&(o=n),N(r)&&(o=r),[l.key||"",i,l,o]}function Pe(e,t,a){const n=e;for(const r in a){const e=`${t}__${r}`;n.__numberFormatters.has(e)&&n.__numberFormatters.delete(e)}}const Ce="9.14.5",De=z.__EXTEND_POINT__,Ae=I(De),Se={UNEXPECTED_RETURN_TYPE:De,INVALID_ARGUMENT:Ae(),MUST_BE_CALL_SETUP_TOP:Ae(),NOT_INSTALLED:Ae(),NOT_AVAILABLE_IN_LEGACY_MODE:Ae(),REQUIRED_VALUE:Ae(),INVALID_VALUE:Ae(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Ae(),NOT_INSTALLED_WITH_PROVIDE:Ae(),UNEXPECTED_ERROR:Ae(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Ae(),BRIDGE_SUPPORT_VUE_2_ONLY:Ae(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Ae(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Ae(),__EXTEND_POINT__:Ae()};const $e=r("__translateVNode"),Ue=r("__datetimeParts"),xe=r("__numberParts"),He=r("__setPluralRules"),je=r("__injectWithOption"),Ve=r("__dispose");function Ge(e){if(!L(e))return e;if(W(e))return e;for(const t in e)if(d(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let r=e,l=!1;for(let e=0;e<n;e++){if("__proto__"===a[e])throw new Error(`unsafe key: ${a[e]}`);if(a[e]in r||(r[a[e]]=_()),!L(r[a[e]])){l=!0;break}r=r[a[e]]}if(l||(W(r)?P.includes(a[n])||delete e[t]:(r[a[n]]=e[t],delete e[t])),!W(r)){const e=r[a[n]];L(e)&&Ge(e)}}else L(e[t])&&Ge(e[t]);return e}function Ye(e,t){const{messages:a,__i18n:n,messageResolver:r,flatJson:l}=t,o=N(a)?a:b(n)?_():{[e]:_()};if(b(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(o[t]=o[t]||_(),O(a,o[t])):O(a,o)}else h(e)&&O(JSON.parse(e),o)})),null==r&&l)for(const s in o)d(o,s)&&Ge(o[s]);return o}function Be(e){return e.type}function Xe(e,t,a){let n=L(t.messages)?t.messages:_();"__i18nGlobal"in a&&(n=Ye(e.locale.value,{messages:n,__i18n:a.__i18nGlobal}));const r=Object.keys(n);if(r.length&&r.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),L(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(L(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function ze(e){return t.createVNode(t.Text,null,e,0)}const Je=()=>[],qe=()=>!1;let Ze=0;function Ke(e){return(a,n,r,l)=>e(n,r,t.getCurrentInstance()||void 0,l)}function Qe(e={},a){const{__root:r,__injectWithOption:l}=e,o=void 0===r,i=e.flatJson,u=n?t.ref:t.shallowRef,f=!!e.translateExistCompatible;let _=!k(e.inheritLocale)||e.inheritLocale;const g=u(r&&_?r.locale.value:h(e.locale)?e.locale:le),p=u(r&&_?r.fallbackLocale.value:h(e.fallbackLocale)||b(e.fallbackLocale)||N(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:g.value),v=u(Ye(g.value,e)),T=u(N(e.datetimeFormats)?e.datetimeFormats:{[g.value]:{}}),F=u(N(e.numberFormats)?e.numberFormats:{[g.value]:{}});let y=r?r.missingWarn:!k(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,I=r?r.fallbackWarn:!k(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,R=r?r.fallbackRoot:!k(e.fallbackRoot)||e.fallbackRoot,M=!!e.fallbackFormat,w=E(e.missing)?e.missing:null,P=E(e.missing)?Ke(e.missing):null,C=E(e.postTranslation)?e.postTranslation:null,D=r?r.warnHtmlMessage:!k(e.warnHtmlMessage)||e.warnHtmlMessage,A=!!e.escapeParameter;const S=r?r.modifiers:N(e.modifiers)?e.modifiers:{};let $,U=e.pluralRules||r&&r.pluralRules;$=(()=>{o&&fe(null);const t={version:Ce,locale:g.value,fallbackLocale:p.value,messages:v.value,modifiers:S,pluralRules:U,missing:null===P?void 0:P,missingWarn:y,fallbackWarn:I,fallbackFormat:M,unresolving:!0,postTranslation:null===C?void 0:C,warnHtmlMessage:D,escapeParameter:A,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=T.value,t.numberFormats=F.value,t.__datetimeFormatters=N($)?$.__datetimeFormatters:void 0,t.__numberFormatters=N($)?$.__numberFormatters:void 0;const a=pe(t);return o&&fe(a),a})(),be($,g.value,p.value);const x=t.computed({get:()=>g.value,set:e=>{g.value=e,$.locale=g.value}}),H=t.computed({get:()=>p.value,set:e=>{p.value=e,$.fallbackLocale=p.value,be($,g.value,e)}}),j=t.computed((()=>v.value)),V=t.computed((()=>T.value)),G=t.computed((()=>F.value));const Y=(e,t,a,n,l,i)=>{let c;g.value,p.value,v.value,T.value,F.value;try{0,o||($.fallbackContext=r?_e():void 0),c=e($)}finally{o||($.fallbackContext=void 0)}if("translate exists"!==a&&s(c)&&c===re||"translate exists"===a&&!c){const[e,a]=t();return r&&R?n(r):l(e)}if(i(c))return c;throw Error(Se.UNEXPECTED_RETURN_TYPE)};function B(...e){return Y((t=>Reflect.apply(Le,null,[t,...e])),(()=>ye(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>h(e)))}const X={normalize:function(e){return e.map((e=>h(e)||s(e)||k(e)?ze(String(e)):e))},interpolate:e=>e,type:"vnode"};function z(e){return v.value[e]||{}}Ze++,r&&n&&(t.watch(r.locale,(e=>{_&&(g.value=e,$.locale=e,be($,g.value,p.value))})),t.watch(r.fallbackLocale,(e=>{_&&(p.value=e,$.fallbackLocale=e,be($,g.value,p.value))})));const J={id:Ze,locale:x,fallbackLocale:H,get inheritLocale(){return _},set inheritLocale(e){_=e,e&&r&&(g.value=r.locale.value,p.value=r.fallbackLocale.value,be($,g.value,p.value))},get availableLocales(){return Object.keys(v.value).sort()},messages:j,get modifiers(){return S},get pluralRules(){return U||{}},get isGlobal(){return o},get missingWarn(){return y},set missingWarn(e){y=e,$.missingWarn=y},get fallbackWarn(){return I},set fallbackWarn(e){I=e,$.fallbackWarn=I},get fallbackRoot(){return R},set fallbackRoot(e){R=e},get fallbackFormat(){return M},set fallbackFormat(e){M=e,$.fallbackFormat=M},get warnHtmlMessage(){return D},set warnHtmlMessage(e){D=e,$.warnHtmlMessage=e},get escapeParameter(){return A},set escapeParameter(e){A=e,$.escapeParameter=e},t:B,getLocaleMessage:z,setLocaleMessage:function(e,t){if(i){const a={[e]:t};for(const e in a)d(a,e)&&Ge(a[e]);t=a[e]}v.value[e]=t,$.messages=v.value},mergeLocaleMessage:function(e,t){v.value[e]=v.value[e]||{};const a={[e]:t};if(i)for(const n in a)d(a,n)&&Ge(a[n]);O(t=a[e],v.value[e]),$.messages=v.value},getPostTranslationHandler:function(){return E(C)?C:null},setPostTranslationHandler:function(e){C=e,$.postTranslation=e},getMissingHandler:function(){return w},setMissingHandler:function(e){null!==e&&(P=Ke(e)),w=e,$.missing=P},[He]:function(e){U=e,$.pluralRules=U}};return J.datetimeFormats=V,J.numberFormats=G,J.rt=function(...e){const[t,a,n]=e;if(n&&!L(n))throw Error(Se.INVALID_ARGUMENT);return B(t,a,m({resolvedMessage:!0},n||{}))},J.te=function(e,t){return Y((()=>{if(!e)return!1;const a=z(h(t)?t:g.value),n=$.messageResolver(a,e);return f?null!=n:W(n)||ke(n)||h(n)}),(()=>[e]),"translate exists",(a=>Reflect.apply(a.te,a,[e,t])),qe,(e=>k(e)))},J.tm=function(e){const t=function(e){let t=null;const a=Q($,p.value,g.value);for(let n=0;n<a.length;n++){const r=v.value[a[n]]||{},l=$.messageResolver(r,e);if(null!=l){t=l;break}}return t}(e);return null!=t?t:r&&r.tm(e)||{}},J.d=function(...e){return Y((t=>Reflect.apply(Ne,null,[t,...e])),(()=>Re(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>oe),(e=>h(e)))},J.n=function(...e){return Y((t=>Reflect.apply(Me,null,[t,...e])),(()=>we(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>oe),(e=>h(e)))},J.getDateTimeFormat=function(e){return T.value[e]||{}},J.setDateTimeFormat=function(e,t){T.value[e]=t,$.datetimeFormats=T.value,Oe($,e,t)},J.mergeDateTimeFormat=function(e,t){T.value[e]=m(T.value[e]||{},t),$.datetimeFormats=T.value,Oe($,e,t)},J.getNumberFormat=function(e){return F.value[e]||{}},J.setNumberFormat=function(e,t){F.value[e]=t,$.numberFormats=F.value,Pe($,e,t)},J.mergeNumberFormat=function(e,t){F.value[e]=m(F.value[e]||{},t),$.numberFormats=F.value,Pe($,e,t)},J[je]=l,J[$e]=function(...e){return Y((t=>{let a;const n=t;try{n.processor=X,a=Reflect.apply(Le,null,[n,...e])}finally{n.processor=null}return a}),(()=>ye(...e)),"translate",(t=>t[$e](...e)),(e=>[ze(e)]),(e=>b(e)))},J[Ue]=function(...e){return Y((t=>Reflect.apply(Ne,null,[t,...e])),(()=>Re(...e)),"datetime format",(t=>t[Ue](...e)),Je,(e=>h(e)||b(e)))},J[xe]=function(...e){return Y((t=>Reflect.apply(Me,null,[t,...e])),(()=>we(...e)),"number format",(t=>t[xe](...e)),Je,(e=>h(e)||b(e)))},J}function et(e={},t){{const t=Qe(function(e){const t=h(e.locale)?e.locale:le,a=h(e.fallbackLocale)||b(e.fallbackLocale)||N(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=E(e.missing)?e.missing:void 0,r=!k(e.silentTranslationWarn)&&!c(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!k(e.silentFallbackWarn)&&!c(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!k(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,i=N(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,f=E(e.postTranslation)?e.postTranslation:void 0,_=!h(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,g=!!e.escapeParameterHtml,p=!k(e.sync)||e.sync;let v=e.messages;if(N(e.sharedMessages)){const t=e.sharedMessages;v=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return m(n,t[a]),e}),v||{})}const{__i18n:d,__root:L,__injectWithOption:T}=e,F=e.datetimeFormats,y=e.numberFormats,I=e.flatJson,R=e.translateExistCompatible;return{locale:t,fallbackLocale:a,messages:v,flatJson:I,datetimeFormats:F,numberFormats:y,missing:n,missingWarn:r,fallbackWarn:l,fallbackRoot:o,fallbackFormat:s,modifiers:i,pluralRules:u,postTranslation:f,warnHtmlMessage:_,escapeParameter:g,messageResolver:e.messageResolver,inheritLocale:p,translateExistCompatible:R,__i18n:d,__root:L,__injectWithOption:T}}(e)),{__extender:a}=e,n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return k(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=k(e)?!e:e},get silentFallbackWarn(){return k(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=k(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,r]=e,l={};let o=null,s=null;if(!h(a))throw Error(Se.INVALID_ARGUMENT);const i=a;return h(n)?l.locale=n:b(n)?o=n:N(n)&&(s=n),b(r)?o=r:N(r)&&(s=r),Reflect.apply(t.t,t,[i,o||s||{},l])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,n,r]=e,l={plural:1};let o=null,i=null;if(!h(a))throw Error(Se.INVALID_ARGUMENT);const c=a;return h(n)?l.locale=n:s(n)?l.plural=n:b(n)?o=n:N(n)&&(i=n),h(r)?l.locale=r:b(r)?o=r:N(r)&&(i=r),Reflect.apply(t.t,t,[c,o||i||{},l])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1};return n.__extender=a,n}}const tt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function at(e){return t.Fragment}const nt=t.defineComponent({name:"i18n-t",props:m({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>s(e)||!isNaN(e)}},tt),setup(e,a){const{slots:n,attrs:r}=a,l=e.i18n||pt({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(n).filter((e=>"_"!==e)),s=_();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=h(e.plural)?+e.plural:e.plural);const i=function({slots:e},a){if(1===a.length&&"default"===a[0])return(e.default?e.default():[]).reduce(((e,a)=>[...e,...a.type===t.Fragment?a.children:[a]]),[]);return a.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),_())}(a,o),c=l[$e](e.keypath,i,s),u=m(_(),r),f=h(e.tag)||L(e.tag)?e.tag:at();return t.h(f,u,c)}}}),rt=nt;function lt(e,a,n,r){const{slots:l,attrs:o}=a;return()=>{const a={part:!0};let s=_();e.locale&&(a.locale=e.locale),h(e.format)?a.key=e.format:L(e.format)&&(h(e.format.key)&&(a.key=e.format.key),s=Object.keys(e.format).reduce(((t,a)=>n.includes(a)?m(_(),t,{[a]:e.format[a]}):t),_()));const i=r(e.value,a,s);let c=[a.key];b(i)?c=i.map(((e,t)=>{const a=l[e.type],n=a?a({[e.type]:e.value,index:t,parts:i}):[e.value];var r;return b(r=n)&&!h(r[0])&&(n[0].key=`${e.type}-${t}`),n})):h(i)&&(c=[i]);const u=m(_(),o),f=h(e.tag)||L(e.tag)?e.tag:at();return t.h(f,u,c)}}const ot=t.defineComponent({name:"i18n-n",props:m({value:{type:Number,required:!0},format:{type:[String,Object]}},tt),setup(e,t){const a=e.i18n||pt({useScope:e.scope,__useComponent:!0});return lt(e,t,We,((...e)=>a[xe](...e)))}}),st=ot,it=t.defineComponent({name:"i18n-d",props:m({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},tt),setup(e,t){const a=e.i18n||pt({useScope:e.scope,__useComponent:!0});return lt(e,t,Ie,((...e)=>a[Ue](...e)))}}),ct=it;function ut(e){const a=t=>{const{instance:a,modifiers:n,value:r}=t;if(!a||!a.$)throw Error(Se.UNEXPECTED_ERROR);const l=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),o=mt(r);return[Reflect.apply(l.t,l,[...ft(o)]),l]};return{created:(r,l)=>{const[o,s]=a(l);n&&e.global===s&&(r.__i18nWatcher=t.watch(s.locale,(()=>{l.instance&&l.instance.$forceUpdate()}))),r.__composer=s,r.textContent=o},unmounted:e=>{n&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,n=mt(t);e.textContent=Reflect.apply(a.t,a,[...ft(n)])}},getSSRProps:e=>{const[t]=a(e);return{textContent:t}}}}function mt(e){if(h(e))return{path:e};if(N(e)){if(!("path"in e))throw Error(Se.REQUIRED_VALUE,"path");return e}throw Error(Se.INVALID_VALUE)}function ft(e){const{path:t,locale:a,args:n,choice:r,plural:l}=e,o={},i=n||{};return h(a)&&(o.locale=a),s(r)&&(o.plural=r),s(l)&&(o.plural=l),[t,i,o]}function _t(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[He](t.pluralizationRules||e.pluralizationRules);const a=Ye(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const gt=r("global-vue-i18n");function pt(e={}){const a=t.getCurrentInstance();if(null==a)throw Error(Se.MUST_BE_CALL_SETUP_TOP);if(!a.isCE&&null!=a.appContext.app&&!a.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Se.NOT_INSTALLED);const n=function(e){{const a=t.inject(e.isCE?gt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!a)throw function(e,...t){return M(e,null,void 0)}(e.isCE?Se.NOT_INSTALLED_WITH_PROVIDE:Se.UNEXPECTED_ERROR);return a}}(a),r=function(e){return"composition"===e.mode?e.global:e.global.__composer}(n),l=Be(a),o=function(e,t){return u(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,l);if("legacy"===n.mode&&!e.__useComponent){if(!n.allowComposition)throw Error(Se.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,a,n,r={}){const l="local"===a,o=t.shallowRef(null);if(l&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(Se.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=k(r.inheritLocale)?r.inheritLocale:!h(r.locale),i=t.ref(!l||s?n.locale.value:h(r.locale)?r.locale:le),u=t.ref(!l||s?n.fallbackLocale.value:h(r.fallbackLocale)||b(r.fallbackLocale)||N(r.fallbackLocale)||!1===r.fallbackLocale?r.fallbackLocale:i.value),m=t.ref(Ye(i.value,r)),f=t.ref(N(r.datetimeFormats)?r.datetimeFormats:{[i.value]:{}}),_=t.ref(N(r.numberFormats)?r.numberFormats:{[i.value]:{}}),g=l?n.missingWarn:!k(r.missingWarn)&&!c(r.missingWarn)||r.missingWarn,p=l?n.fallbackWarn:!k(r.fallbackWarn)&&!c(r.fallbackWarn)||r.fallbackWarn,v=l?n.fallbackRoot:!k(r.fallbackRoot)||r.fallbackRoot,d=!!r.fallbackFormat,L=E(r.missing)?r.missing:null,T=E(r.postTranslation)?r.postTranslation:null,F=l?n.warnHtmlMessage:!k(r.warnHtmlMessage)||r.warnHtmlMessage,y=!!r.escapeParameter,I=l?n.modifiers:N(r.modifiers)?r.modifiers:{},R=r.pluralRules||l&&n.pluralRules;function O(){return[i.value,u.value,m.value,f.value,_.value]}const M=t.computed({get:()=>o.value?o.value.locale.value:i.value,set:e=>{o.value&&(o.value.locale.value=e),i.value=e}}),W=t.computed({get:()=>o.value?o.value.fallbackLocale.value:u.value,set:e=>{o.value&&(o.value.fallbackLocale.value=e),u.value=e}}),w=t.computed((()=>o.value?o.value.messages.value:m.value)),P=t.computed((()=>f.value)),C=t.computed((()=>_.value));function D(){return o.value?o.value.getPostTranslationHandler():T}function A(e){o.value&&o.value.setPostTranslationHandler(e)}function S(){return o.value?o.value.getMissingHandler():L}function $(e){o.value&&o.value.setMissingHandler(e)}function U(e){return O(),e()}function x(...e){return o.value?U((()=>Reflect.apply(o.value.t,null,[...e]))):U((()=>""))}function H(...e){return o.value?Reflect.apply(o.value.rt,null,[...e]):""}function j(...e){return o.value?U((()=>Reflect.apply(o.value.d,null,[...e]))):U((()=>""))}function V(...e){return o.value?U((()=>Reflect.apply(o.value.n,null,[...e]))):U((()=>""))}function G(e){return o.value?o.value.tm(e):{}}function Y(e,t){return!!o.value&&o.value.te(e,t)}function B(e){return o.value?o.value.getLocaleMessage(e):{}}function X(e,t){o.value&&(o.value.setLocaleMessage(e,t),m.value[e]=t)}function z(e,t){o.value&&o.value.mergeLocaleMessage(e,t)}function J(e){return o.value?o.value.getDateTimeFormat(e):{}}function q(e,t){o.value&&(o.value.setDateTimeFormat(e,t),f.value[e]=t)}function Z(e,t){o.value&&o.value.mergeDateTimeFormat(e,t)}function K(e){return o.value?o.value.getNumberFormat(e):{}}function Q(e,t){o.value&&(o.value.setNumberFormat(e,t),_.value[e]=t)}function ee(e,t){o.value&&o.value.mergeNumberFormat(e,t)}const te={get id(){return o.value?o.value.id:-1},locale:M,fallbackLocale:W,messages:w,datetimeFormats:P,numberFormats:C,get inheritLocale(){return o.value?o.value.inheritLocale:s},set inheritLocale(e){o.value&&(o.value.inheritLocale=e)},get availableLocales(){return o.value?o.value.availableLocales:Object.keys(m.value)},get modifiers(){return o.value?o.value.modifiers:I},get pluralRules(){return o.value?o.value.pluralRules:R},get isGlobal(){return!!o.value&&o.value.isGlobal},get missingWarn(){return o.value?o.value.missingWarn:g},set missingWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackWarn(){return o.value?o.value.fallbackWarn:p},set fallbackWarn(e){o.value&&(o.value.missingWarn=e)},get fallbackRoot(){return o.value?o.value.fallbackRoot:v},set fallbackRoot(e){o.value&&(o.value.fallbackRoot=e)},get fallbackFormat(){return o.value?o.value.fallbackFormat:d},set fallbackFormat(e){o.value&&(o.value.fallbackFormat=e)},get warnHtmlMessage(){return o.value?o.value.warnHtmlMessage:F},set warnHtmlMessage(e){o.value&&(o.value.warnHtmlMessage=e)},get escapeParameter(){return o.value?o.value.escapeParameter:y},set escapeParameter(e){o.value&&(o.value.escapeParameter=e)},t:x,getPostTranslationHandler:D,setPostTranslationHandler:A,getMissingHandler:S,setMissingHandler:$,rt:H,d:j,n:V,tm:G,te:Y,getLocaleMessage:B,setLocaleMessage:X,mergeLocaleMessage:z,getDateTimeFormat:J,setDateTimeFormat:q,mergeDateTimeFormat:Z,getNumberFormat:K,setNumberFormat:Q,mergeNumberFormat:ee};function ae(e){e.locale.value=i.value,e.fallbackLocale.value=u.value,Object.keys(m.value).forEach((t=>{e.mergeLocaleMessage(t,m.value[t])})),Object.keys(f.value).forEach((t=>{e.mergeDateTimeFormat(t,f.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=y,e.fallbackFormat=d,e.fallbackRoot=v,e.fallbackWarn=p,e.missingWarn=g,e.warnHtmlMessage=F}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(Se.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=o.value=e.proxy.$i18n.__composer;"global"===a?(i.value=t.locale.value,u.value=t.fallbackLocale.value,m.value=t.messages.value,f.value=t.datetimeFormats.value,_.value=t.numberFormats.value):l&&ae(t)})),te}(a,o,r,e)}if("global"===o)return Xe(r,e,l),r;if("parent"===o){let t=function(e,t,a=!1){let n=null;const r=t.root;let l=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,a);for(;null!=l;){const t=e;if("composition"===e.mode)n=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(n=e.__composer,a&&n&&!n[je]&&(n=null))}if(null!=n)break;if(r===l)break;l=l.parent}return n}(n,a,e.__useComponent);return null==t&&(t=r),t}const s=n;let i=s.__getInstance(a);if(null==i){const n=m({},e);"__i18n"in l&&(n.__i18n=l.__i18n),r&&(n.__root=r),i=Qe(n),s.__composerExtend&&(i[Ve]=s.__composerExtend(i)),function(e,a,n){t.onMounted((()=>{}),a),t.onUnmounted((()=>{const t=n;e.__deleteInstance(a);const r=t[Ve];r&&(r(),delete t[Ve])}),a)}(s,a,i),s.__setInstance(a,i)}return i}const vt=["locale","fallbackLocale","availableLocales"],dt=["t","rt","d","n","tm","te"];return ce=function(e,t){if(!L(e))return null;let a=$.get(t);if(a||(a=function(e){const t=[];let a,n,r,l,o,s,i,c=-1,u=0,m=0;const f=[];function _(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,r="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=r:n+=r},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=S(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!_()){if(l=A(a),i=C[u],o=i[l]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(r=a,!1===s())))return;if(7===u)return t}}(t),a&&$.set(t,a)),!a)return null;const n=a.length;let r=e,l=0;for(;l<n;){const e=a[l];if(P.includes(e)&&W(r))return null;const t=r[e];if(void 0===t)return null;if(E(r))return null;r=t,l++}return r},ue=Q,e.DatetimeFormat=it,e.I18nD=ct,e.I18nInjectionKey=gt,e.I18nN=st,e.I18nT=rt,e.NumberFormat=ot,e.Translation=nt,e.VERSION=Ce,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(Se.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},a){const n=!k(e.legacy)||e.legacy,l=!k(e.globalInjection)||e.globalInjection,o=!n||!!e.allowComposition,s=new Map,[i,c]=function(e,a,n){const r=t.effectScope();{const t=a?r.run((()=>et(e))):r.run((()=>Qe(e)));if(null==t)throw Error(Se.UNEXPECTED_ERROR);return[r,t]}}(e,n),u=r("");{const e={get mode(){return n?"legacy":"composition"},get allowComposition(){return o},async install(a,...r){if(a.__VUE_I18N_SYMBOL__=u,a.provide(a.__VUE_I18N_SYMBOL__,e),N(r[0])){const t=r[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let o=null;!n&&l&&(o=function(e,a){const n=Object.create(null);vt.forEach((e=>{const r=Object.getOwnPropertyDescriptor(a,e);if(!r)throw Error(Se.UNEXPECTED_ERROR);const l=t.isRef(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,l)})),e.config.globalProperties.$i18n=n,dt.forEach((t=>{const n=Object.getOwnPropertyDescriptor(a,t);if(!n||!n.value)throw Error(Se.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,n)}));const r=()=>{delete e.config.globalProperties.$i18n,dt.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return r}(a,e.global)),function(e,t,...a){const n=N(a[0])?a[0]:{},r=!!n.useI18nComponentName;(!k(n.globalInstall)||n.globalInstall)&&([r?"i18n":nt.name,"I18nT"].forEach((t=>e.component(t,nt))),[ot.name,"I18nN"].forEach((t=>e.component(t,ot))),[it.name,"I18nD"].forEach((t=>e.component(t,it)))),e.directive("t",ut(t))}(a,e,...r),n&&a.mixin(function(e,a,n){return{beforeCreate(){const r=t.getCurrentInstance();if(!r)throw Error(Se.UNEXPECTED_ERROR);const l=this.$options;if(l.i18n){const t=l.i18n;if(l.__i18n&&(t.__i18n=l.__i18n),t.__root=a,this===this.$root)this.$i18n=_t(e,t);else{t.__injectWithOption=!0,t.__extender=n.__vueI18nExtend,this.$i18n=et(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(l.__i18n)if(this===this.$root)this.$i18n=_t(e,l);else{this.$i18n=et({__i18n:l.__i18n,__injectWithOption:!0,__extender:n.__vueI18nExtend,__root:a});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;l.__i18nGlobal&&Xe(a,l,l),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),n.__setInstance(r,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(Se.UNEXPECTED_ERROR);const a=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,a.__disposer&&(a.__disposer(),delete a.__disposer,delete a.__extender),n.__deleteInstance(e),delete this.$i18n}}}(c,c.__composer,e));const s=a.unmount;a.unmount=()=>{o&&o(),e.dispose(),s()}},get global(){return c},dispose(){i.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=pt,e.vTDirective=ut,e}({},Vue);
