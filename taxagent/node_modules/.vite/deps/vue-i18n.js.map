{"version": 3, "sources": ["../../@intlify/shared/dist/shared.mjs", "../../@intlify/message-compiler/dist/message-compiler.esm-browser.js", "../../@intlify/core-base/dist/core-base.mjs", "../../vue-i18n/dist/vue-i18n.mjs"], "sourcesContent": ["/*!\n  * shared v9.14.5\n  * (c) 2025 ka<PERSON><PERSON> kawa<PERSON>\n  * Released under the MIT License.\n  */\nfunction warn(msg, err) {\n    if (typeof console !== 'undefined') {\n        console.warn(`[intlify] ` + msg);\n        /* istanbul ignore if */\n        if (err) {\n            console.warn(err.stack);\n        }\n    }\n}\nconst hasWarned = {};\nfunction warnOnce(msg) {\n    if (!hasWarned[msg]) {\n        hasWarned[msg] = true;\n        warn(msg);\n    }\n}\n\n/**\n * Original Utilities\n * written by kazuya kawaguchi\n */\nconst inBrowser = typeof window !== 'undefined';\nlet mark;\nlet measure;\nif ((process.env.NODE_ENV !== 'production')) {\n    const perf = inBrowser && window.performance;\n    if (perf &&\n        perf.mark &&\n        perf.measure &&\n        perf.clearMarks &&\n        // @ts-ignore browser compat\n        perf.clearMeasures) {\n        mark = (tag) => {\n            perf.mark(tag);\n        };\n        measure = (name, startTag, endTag) => {\n            perf.measure(name, startTag, endTag);\n            perf.clearMarks(startTag);\n            perf.clearMarks(endTag);\n        };\n    }\n}\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n    if (args.length === 1 && isObject(args[0])) {\n        args = args[0];\n    }\n    if (!args || !args.hasOwnProperty) {\n        args = {};\n    }\n    return message.replace(RE_ARGS, (match, identifier) => {\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\n    });\n}\nconst makeSymbol = (name, shareable = false) => !shareable ? Symbol(name) : Symbol.for(name);\nconst generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });\nconst friendlyJSONstringify = (json) => JSON.stringify(json)\n    .replace(/\\u2028/g, '\\\\u2028')\n    .replace(/\\u2029/g, '\\\\u2029')\n    .replace(/\\u0027/g, '\\\\u0027');\nconst isNumber = (val) => typeof val === 'number' && isFinite(val);\nconst isDate = (val) => toTypeString(val) === '[object Date]';\nconst isRegExp = (val) => toTypeString(val) === '[object RegExp]';\nconst isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;\nconst assign = Object.assign;\nconst _create = Object.create;\nconst create = (obj = null) => _create(obj);\nlet _globalThis;\nconst getGlobalThis = () => {\n    // prettier-ignore\n    return (_globalThis ||\n        (_globalThis =\n            typeof globalThis !== 'undefined'\n                ? globalThis\n                : typeof self !== 'undefined'\n                    ? self\n                    : typeof window !== 'undefined'\n                        ? window\n                        : typeof global !== 'undefined'\n                            ? global\n                            : create()));\n};\nfunction escapeHtml(rawText) {\n    return rawText\n        .replace(/&/g, '&amp;') // escape `&` first to avoid double escaping\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&apos;')\n        .replace(/\\//g, '&#x2F;') // escape `/` to prevent closing tags or JavaScript URLs\n        .replace(/=/g, '&#x3D;'); // escape `=` to prevent attribute injection\n}\nfunction escapeAttributeValue(value) {\n    return value\n        .replace(/&(?![a-zA-Z0-9#]{2,6};)/g, '&amp;') // escape unescaped `&`\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&apos;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\nfunction sanitizeTranslatedHtml(html) {\n    // Escape dangerous characters in attribute values\n    // Process attributes with double quotes\n    html = html.replace(/(\\w+)\\s*=\\s*\"([^\"]*)\"/g, (_, attrName, attrValue) => `${attrName}=\"${escapeAttributeValue(attrValue)}\"`);\n    // Process attributes with single quotes\n    html = html.replace(/(\\w+)\\s*=\\s*'([^']*)'/g, (_, attrName, attrValue) => `${attrName}='${escapeAttributeValue(attrValue)}'`);\n    // Detect and neutralize event handler attributes\n    const eventHandlerPattern = /\\s*on\\w+\\s*=\\s*[\"']?[^\"'>]+[\"']?/gi;\n    if (eventHandlerPattern.test(html)) {\n        if ((process.env.NODE_ENV !== 'production')) {\n            warn('Potentially dangerous event handlers detected in translation. ' +\n                'Consider removing onclick, onerror, etc. from your translation messages.');\n        }\n        // Neutralize event handler attributes by escaping 'on'\n        html = html.replace(/(\\s+)(on)(\\w+\\s*=)/gi, '$1&#111;n$3');\n    }\n    // Disable javascript: URLs in various contexts\n    const javascriptUrlPattern = [\n        // In href, src, action, formaction attributes\n        /(\\s+(?:href|src|action|formaction)\\s*=\\s*[\"']?)\\s*javascript:/gi,\n        // In style attributes within url()\n        /(style\\s*=\\s*[\"'][^\"']*url\\s*\\(\\s*)javascript:/gi\n    ];\n    javascriptUrlPattern.forEach(pattern => {\n        html = html.replace(pattern, '$1javascript&#58;');\n    });\n    return html;\n}\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn(obj, key) {\n    return hasOwnProperty.call(obj, key);\n}\n/* eslint-enable */\n/**\n * Useful Utilities By Evan you\n * Modified by kazuya kawaguchi\n * MIT License\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts\n */\nconst isArray = Array.isArray;\nconst isFunction = (val) => typeof val === 'function';\nconst isString = (val) => typeof val === 'string';\nconst isBoolean = (val) => typeof val === 'boolean';\nconst isSymbol = (val) => typeof val === 'symbol';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = (val) => val !== null && typeof val === 'object';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isPromise = (val) => {\n    return isObject(val) && isFunction(val.then) && isFunction(val.catch);\n};\nconst objectToString = Object.prototype.toString;\nconst toTypeString = (value) => objectToString.call(value);\nconst isPlainObject = (val) => {\n    if (!isObject(val))\n        return false;\n    const proto = Object.getPrototypeOf(val);\n    return proto === null || proto.constructor === Object;\n};\n// for converting list and named values to displayed strings.\nconst toDisplayString = (val) => {\n    return val == null\n        ? ''\n        : isArray(val) || (isPlainObject(val) && val.toString === objectToString)\n            ? JSON.stringify(val, null, 2)\n            : String(val);\n};\nfunction join(items, separator = '') {\n    return items.reduce((str, item, index) => (index === 0 ? str + item : str + separator + item), '');\n}\nconst RANGE = 2;\nfunction generateCodeFrame(source, start = 0, end = source.length) {\n    const lines = source.split(/\\r?\\n/);\n    let count = 0;\n    const res = [];\n    for (let i = 0; i < lines.length; i++) {\n        count += lines[i].length + 1;\n        if (count >= start) {\n            for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {\n                if (j < 0 || j >= lines.length)\n                    continue;\n                const line = j + 1;\n                res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);\n                const lineLength = lines[j].length;\n                if (j === i) {\n                    // push underline\n                    const pad = start - (count - lineLength) + 1;\n                    const length = Math.max(1, end > count ? lineLength - pad : end - start);\n                    res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));\n                }\n                else if (j > i) {\n                    if (end > count) {\n                        const length = Math.max(Math.min(end - count, lineLength), 1);\n                        res.push(`   |  ` + '^'.repeat(length));\n                    }\n                    count += lineLength + 1;\n                }\n            }\n            break;\n        }\n    }\n    return res.join('\\n');\n}\nfunction incrementer(code) {\n    let current = code;\n    return () => ++current;\n}\n\n/**\n * Event emitter, forked from the below:\n * - original repository url: https://github.com/developit/mitt\n * - code url: https://github.com/developit/mitt/blob/master/src/index.ts\n * - author: Jason Miller (https://github.com/developit)\n * - license: MIT\n */\n/**\n * Create a event emitter\n *\n * @returns An event emitter\n */\nfunction createEmitter() {\n    const events = new Map();\n    const emitter = {\n        events,\n        on(event, handler) {\n            const handlers = events.get(event);\n            const added = handlers && handlers.push(handler);\n            if (!added) {\n                events.set(event, [handler]);\n            }\n        },\n        off(event, handler) {\n            const handlers = events.get(event);\n            if (handlers) {\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n            }\n        },\n        emit(event, payload) {\n            (events.get(event) || [])\n                .slice()\n                .map(handler => handler(payload));\n            (events.get('*') || [])\n                .slice()\n                .map(handler => handler(event, payload));\n        }\n    };\n    return emitter;\n}\n\nconst isNotObjectOrIsArray = (val) => !isObject(val) || isArray(val);\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nfunction deepCopy(src, des) {\n    // src and des should both be objects, and none of them can be a array\n    if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {\n        throw new Error('Invalid value');\n    }\n    const stack = [{ src, des }];\n    while (stack.length) {\n        const { src, des } = stack.pop();\n        // using `Object.keys` which skips prototype properties\n        Object.keys(src).forEach(key => {\n            if (key === '__proto__') {\n                return;\n            }\n            // if src[key] is an object/array, set des[key]\n            // to empty object/array to prevent setting by reference\n            if (isObject(src[key]) && !isObject(des[key])) {\n                des[key] = Array.isArray(src[key]) ? [] : create();\n            }\n            if (isNotObjectOrIsArray(des[key]) || isNotObjectOrIsArray(src[key])) {\n                // replace with src[key] when:\n                // src[key] or des[key] is not an object, or\n                // src[key] or des[key] is an array\n                des[key] = src[key];\n            }\n            else {\n                // src[key] and des[key] are both objects, merge them\n                stack.push({ src: src[key], des: des[key] });\n            }\n        });\n    }\n}\n\nexport { assign, create, createEmitter, deepCopy, escapeHtml, format, friendlyJSONstringify, generateCodeFrame, generateFormatCacheKey, getGlobalThis, hasOwn, inBrowser, incrementer, isArray, isBoolean, isDate, isEmptyObject, isFunction, isNumber, isObject, isPlainObject, isPromise, isRegExp, isString, isSymbol, join, makeSymbol, mark, measure, objectToString, sanitizeTranslatedHtml, toDisplayString, toTypeString, warn, warnOnce };\n", "/*!\n  * message-compiler v9.14.5\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nconst LOCATION_STUB = {\n    start: { line: 1, column: 1, offset: 0 },\n    end: { line: 1, column: 1, offset: 0 }\n};\nfunction createPosition(line, column, offset) {\n    return { line, column, offset };\n}\nfunction createLocation(start, end, source) {\n    const loc = { start, end };\n    if (source != null) {\n        loc.source = source;\n    }\n    return loc;\n}\n\n/**\n * Original Utilities\n * written by kazuya kawaguchi\n */\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n    if (args.length === 1 && isObject(args[0])) {\n        args = args[0];\n    }\n    if (!args || !args.hasOwnProperty) {\n        args = {};\n    }\n    return message.replace(RE_ARGS, (match, identifier) => {\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\n    });\n}\nconst assign = Object.assign;\nconst isString = (val) => typeof val === 'string';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = (val) => val !== null && typeof val === 'object';\nfunction join(items, separator = '') {\n    return items.reduce((str, item, index) => (index === 0 ? str + item : str + separator + item), '');\n}\n\nconst CompileWarnCodes = {\n    USE_MODULO_SYNTAX: 1,\n    __EXTEND_POINT__: 2\n};\n/** @internal */\nconst warnMessages = {\n    [CompileWarnCodes.USE_MODULO_SYNTAX]: `Use modulo before '{{0}}'.`\n};\nfunction createCompileWarn(code, loc, ...args) {\n    const msg = format(warnMessages[code] || '', ...(args || [])) ;\n    const message = { message: String(msg), code };\n    if (loc) {\n        message.location = loc;\n    }\n    return message;\n}\n\nconst CompileErrorCodes = {\n    // tokenizer error codes\n    EXPECTED_TOKEN: 1,\n    INVALID_TOKEN_IN_PLACEHOLDER: 2,\n    UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,\n    UNKNOWN_ESCAPE_SEQUENCE: 4,\n    INVALID_UNICODE_ESCAPE_SEQUENCE: 5,\n    UNBALANCED_CLOSING_BRACE: 6,\n    UNTERMINATED_CLOSING_BRACE: 7,\n    EMPTY_PLACEHOLDER: 8,\n    NOT_ALLOW_NEST_PLACEHOLDER: 9,\n    INVALID_LINKED_FORMAT: 10,\n    // parser error codes\n    MUST_HAVE_MESSAGES_IN_PLURAL: 11,\n    UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,\n    UNEXPECTED_EMPTY_LINKED_KEY: 13,\n    UNEXPECTED_LEXICAL_ANALYSIS: 14,\n    // generator error codes\n    UNHANDLED_CODEGEN_NODE_TYPE: 15,\n    // minifier error codes\n    UNHANDLED_MINIFIER_NODE_TYPE: 16,\n    // Special value for higher-order compilers to pick up the last code\n    // to avoid collision of error codes. This should always be kept as the last\n    // item.\n    __EXTEND_POINT__: 17\n};\n/** @internal */\nconst errorMessages = {\n    // tokenizer error messages\n    [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,\n    [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,\n    [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,\n    [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\\\{0}`,\n    [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,\n    [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,\n    [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,\n    [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,\n    [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,\n    [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,\n    // parser error messages\n    [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,\n    [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,\n    // generator error messages\n    [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,\n    // minimizer error messages\n    [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`\n};\nfunction createCompileError(code, loc, options = {}) {\n    const { domain, messages, args } = options;\n    const msg = format((messages || errorMessages)[code] || '', ...(args || []))\n        ;\n    const error = new SyntaxError(String(msg));\n    error.code = code;\n    if (loc) {\n        error.location = loc;\n    }\n    error.domain = domain;\n    return error;\n}\n/** @internal */\nfunction defaultOnError(error) {\n    throw error;\n}\n\n// eslint-disable-next-line no-useless-escape\nconst RE_HTML_TAG = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\nconst detectHtmlTag = (source) => RE_HTML_TAG.test(source);\n\nconst CHAR_SP = ' ';\nconst CHAR_CR = '\\r';\nconst CHAR_LF = '\\n';\nconst CHAR_LS = String.fromCharCode(0x2028);\nconst CHAR_PS = String.fromCharCode(0x2029);\nfunction createScanner(str) {\n    const _buf = str;\n    let _index = 0;\n    let _line = 1;\n    let _column = 1;\n    let _peekOffset = 0;\n    const isCRLF = (index) => _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\n    const isLF = (index) => _buf[index] === CHAR_LF;\n    const isPS = (index) => _buf[index] === CHAR_PS;\n    const isLS = (index) => _buf[index] === CHAR_LS;\n    const isLineEnd = (index) => isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\n    const index = () => _index;\n    const line = () => _line;\n    const column = () => _column;\n    const peekOffset = () => _peekOffset;\n    const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\n    const currentChar = () => charAt(_index);\n    const currentPeek = () => charAt(_index + _peekOffset);\n    function next() {\n        _peekOffset = 0;\n        if (isLineEnd(_index)) {\n            _line++;\n            _column = 0;\n        }\n        if (isCRLF(_index)) {\n            _index++;\n        }\n        _index++;\n        _column++;\n        return _buf[_index];\n    }\n    function peek() {\n        if (isCRLF(_index + _peekOffset)) {\n            _peekOffset++;\n        }\n        _peekOffset++;\n        return _buf[_index + _peekOffset];\n    }\n    function reset() {\n        _index = 0;\n        _line = 1;\n        _column = 1;\n        _peekOffset = 0;\n    }\n    function resetPeek(offset = 0) {\n        _peekOffset = offset;\n    }\n    function skipToPeek() {\n        const target = _index + _peekOffset;\n        // eslint-disable-next-line no-unmodified-loop-condition\n        while (target !== _index) {\n            next();\n        }\n        _peekOffset = 0;\n    }\n    return {\n        index,\n        line,\n        column,\n        peekOffset,\n        charAt,\n        currentChar,\n        currentPeek,\n        next,\n        peek,\n        reset,\n        resetPeek,\n        skipToPeek\n    };\n}\n\nconst EOF = undefined;\nconst DOT = '.';\nconst LITERAL_DELIMITER = \"'\";\nconst ERROR_DOMAIN$3 = 'tokenizer';\nfunction createTokenizer(source, options = {}) {\n    const location = options.location !== false;\n    const _scnr = createScanner(source);\n    const currentOffset = () => _scnr.index();\n    const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());\n    const _initLoc = currentPosition();\n    const _initOffset = currentOffset();\n    const _context = {\n        currentType: 14 /* TokenTypes.EOF */,\n        offset: _initOffset,\n        startLoc: _initLoc,\n        endLoc: _initLoc,\n        lastType: 14 /* TokenTypes.EOF */,\n        lastOffset: _initOffset,\n        lastStartLoc: _initLoc,\n        lastEndLoc: _initLoc,\n        braceNest: 0,\n        inLinked: false,\n        text: ''\n    };\n    const context = () => _context;\n    const { onError } = options;\n    function emitError(code, pos, offset, ...args) {\n        const ctx = context();\n        pos.column += offset;\n        pos.offset += offset;\n        if (onError) {\n            const loc = location ? createLocation(ctx.startLoc, pos) : null;\n            const err = createCompileError(code, loc, {\n                domain: ERROR_DOMAIN$3,\n                args\n            });\n            onError(err);\n        }\n    }\n    function getToken(context, type, value) {\n        context.endLoc = currentPosition();\n        context.currentType = type;\n        const token = { type };\n        if (location) {\n            token.loc = createLocation(context.startLoc, context.endLoc);\n        }\n        if (value != null) {\n            token.value = value;\n        }\n        return token;\n    }\n    const getEndToken = (context) => getToken(context, 14 /* TokenTypes.EOF */);\n    function eat(scnr, ch) {\n        if (scnr.currentChar() === ch) {\n            scnr.next();\n            return ch;\n        }\n        else {\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n            return '';\n        }\n    }\n    function peekSpaces(scnr) {\n        let buf = '';\n        while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\n            buf += scnr.currentPeek();\n            scnr.peek();\n        }\n        return buf;\n    }\n    function skipSpaces(scnr) {\n        const buf = peekSpaces(scnr);\n        scnr.skipToPeek();\n        return buf;\n    }\n    function isIdentifierStart(ch) {\n        if (ch === EOF) {\n            return false;\n        }\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            cc === 95 // _\n        );\n    }\n    function isNumberStart(ch) {\n        if (ch === EOF) {\n            return false;\n        }\n        const cc = ch.charCodeAt(0);\n        return cc >= 48 && cc <= 57; // 0-9\n    }\n    function isNamedIdentifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = isIdentifierStart(scnr.currentPeek());\n        scnr.resetPeek();\n        return ret;\n    }\n    function isListIdentifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\n        const ret = isNumberStart(ch);\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLiteralStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === LITERAL_DELIMITER;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedDotStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 8 /* TokenTypes.LinkedAlias */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \".\" /* TokenChars.LinkedDot */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedModifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 9 /* TokenTypes.LinkedDot */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = isIdentifierStart(scnr.currentPeek());\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedDelimiterStart(scnr, context) {\n        const { currentType } = context;\n        if (!(currentType === 8 /* TokenTypes.LinkedAlias */ ||\n            currentType === 12 /* TokenTypes.LinkedModifier */)) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \":\" /* TokenChars.LinkedDelimiter */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedReferStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 10 /* TokenTypes.LinkedDelimiter */) {\n            return false;\n        }\n        const fn = () => {\n            const ch = scnr.currentPeek();\n            if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                return isIdentifierStart(scnr.peek());\n            }\n            else if (ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"%\" /* TokenChars.Modulo */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                ch === \":\" /* TokenChars.LinkedDelimiter */ ||\n                ch === \".\" /* TokenChars.LinkedDot */ ||\n                ch === CHAR_SP ||\n                !ch) {\n                return false;\n            }\n            else if (ch === CHAR_LF) {\n                scnr.peek();\n                return fn();\n            }\n            else {\n                // other characters\n                return isTextStart(scnr, false);\n            }\n        };\n        const ret = fn();\n        scnr.resetPeek();\n        return ret;\n    }\n    function isPluralStart(scnr) {\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \"|\" /* TokenChars.Pipe */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function detectModuloStart(scnr) {\n        const spaces = peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \"%\" /* TokenChars.Modulo */ &&\n            scnr.peek() === \"{\" /* TokenChars.BraceLeft */;\n        scnr.resetPeek();\n        return {\n            isModulo: ret,\n            hasSpace: spaces.length > 0\n        };\n    }\n    function isTextStart(scnr, reset = true) {\n        const fn = (hasSpace = false, prev = '', detectModulo = false) => {\n            const ch = scnr.currentPeek();\n            if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                return prev === \"%\" /* TokenChars.Modulo */ ? false : hasSpace;\n            }\n            else if (ch === \"@\" /* TokenChars.LinkedAlias */ || !ch) {\n                return prev === \"%\" /* TokenChars.Modulo */ ? true : hasSpace;\n            }\n            else if (ch === \"%\" /* TokenChars.Modulo */) {\n                scnr.peek();\n                return fn(hasSpace, \"%\" /* TokenChars.Modulo */, true);\n            }\n            else if (ch === \"|\" /* TokenChars.Pipe */) {\n                return prev === \"%\" /* TokenChars.Modulo */ || detectModulo\n                    ? true\n                    : !(prev === CHAR_SP || prev === CHAR_LF);\n            }\n            else if (ch === CHAR_SP) {\n                scnr.peek();\n                return fn(true, CHAR_SP, detectModulo);\n            }\n            else if (ch === CHAR_LF) {\n                scnr.peek();\n                return fn(true, CHAR_LF, detectModulo);\n            }\n            else {\n                return true;\n            }\n        };\n        const ret = fn();\n        reset && scnr.resetPeek();\n        return ret;\n    }\n    function takeChar(scnr, fn) {\n        const ch = scnr.currentChar();\n        if (ch === EOF) {\n            return EOF;\n        }\n        if (fn(ch)) {\n            scnr.next();\n            return ch;\n        }\n        return null;\n    }\n    function isIdentifier(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            (cc >= 48 && cc <= 57) || // 0-9\n            cc === 95 || // _\n            cc === 36 // $\n        );\n    }\n    function takeIdentifierChar(scnr) {\n        return takeChar(scnr, isIdentifier);\n    }\n    function isNamedIdentifier(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            (cc >= 48 && cc <= 57) || // 0-9\n            cc === 95 || // _\n            cc === 36 || // $\n            cc === 45 // -\n        );\n    }\n    function takeNamedIdentifierChar(scnr) {\n        return takeChar(scnr, isNamedIdentifier);\n    }\n    function isDigit(ch) {\n        const cc = ch.charCodeAt(0);\n        return cc >= 48 && cc <= 57; // 0-9\n    }\n    function takeDigit(scnr) {\n        return takeChar(scnr, isDigit);\n    }\n    function isHexDigit(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 48 && cc <= 57) || // 0-9\n            (cc >= 65 && cc <= 70) || // A-F\n            (cc >= 97 && cc <= 102)); // a-f\n    }\n    function takeHexDigit(scnr) {\n        return takeChar(scnr, isHexDigit);\n    }\n    function getDigits(scnr) {\n        let ch = '';\n        let num = '';\n        while ((ch = takeDigit(scnr))) {\n            num += ch;\n        }\n        return num;\n    }\n    function readModulo(scnr) {\n        skipSpaces(scnr);\n        const ch = scnr.currentChar();\n        if (ch !== \"%\" /* TokenChars.Modulo */) {\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n        }\n        scnr.next();\n        return \"%\" /* TokenChars.Modulo */;\n    }\n    function readText(scnr) {\n        let buf = '';\n        // eslint-disable-next-line no-constant-condition\n        while (true) {\n            const ch = scnr.currentChar();\n            if (ch === \"{\" /* TokenChars.BraceLeft */ ||\n                ch === \"}\" /* TokenChars.BraceRight */ ||\n                ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                !ch) {\n                break;\n            }\n            else if (ch === \"%\" /* TokenChars.Modulo */) {\n                if (isTextStart(scnr)) {\n                    buf += ch;\n                    scnr.next();\n                }\n                else {\n                    break;\n                }\n            }\n            else if (ch === CHAR_SP || ch === CHAR_LF) {\n                if (isTextStart(scnr)) {\n                    buf += ch;\n                    scnr.next();\n                }\n                else if (isPluralStart(scnr)) {\n                    break;\n                }\n                else {\n                    buf += ch;\n                    scnr.next();\n                }\n            }\n            else {\n                buf += ch;\n                scnr.next();\n            }\n        }\n        return buf;\n    }\n    function readNamedIdentifier(scnr) {\n        skipSpaces(scnr);\n        let ch = '';\n        let name = '';\n        while ((ch = takeNamedIdentifierChar(scnr))) {\n            name += ch;\n        }\n        if (scnr.currentChar() === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        return name;\n    }\n    function readListIdentifier(scnr) {\n        skipSpaces(scnr);\n        let value = '';\n        if (scnr.currentChar() === '-') {\n            scnr.next();\n            value += `-${getDigits(scnr)}`;\n        }\n        else {\n            value += getDigits(scnr);\n        }\n        if (scnr.currentChar() === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        return value;\n    }\n    function isLiteral(ch) {\n        return ch !== LITERAL_DELIMITER && ch !== CHAR_LF;\n    }\n    function readLiteral(scnr) {\n        skipSpaces(scnr);\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n        let ch = '';\n        let literal = '';\n        while ((ch = takeChar(scnr, isLiteral))) {\n            if (ch === '\\\\') {\n                literal += readEscapeSequence(scnr);\n            }\n            else {\n                literal += ch;\n            }\n        }\n        const current = scnr.currentChar();\n        if (current === CHAR_LF || current === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);\n            // TODO: Is it correct really?\n            if (current === CHAR_LF) {\n                scnr.next();\n                // eslint-disable-next-line no-useless-escape\n                eat(scnr, `\\'`);\n            }\n            return literal;\n        }\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n        return literal;\n    }\n    function readEscapeSequence(scnr) {\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case '\\\\':\n            case `\\'`: // eslint-disable-line no-useless-escape\n                scnr.next();\n                return `\\\\${ch}`;\n            case 'u':\n                return readUnicodeEscapeSequence(scnr, ch, 4);\n            case 'U':\n                return readUnicodeEscapeSequence(scnr, ch, 6);\n            default:\n                emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);\n                return '';\n        }\n    }\n    function readUnicodeEscapeSequence(scnr, unicode, digits) {\n        eat(scnr, unicode);\n        let sequence = '';\n        for (let i = 0; i < digits; i++) {\n            const ch = takeHexDigit(scnr);\n            if (!ch) {\n                emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\\\${unicode}${sequence}${scnr.currentChar()}`);\n                break;\n            }\n            sequence += ch;\n        }\n        return `\\\\${unicode}${sequence}`;\n    }\n    function isInvalidIdentifier(ch) {\n        return (ch !== \"{\" /* TokenChars.BraceLeft */ &&\n            ch !== \"}\" /* TokenChars.BraceRight */ &&\n            ch !== CHAR_SP &&\n            ch !== CHAR_LF);\n    }\n    function readInvalidIdentifier(scnr) {\n        skipSpaces(scnr);\n        let ch = '';\n        let identifiers = '';\n        while ((ch = takeChar(scnr, isInvalidIdentifier))) {\n            identifiers += ch;\n        }\n        return identifiers;\n    }\n    function readLinkedModifier(scnr) {\n        let ch = '';\n        let name = '';\n        while ((ch = takeIdentifierChar(scnr))) {\n            name += ch;\n        }\n        return name;\n    }\n    function readLinkedRefer(scnr) {\n        const fn = (buf) => {\n            const ch = scnr.currentChar();\n            if (ch === \"{\" /* TokenChars.BraceLeft */ ||\n                ch === \"%\" /* TokenChars.Modulo */ ||\n                ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                ch === \"(\" /* TokenChars.ParenLeft */ ||\n                ch === \")\" /* TokenChars.ParenRight */ ||\n                !ch) {\n                return buf;\n            }\n            else if (ch === CHAR_SP) {\n                return buf;\n            }\n            else if (ch === CHAR_LF || ch === DOT) {\n                buf += ch;\n                scnr.next();\n                return fn(buf);\n            }\n            else {\n                buf += ch;\n                scnr.next();\n                return fn(buf);\n            }\n        };\n        return fn('');\n    }\n    function readPlural(scnr) {\n        skipSpaces(scnr);\n        const plural = eat(scnr, \"|\" /* TokenChars.Pipe */);\n        skipSpaces(scnr);\n        return plural;\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readTokenInPlaceholder(scnr, context) {\n        let token = null;\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case \"{\" /* TokenChars.BraceLeft */:\n                if (context.braceNest >= 1) {\n                    emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);\n                }\n                scnr.next();\n                token = getToken(context, 2 /* TokenTypes.BraceLeft */, \"{\" /* TokenChars.BraceLeft */);\n                skipSpaces(scnr);\n                context.braceNest++;\n                return token;\n            case \"}\" /* TokenChars.BraceRight */:\n                if (context.braceNest > 0 &&\n                    context.currentType === 2 /* TokenTypes.BraceLeft */) {\n                    emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);\n                }\n                scnr.next();\n                token = getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n                context.braceNest--;\n                context.braceNest > 0 && skipSpaces(scnr);\n                if (context.inLinked && context.braceNest === 0) {\n                    context.inLinked = false;\n                }\n                return token;\n            case \"@\" /* TokenChars.LinkedAlias */:\n                if (context.braceNest > 0) {\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                }\n                token = readTokenInLinked(scnr, context) || getEndToken(context);\n                context.braceNest = 0;\n                return token;\n            default: {\n                let validNamedIdentifier = true;\n                let validListIdentifier = true;\n                let validLiteral = true;\n                if (isPluralStart(scnr)) {\n                    if (context.braceNest > 0) {\n                        emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                    }\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (context.braceNest > 0 &&\n                    (context.currentType === 5 /* TokenTypes.Named */ ||\n                        context.currentType === 6 /* TokenTypes.List */ ||\n                        context.currentType === 7 /* TokenTypes.Literal */)) {\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                    context.braceNest = 0;\n                    return readToken(scnr, context);\n                }\n                if ((validNamedIdentifier = isNamedIdentifierStart(scnr, context))) {\n                    token = getToken(context, 5 /* TokenTypes.Named */, readNamedIdentifier(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if ((validListIdentifier = isListIdentifierStart(scnr, context))) {\n                    token = getToken(context, 6 /* TokenTypes.List */, readListIdentifier(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if ((validLiteral = isLiteralStart(scnr, context))) {\n                    token = getToken(context, 7 /* TokenTypes.Literal */, readLiteral(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\n                    // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\n                    token = getToken(context, 13 /* TokenTypes.InvalidPlace */, readInvalidIdentifier(scnr));\n                    emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);\n                    skipSpaces(scnr);\n                    return token;\n                }\n                break;\n            }\n        }\n        return token;\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readTokenInLinked(scnr, context) {\n        const { currentType } = context;\n        let token = null;\n        const ch = scnr.currentChar();\n        if ((currentType === 8 /* TokenTypes.LinkedAlias */ ||\n            currentType === 9 /* TokenTypes.LinkedDot */ ||\n            currentType === 12 /* TokenTypes.LinkedModifier */ ||\n            currentType === 10 /* TokenTypes.LinkedDelimiter */) &&\n            (ch === CHAR_LF || ch === CHAR_SP)) {\n            emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n        }\n        switch (ch) {\n            case \"@\" /* TokenChars.LinkedAlias */:\n                scnr.next();\n                token = getToken(context, 8 /* TokenTypes.LinkedAlias */, \"@\" /* TokenChars.LinkedAlias */);\n                context.inLinked = true;\n                return token;\n            case \".\" /* TokenChars.LinkedDot */:\n                skipSpaces(scnr);\n                scnr.next();\n                return getToken(context, 9 /* TokenTypes.LinkedDot */, \".\" /* TokenChars.LinkedDot */);\n            case \":\" /* TokenChars.LinkedDelimiter */:\n                skipSpaces(scnr);\n                scnr.next();\n                return getToken(context, 10 /* TokenTypes.LinkedDelimiter */, \":\" /* TokenChars.LinkedDelimiter */);\n            default:\n                if (isPluralStart(scnr)) {\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (isLinkedDotStart(scnr, context) ||\n                    isLinkedDelimiterStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    return readTokenInLinked(scnr, context);\n                }\n                if (isLinkedModifierStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    return getToken(context, 12 /* TokenTypes.LinkedModifier */, readLinkedModifier(scnr));\n                }\n                if (isLinkedReferStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                        // scan the placeholder\n                        return readTokenInPlaceholder(scnr, context) || token;\n                    }\n                    else {\n                        return getToken(context, 11 /* TokenTypes.LinkedKey */, readLinkedRefer(scnr));\n                    }\n                }\n                if (currentType === 8 /* TokenTypes.LinkedAlias */) {\n                    emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n                }\n                context.braceNest = 0;\n                context.inLinked = false;\n                return readToken(scnr, context);\n        }\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readToken(scnr, context) {\n        let token = { type: 14 /* TokenTypes.EOF */ };\n        if (context.braceNest > 0) {\n            return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n        }\n        if (context.inLinked) {\n            return readTokenInLinked(scnr, context) || getEndToken(context);\n        }\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case \"{\" /* TokenChars.BraceLeft */:\n                return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n            case \"}\" /* TokenChars.BraceRight */:\n                emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);\n                scnr.next();\n                return getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n            case \"@\" /* TokenChars.LinkedAlias */:\n                return readTokenInLinked(scnr, context) || getEndToken(context);\n            default: {\n                if (isPluralStart(scnr)) {\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                const { isModulo, hasSpace } = detectModuloStart(scnr);\n                if (isModulo) {\n                    return hasSpace\n                        ? getToken(context, 0 /* TokenTypes.Text */, readText(scnr))\n                        : getToken(context, 4 /* TokenTypes.Modulo */, readModulo(scnr));\n                }\n                if (isTextStart(scnr)) {\n                    return getToken(context, 0 /* TokenTypes.Text */, readText(scnr));\n                }\n                break;\n            }\n        }\n        return token;\n    }\n    function nextToken() {\n        const { currentType, offset, startLoc, endLoc } = _context;\n        _context.lastType = currentType;\n        _context.lastOffset = offset;\n        _context.lastStartLoc = startLoc;\n        _context.lastEndLoc = endLoc;\n        _context.offset = currentOffset();\n        _context.startLoc = currentPosition();\n        if (_scnr.currentChar() === EOF) {\n            return getToken(_context, 14 /* TokenTypes.EOF */);\n        }\n        return readToken(_scnr, _context);\n    }\n    return {\n        nextToken,\n        currentOffset,\n        currentPosition,\n        context\n    };\n}\n\nconst ERROR_DOMAIN$2 = 'parser';\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\nconst KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\n    switch (match) {\n        case `\\\\\\\\`:\n            return `\\\\`;\n        // eslint-disable-next-line no-useless-escape\n        case `\\\\\\'`:\n            // eslint-disable-next-line no-useless-escape\n            return `\\'`;\n        default: {\n            const codePoint = parseInt(codePoint4 || codePoint6, 16);\n            if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\n                return String.fromCodePoint(codePoint);\n            }\n            // invalid ...\n            // Replace them with U+FFFD REPLACEMENT CHARACTER.\n            return '�';\n        }\n    }\n}\nfunction createParser(options = {}) {\n    const location = options.location !== false;\n    const { onError, onWarn } = options;\n    function emitError(tokenzer, code, start, offset, ...args) {\n        const end = tokenzer.currentPosition();\n        end.offset += offset;\n        end.column += offset;\n        if (onError) {\n            const loc = location ? createLocation(start, end) : null;\n            const err = createCompileError(code, loc, {\n                domain: ERROR_DOMAIN$2,\n                args\n            });\n            onError(err);\n        }\n    }\n    function emitWarn(tokenzer, code, start, offset, ...args) {\n        const end = tokenzer.currentPosition();\n        end.offset += offset;\n        end.column += offset;\n        if (onWarn) {\n            const loc = location ? createLocation(start, end) : null;\n            onWarn(createCompileWarn(code, loc, args));\n        }\n    }\n    function startNode(type, offset, loc) {\n        const node = { type };\n        if (location) {\n            node.start = offset;\n            node.end = offset;\n            node.loc = { start: loc, end: loc };\n        }\n        return node;\n    }\n    function endNode(node, offset, pos, type) {\n        if (type) {\n            node.type = type;\n        }\n        if (location) {\n            node.end = offset;\n            if (node.loc) {\n                node.loc.end = pos;\n            }\n        }\n    }\n    function parseText(tokenizer, value) {\n        const context = tokenizer.context();\n        const node = startNode(3 /* NodeTypes.Text */, context.offset, context.startLoc);\n        node.value = value;\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseList(tokenizer, index) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(5 /* NodeTypes.List */, offset, loc);\n        node.index = parseInt(index, 10);\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseNamed(tokenizer, key, modulo) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(4 /* NodeTypes.Named */, offset, loc);\n        node.key = key;\n        if (modulo === true) {\n            node.modulo = true;\n        }\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLiteral(tokenizer, value) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(9 /* NodeTypes.Literal */, offset, loc);\n        node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLinkedModifier(tokenizer) {\n        const token = tokenizer.nextToken();\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get linked dot loc\n        const node = startNode(8 /* NodeTypes.LinkedModifier */, offset, loc);\n        if (token.type !== 12 /* TokenTypes.LinkedModifier */) {\n            // empty modifier\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);\n            node.value = '';\n            endNode(node, offset, loc);\n            return {\n                nextConsumeToken: token,\n                node\n            };\n        }\n        // check token\n        if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        node.value = token.value || '';\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return {\n            node\n        };\n    }\n    function parseLinkedKey(tokenizer, value) {\n        const context = tokenizer.context();\n        const node = startNode(7 /* NodeTypes.LinkedKey */, context.offset, context.startLoc);\n        node.value = value;\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLinked(tokenizer) {\n        const context = tokenizer.context();\n        const linkedNode = startNode(6 /* NodeTypes.Linked */, context.offset, context.startLoc);\n        let token = tokenizer.nextToken();\n        if (token.type === 9 /* TokenTypes.LinkedDot */) {\n            const parsed = parseLinkedModifier(tokenizer);\n            linkedNode.modifier = parsed.node;\n            token = parsed.nextConsumeToken || tokenizer.nextToken();\n        }\n        // asset check token\n        if (token.type !== 10 /* TokenTypes.LinkedDelimiter */) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        token = tokenizer.nextToken();\n        // skip brace left\n        if (token.type === 2 /* TokenTypes.BraceLeft */) {\n            token = tokenizer.nextToken();\n        }\n        switch (token.type) {\n            case 11 /* TokenTypes.LinkedKey */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\n                break;\n            case 5 /* TokenTypes.Named */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseNamed(tokenizer, token.value || '');\n                break;\n            case 6 /* TokenTypes.List */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseList(tokenizer, token.value || '');\n                break;\n            case 7 /* TokenTypes.Literal */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseLiteral(tokenizer, token.value || '');\n                break;\n            default: {\n                // empty key\n                emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);\n                const nextContext = tokenizer.context();\n                const emptyLinkedKeyNode = startNode(7 /* NodeTypes.LinkedKey */, nextContext.offset, nextContext.startLoc);\n                emptyLinkedKeyNode.value = '';\n                endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\n                linkedNode.key = emptyLinkedKeyNode;\n                endNode(linkedNode, nextContext.offset, nextContext.startLoc);\n                return {\n                    nextConsumeToken: token,\n                    node: linkedNode\n                };\n            }\n        }\n        endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return {\n            node: linkedNode\n        };\n    }\n    function parseMessage(tokenizer) {\n        const context = tokenizer.context();\n        const startOffset = context.currentType === 1 /* TokenTypes.Pipe */\n            ? tokenizer.currentOffset()\n            : context.offset;\n        const startLoc = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.endLoc\n            : context.startLoc;\n        const node = startNode(2 /* NodeTypes.Message */, startOffset, startLoc);\n        node.items = [];\n        let nextToken = null;\n        let modulo = null;\n        do {\n            const token = nextToken || tokenizer.nextToken();\n            nextToken = null;\n            switch (token.type) {\n                case 0 /* TokenTypes.Text */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseText(tokenizer, token.value || ''));\n                    break;\n                case 6 /* TokenTypes.List */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseList(tokenizer, token.value || ''));\n                    break;\n                case 4 /* TokenTypes.Modulo */:\n                    modulo = true;\n                    break;\n                case 5 /* TokenTypes.Named */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseNamed(tokenizer, token.value || '', !!modulo));\n                    if (modulo) {\n                        emitWarn(tokenizer, CompileWarnCodes.USE_MODULO_SYNTAX, context.lastStartLoc, 0, getTokenCaption(token));\n                        modulo = null;\n                    }\n                    break;\n                case 7 /* TokenTypes.Literal */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseLiteral(tokenizer, token.value || ''));\n                    break;\n                case 8 /* TokenTypes.LinkedAlias */: {\n                    const parsed = parseLinked(tokenizer);\n                    node.items.push(parsed.node);\n                    nextToken = parsed.nextConsumeToken || null;\n                    break;\n                }\n            }\n        } while (context.currentType !== 14 /* TokenTypes.EOF */ &&\n            context.currentType !== 1 /* TokenTypes.Pipe */);\n        // adjust message node loc\n        const endOffset = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.lastOffset\n            : tokenizer.currentOffset();\n        const endLoc = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.lastEndLoc\n            : tokenizer.currentPosition();\n        endNode(node, endOffset, endLoc);\n        return node;\n    }\n    function parsePlural(tokenizer, offset, loc, msgNode) {\n        const context = tokenizer.context();\n        let hasEmptyMessage = msgNode.items.length === 0;\n        const node = startNode(1 /* NodeTypes.Plural */, offset, loc);\n        node.cases = [];\n        node.cases.push(msgNode);\n        do {\n            const msg = parseMessage(tokenizer);\n            if (!hasEmptyMessage) {\n                hasEmptyMessage = msg.items.length === 0;\n            }\n            node.cases.push(msg);\n        } while (context.currentType !== 14 /* TokenTypes.EOF */);\n        if (hasEmptyMessage) {\n            emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);\n        }\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseResource(tokenizer) {\n        const context = tokenizer.context();\n        const { offset, startLoc } = context;\n        const msgNode = parseMessage(tokenizer);\n        if (context.currentType === 14 /* TokenTypes.EOF */) {\n            return msgNode;\n        }\n        else {\n            return parsePlural(tokenizer, offset, startLoc, msgNode);\n        }\n    }\n    function parse(source) {\n        const tokenizer = createTokenizer(source, assign({}, options));\n        const context = tokenizer.context();\n        const node = startNode(0 /* NodeTypes.Resource */, context.offset, context.startLoc);\n        if (location && node.loc) {\n            node.loc.source = source;\n        }\n        node.body = parseResource(tokenizer);\n        if (options.onCacheKey) {\n            node.cacheKey = options.onCacheKey(source);\n        }\n        // assert whether achieved to EOF\n        if (context.currentType !== 14 /* TokenTypes.EOF */) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || '');\n        }\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    return { parse };\n}\nfunction getTokenCaption(token) {\n    if (token.type === 14 /* TokenTypes.EOF */) {\n        return 'EOF';\n    }\n    const name = (token.value || '').replace(/\\r?\\n/gu, '\\\\n');\n    return name.length > 10 ? name.slice(0, 9) + '…' : name;\n}\n\nfunction createTransformer(ast, options = {} // eslint-disable-line\n) {\n    const _context = {\n        ast,\n        helpers: new Set()\n    };\n    const context = () => _context;\n    const helper = (name) => {\n        _context.helpers.add(name);\n        return name;\n    };\n    return { context, helper };\n}\nfunction traverseNodes(nodes, transformer) {\n    for (let i = 0; i < nodes.length; i++) {\n        traverseNode(nodes[i], transformer);\n    }\n}\nfunction traverseNode(node, transformer) {\n    // TODO: if we need pre-hook of transform, should be implemented to here\n    switch (node.type) {\n        case 1 /* NodeTypes.Plural */:\n            traverseNodes(node.cases, transformer);\n            transformer.helper(\"plural\" /* HelperNameMap.PLURAL */);\n            break;\n        case 2 /* NodeTypes.Message */:\n            traverseNodes(node.items, transformer);\n            break;\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            traverseNode(linked.key, transformer);\n            transformer.helper(\"linked\" /* HelperNameMap.LINKED */);\n            transformer.helper(\"type\" /* HelperNameMap.TYPE */);\n            break;\n        }\n        case 5 /* NodeTypes.List */:\n            transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n            transformer.helper(\"list\" /* HelperNameMap.LIST */);\n            break;\n        case 4 /* NodeTypes.Named */:\n            transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n            transformer.helper(\"named\" /* HelperNameMap.NAMED */);\n            break;\n    }\n    // TODO: if we need post-hook of transform, should be implemented to here\n}\n// transform AST\nfunction transform(ast, options = {} // eslint-disable-line\n) {\n    const transformer = createTransformer(ast);\n    transformer.helper(\"normalize\" /* HelperNameMap.NORMALIZE */);\n    // traverse\n    ast.body && traverseNode(ast.body, transformer);\n    // set meta information\n    const context = transformer.context();\n    ast.helpers = Array.from(context.helpers);\n}\n\nfunction optimize(ast) {\n    const body = ast.body;\n    if (body.type === 2 /* NodeTypes.Message */) {\n        optimizeMessageNode(body);\n    }\n    else {\n        body.cases.forEach(c => optimizeMessageNode(c));\n    }\n    return ast;\n}\nfunction optimizeMessageNode(message) {\n    if (message.items.length === 1) {\n        const item = message.items[0];\n        if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n            message.static = item.value;\n            delete item.value; // optimization for size\n        }\n    }\n    else {\n        const values = [];\n        for (let i = 0; i < message.items.length; i++) {\n            const item = message.items[i];\n            if (!(item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */)) {\n                break;\n            }\n            if (item.value == null) {\n                break;\n            }\n            values.push(item.value);\n        }\n        if (values.length === message.items.length) {\n            message.static = join(values);\n            for (let i = 0; i < message.items.length; i++) {\n                const item = message.items[i];\n                if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n                    delete item.value; // optimization for size\n                }\n            }\n        }\n    }\n}\n\nconst ERROR_DOMAIN$1 = 'minifier';\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction minify(node) {\n    node.t = node.type;\n    switch (node.type) {\n        case 0 /* NodeTypes.Resource */: {\n            const resource = node;\n            minify(resource.body);\n            resource.b = resource.body;\n            delete resource.body;\n            break;\n        }\n        case 1 /* NodeTypes.Plural */: {\n            const plural = node;\n            const cases = plural.cases;\n            for (let i = 0; i < cases.length; i++) {\n                minify(cases[i]);\n            }\n            plural.c = cases;\n            delete plural.cases;\n            break;\n        }\n        case 2 /* NodeTypes.Message */: {\n            const message = node;\n            const items = message.items;\n            for (let i = 0; i < items.length; i++) {\n                minify(items[i]);\n            }\n            message.i = items;\n            delete message.items;\n            if (message.static) {\n                message.s = message.static;\n                delete message.static;\n            }\n            break;\n        }\n        case 3 /* NodeTypes.Text */:\n        case 9 /* NodeTypes.Literal */:\n        case 8 /* NodeTypes.LinkedModifier */:\n        case 7 /* NodeTypes.LinkedKey */: {\n            const valueNode = node;\n            if (valueNode.value) {\n                valueNode.v = valueNode.value;\n                delete valueNode.value;\n            }\n            break;\n        }\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            minify(linked.key);\n            linked.k = linked.key;\n            delete linked.key;\n            if (linked.modifier) {\n                minify(linked.modifier);\n                linked.m = linked.modifier;\n                delete linked.modifier;\n            }\n            break;\n        }\n        case 5 /* NodeTypes.List */: {\n            const list = node;\n            list.i = list.index;\n            delete list.index;\n            break;\n        }\n        case 4 /* NodeTypes.Named */: {\n            const named = node;\n            named.k = named.key;\n            delete named.key;\n            break;\n        }\n        default:\n            {\n                throw createCompileError(CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE, null, {\n                    domain: ERROR_DOMAIN$1,\n                    args: [node.type]\n                });\n            }\n    }\n    delete node.type;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference types=\"source-map-js\" />\nconst ERROR_DOMAIN = 'parser';\nfunction createCodeGenerator(ast, options) {\n    const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;\n    const location = options.location !== false;\n    const _context = {\n        filename,\n        code: '',\n        column: 1,\n        line: 1,\n        offset: 0,\n        map: undefined,\n        breakLineCode,\n        needIndent: _needIndent,\n        indentLevel: 0\n    };\n    if (location && ast.loc) {\n        _context.source = ast.loc.source;\n    }\n    const context = () => _context;\n    function push(code, node) {\n        _context.code += code;\n    }\n    function _newline(n, withBreakLine = true) {\n        const _breakLineCode = withBreakLine ? breakLineCode : '';\n        push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);\n    }\n    function indent(withNewLine = true) {\n        const level = ++_context.indentLevel;\n        withNewLine && _newline(level);\n    }\n    function deindent(withNewLine = true) {\n        const level = --_context.indentLevel;\n        withNewLine && _newline(level);\n    }\n    function newline() {\n        _newline(_context.indentLevel);\n    }\n    const helper = (key) => `_${key}`;\n    const needIndent = () => _context.needIndent;\n    return {\n        context,\n        push,\n        indent,\n        deindent,\n        newline,\n        helper,\n        needIndent\n    };\n}\nfunction generateLinkedNode(generator, node) {\n    const { helper } = generator;\n    generator.push(`${helper(\"linked\" /* HelperNameMap.LINKED */)}(`);\n    generateNode(generator, node.key);\n    if (node.modifier) {\n        generator.push(`, `);\n        generateNode(generator, node.modifier);\n        generator.push(`, _type`);\n    }\n    else {\n        generator.push(`, undefined, _type`);\n    }\n    generator.push(`)`);\n}\nfunction generateMessageNode(generator, node) {\n    const { helper, needIndent } = generator;\n    generator.push(`${helper(\"normalize\" /* HelperNameMap.NORMALIZE */)}([`);\n    generator.indent(needIndent());\n    const length = node.items.length;\n    for (let i = 0; i < length; i++) {\n        generateNode(generator, node.items[i]);\n        if (i === length - 1) {\n            break;\n        }\n        generator.push(', ');\n    }\n    generator.deindent(needIndent());\n    generator.push('])');\n}\nfunction generatePluralNode(generator, node) {\n    const { helper, needIndent } = generator;\n    if (node.cases.length > 1) {\n        generator.push(`${helper(\"plural\" /* HelperNameMap.PLURAL */)}([`);\n        generator.indent(needIndent());\n        const length = node.cases.length;\n        for (let i = 0; i < length; i++) {\n            generateNode(generator, node.cases[i]);\n            if (i === length - 1) {\n                break;\n            }\n            generator.push(', ');\n        }\n        generator.deindent(needIndent());\n        generator.push(`])`);\n    }\n}\nfunction generateResource(generator, node) {\n    if (node.body) {\n        generateNode(generator, node.body);\n    }\n    else {\n        generator.push('null');\n    }\n}\nfunction generateNode(generator, node) {\n    const { helper } = generator;\n    switch (node.type) {\n        case 0 /* NodeTypes.Resource */:\n            generateResource(generator, node);\n            break;\n        case 1 /* NodeTypes.Plural */:\n            generatePluralNode(generator, node);\n            break;\n        case 2 /* NodeTypes.Message */:\n            generateMessageNode(generator, node);\n            break;\n        case 6 /* NodeTypes.Linked */:\n            generateLinkedNode(generator, node);\n            break;\n        case 8 /* NodeTypes.LinkedModifier */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 7 /* NodeTypes.LinkedKey */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 5 /* NodeTypes.List */:\n            generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"list\" /* HelperNameMap.LIST */)}(${node.index}))`, node);\n            break;\n        case 4 /* NodeTypes.Named */:\n            generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"named\" /* HelperNameMap.NAMED */)}(${JSON.stringify(node.key)}))`, node);\n            break;\n        case 9 /* NodeTypes.Literal */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 3 /* NodeTypes.Text */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        default:\n            {\n                throw createCompileError(CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE, null, {\n                    domain: ERROR_DOMAIN,\n                    args: [node.type]\n                });\n            }\n    }\n}\n// generate code from AST\nconst generate = (ast, options = {} // eslint-disable-line\n) => {\n    const mode = isString(options.mode) ? options.mode : 'normal';\n    const filename = isString(options.filename)\n        ? options.filename\n        : 'message.intl';\n    const sourceMap = !!options.sourceMap;\n    // prettier-ignore\n    const breakLineCode = options.breakLineCode != null\n        ? options.breakLineCode\n        : mode === 'arrow'\n            ? ';'\n            : '\\n';\n    const needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\n    const helpers = ast.helpers || [];\n    const generator = createCodeGenerator(ast, {\n        mode,\n        filename,\n        sourceMap,\n        breakLineCode,\n        needIndent\n    });\n    generator.push(mode === 'normal' ? `function __msg__ (ctx) {` : `(ctx) => {`);\n    generator.indent(needIndent);\n    if (helpers.length > 0) {\n        generator.push(`const { ${join(helpers.map(s => `${s}: _${s}`), ', ')} } = ctx`);\n        generator.newline();\n    }\n    generator.push(`return `);\n    generateNode(generator, ast);\n    generator.deindent(needIndent);\n    generator.push(`}`);\n    delete ast.helpers;\n    const { code, map } = generator.context();\n    return {\n        ast,\n        code,\n        map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\n    };\n};\n\nfunction baseCompile(source, options = {}) {\n    const assignedOptions = assign({}, options);\n    const jit = !!assignedOptions.jit;\n    const enalbeMinify = !!assignedOptions.minify;\n    const enambeOptimize = assignedOptions.optimize == null ? true : assignedOptions.optimize;\n    // parse source codes\n    const parser = createParser(assignedOptions);\n    const ast = parser.parse(source);\n    if (!jit) {\n        // transform ASTs\n        transform(ast, assignedOptions);\n        // generate javascript codes\n        return generate(ast, assignedOptions);\n    }\n    else {\n        // optimize ASTs\n        enambeOptimize && optimize(ast);\n        // minimize ASTs\n        enalbeMinify && minify(ast);\n        // In JIT mode, no ast transform, no code generation.\n        return { ast, code: '' };\n    }\n}\n\nexport { CompileErrorCodes, CompileWarnCodes, ERROR_DOMAIN$2 as ERROR_DOMAIN, LOCATION_STUB, baseCompile, createCompileError, createCompileWarn, createLocation, createParser, createPosition, defaultOnError, detectHtmlTag, errorMessages, warnMessages };\n", "/*!\n  * core-base v9.14.5\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { getGlobalThis, isObject, hasOwn, isFunction, isString, create, isNumber, isPlainObject, assign, join, toDisplayString, isArray, incrementer, format as format$1, isPromise, isBoolean, warn, isRegExp, warnOnce, sanitizeTranslatedHtml, escapeHtml, inBrowser, mark, measure, isEmptyObject, generateCodeFrame, generateFormatCacheKey, isDate } from '@intlify/shared';\nimport { CompileWarnCodes, CompileErrorCodes, createCompileError, detectHtmlTag, defaultOnError, baseCompile as baseCompile$1 } from '@intlify/message-compiler';\nexport { CompileErrorCodes, createCompileError } from '@intlify/message-compiler';\n\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n    if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n    }\n    if (typeof __INTLIFY_JIT_COMPILATION__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_JIT_COMPILATION__ = false;\n    }\n    if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n    }\n}\n\nfunction isMessageAST(val) {\n    return (isObject(val) &&\n        resolveType(val) === 0 &&\n        (hasOwn(val, 'b') || hasOwn(val, 'body')));\n}\nconst PROPS_BODY = ['b', 'body'];\nfunction resolveBody(node) {\n    return resolveProps(node, PROPS_BODY);\n}\nconst PROPS_CASES = ['c', 'cases'];\nfunction resolveCases(node) {\n    return resolveProps(node, PROPS_CASES, []);\n}\nconst PROPS_STATIC = ['s', 'static'];\nfunction resolveStatic(node) {\n    return resolveProps(node, PROPS_STATIC);\n}\nconst PROPS_ITEMS = ['i', 'items'];\nfunction resolveItems(node) {\n    return resolveProps(node, PROPS_ITEMS, []);\n}\nconst PROPS_TYPE = ['t', 'type'];\nfunction resolveType(node) {\n    return resolveProps(node, PROPS_TYPE);\n}\nconst PROPS_VALUE = ['v', 'value'];\nfunction resolveValue$1(node, type) {\n    const resolved = resolveProps(node, PROPS_VALUE);\n    if (resolved != null) {\n        return resolved;\n    }\n    else {\n        throw createUnhandleNodeError(type);\n    }\n}\nconst PROPS_MODIFIER = ['m', 'modifier'];\nfunction resolveLinkedModifier(node) {\n    return resolveProps(node, PROPS_MODIFIER);\n}\nconst PROPS_KEY = ['k', 'key'];\nfunction resolveLinkedKey(node) {\n    const resolved = resolveProps(node, PROPS_KEY);\n    if (resolved) {\n        return resolved;\n    }\n    else {\n        throw createUnhandleNodeError(6 /* NodeTypes.Linked */);\n    }\n}\nfunction resolveProps(node, props, defaultValue) {\n    for (let i = 0; i < props.length; i++) {\n        const prop = props[i];\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (hasOwn(node, prop) && node[prop] != null) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return node[prop];\n        }\n    }\n    return defaultValue;\n}\nconst AST_NODE_PROPS_KEYS = [\n    ...PROPS_BODY,\n    ...PROPS_CASES,\n    ...PROPS_STATIC,\n    ...PROPS_ITEMS,\n    ...PROPS_KEY,\n    ...PROPS_MODIFIER,\n    ...PROPS_VALUE,\n    ...PROPS_TYPE\n];\nfunction createUnhandleNodeError(type) {\n    return new Error(`unhandled node type: ${type}`);\n}\n\nconst pathStateMachine =  [];\npathStateMachine[0 /* States.BEFORE_PATH */] = {\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [0 /* States.BEFORE_PATH */],\n    [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]\n};\npathStateMachine[1 /* States.IN_PATH */] = {\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */],\n    [\".\" /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */]\n};\npathStateMachine[2 /* States.BEFORE_IDENT */] = {\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [2 /* States.BEFORE_IDENT */],\n    [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"0\" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */]\n};\npathStateMachine[3 /* States.IN_IDENT */] = {\n    [\"i\" /* PathCharTypes.IDENT */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"0\" /* PathCharTypes.ZERO */]: [3 /* States.IN_IDENT */, 0 /* Actions.APPEND */],\n    [\"w\" /* PathCharTypes.WORKSPACE */]: [1 /* States.IN_PATH */, 1 /* Actions.PUSH */],\n    [\".\" /* PathCharTypes.DOT */]: [2 /* States.BEFORE_IDENT */, 1 /* Actions.PUSH */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [4 /* States.IN_SUB_PATH */, 1 /* Actions.PUSH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: [7 /* States.AFTER_PATH */, 1 /* Actions.PUSH */]\n};\npathStateMachine[4 /* States.IN_SUB_PATH */] = {\n    [\"'\" /* PathCharTypes.SINGLE_QUOTE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */],\n    [\"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */],\n    [\"[\" /* PathCharTypes.LEFT_BRACKET */]: [\n        4 /* States.IN_SUB_PATH */,\n        2 /* Actions.INC_SUB_PATH_DEPTH */\n    ],\n    [\"]\" /* PathCharTypes.RIGHT_BRACKET */]: [1 /* States.IN_PATH */, 3 /* Actions.PUSH_SUB_PATH */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n    [\"l\" /* PathCharTypes.ELSE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */]\n};\npathStateMachine[5 /* States.IN_SINGLE_QUOTE */] = {\n    [\"'\" /* PathCharTypes.SINGLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n    [\"l\" /* PathCharTypes.ELSE */]: [5 /* States.IN_SINGLE_QUOTE */, 0 /* Actions.APPEND */]\n};\npathStateMachine[6 /* States.IN_DOUBLE_QUOTE */] = {\n    [\"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */]: [4 /* States.IN_SUB_PATH */, 0 /* Actions.APPEND */],\n    [\"o\" /* PathCharTypes.END_OF_FAIL */]: 8 /* States.ERROR */,\n    [\"l\" /* PathCharTypes.ELSE */]: [6 /* States.IN_DOUBLE_QUOTE */, 0 /* Actions.APPEND */]\n};\n/**\n * Check if an expression is a literal value.\n */\nconst literalValueRE = /^\\s?(?:true|false|-?[\\d.]+|'[^']*'|\"[^\"]*\")\\s?$/;\nfunction isLiteral(exp) {\n    return literalValueRE.test(exp);\n}\n/**\n * Strip quotes from a string\n */\nfunction stripQuotes(str) {\n    const a = str.charCodeAt(0);\n    const b = str.charCodeAt(str.length - 1);\n    return a === b && (a === 0x22 || a === 0x27) ? str.slice(1, -1) : str;\n}\n/**\n * Determine the type of a character in a keypath.\n */\nfunction getPathCharType(ch) {\n    if (ch === undefined || ch === null) {\n        return \"o\" /* PathCharTypes.END_OF_FAIL */;\n    }\n    const code = ch.charCodeAt(0);\n    switch (code) {\n        case 0x5b: // [\n        case 0x5d: // ]\n        case 0x2e: // .\n        case 0x22: // \"\n        case 0x27: // '\n            return ch;\n        case 0x5f: // _\n        case 0x24: // $\n        case 0x2d: // -\n            return \"i\" /* PathCharTypes.IDENT */;\n        case 0x09: // Tab (HT)\n        case 0x0a: // Newline (LF)\n        case 0x0d: // Return (CR)\n        case 0xa0: // No-break space (NBSP)\n        case 0xfeff: // Byte Order Mark (BOM)\n        case 0x2028: // Line Separator (LS)\n        case 0x2029: // Paragraph Separator (PS)\n            return \"w\" /* PathCharTypes.WORKSPACE */;\n    }\n    return \"i\" /* PathCharTypes.IDENT */;\n}\n/**\n * Format a subPath, return its plain form if it is\n * a literal string or number. Otherwise prepend the\n * dynamic indicator (*).\n */\nfunction formatSubPath(path) {\n    const trimmed = path.trim();\n    // invalid leading 0\n    if (path.charAt(0) === '0' && isNaN(parseInt(path))) {\n        return false;\n    }\n    return isLiteral(trimmed)\n        ? stripQuotes(trimmed)\n        : \"*\" /* PathCharTypes.ASTARISK */ + trimmed;\n}\n/**\n * Parse a string path into an array of segments\n */\nfunction parse(path) {\n    const keys = [];\n    let index = -1;\n    let mode = 0 /* States.BEFORE_PATH */;\n    let subPathDepth = 0;\n    let c;\n    let key; // eslint-disable-line\n    let newChar;\n    let type;\n    let transition;\n    let action;\n    let typeMap;\n    const actions = [];\n    actions[0 /* Actions.APPEND */] = () => {\n        if (key === undefined) {\n            key = newChar;\n        }\n        else {\n            key += newChar;\n        }\n    };\n    actions[1 /* Actions.PUSH */] = () => {\n        if (key !== undefined) {\n            keys.push(key);\n            key = undefined;\n        }\n    };\n    actions[2 /* Actions.INC_SUB_PATH_DEPTH */] = () => {\n        actions[0 /* Actions.APPEND */]();\n        subPathDepth++;\n    };\n    actions[3 /* Actions.PUSH_SUB_PATH */] = () => {\n        if (subPathDepth > 0) {\n            subPathDepth--;\n            mode = 4 /* States.IN_SUB_PATH */;\n            actions[0 /* Actions.APPEND */]();\n        }\n        else {\n            subPathDepth = 0;\n            if (key === undefined) {\n                return false;\n            }\n            key = formatSubPath(key);\n            if (key === false) {\n                return false;\n            }\n            else {\n                actions[1 /* Actions.PUSH */]();\n            }\n        }\n    };\n    function maybeUnescapeQuote() {\n        const nextChar = path[index + 1];\n        if ((mode === 5 /* States.IN_SINGLE_QUOTE */ &&\n            nextChar === \"'\" /* PathCharTypes.SINGLE_QUOTE */) ||\n            (mode === 6 /* States.IN_DOUBLE_QUOTE */ &&\n                nextChar === \"\\\"\" /* PathCharTypes.DOUBLE_QUOTE */)) {\n            index++;\n            newChar = '\\\\' + nextChar;\n            actions[0 /* Actions.APPEND */]();\n            return true;\n        }\n    }\n    while (mode !== null) {\n        index++;\n        c = path[index];\n        if (c === '\\\\' && maybeUnescapeQuote()) {\n            continue;\n        }\n        type = getPathCharType(c);\n        typeMap = pathStateMachine[mode];\n        transition = typeMap[type] || typeMap[\"l\" /* PathCharTypes.ELSE */] || 8 /* States.ERROR */;\n        // check parse error\n        if (transition === 8 /* States.ERROR */) {\n            return;\n        }\n        mode = transition[0];\n        if (transition[1] !== undefined) {\n            action = actions[transition[1]];\n            if (action) {\n                newChar = c;\n                if (action() === false) {\n                    return;\n                }\n            }\n        }\n        // check parse finish\n        if (mode === 7 /* States.AFTER_PATH */) {\n            return keys;\n        }\n    }\n}\n// path token cache\nconst cache = new Map();\n/**\n * key-value message resolver\n *\n * @remarks\n * Resolves messages with the key-value structure. Note that messages with a hierarchical structure such as objects cannot be resolved\n *\n * @param obj - A target object to be resolved with path\n * @param path - A {@link Path | path} to resolve the value of message\n *\n * @returns A resolved {@link PathValue | path value}\n *\n * @VueI18nGeneral\n */\nfunction resolveWithKeyValue(obj, path) {\n    return isObject(obj) ? obj[path] : null;\n}\n/**\n * message resolver\n *\n * @remarks\n * Resolves messages. messages with a hierarchical structure such as objects can be resolved. This resolver is used in VueI18n as default.\n *\n * @param obj - A target object to be resolved with path\n * @param path - A {@link Path | path} to resolve the value of message\n *\n * @returns A resolved {@link PathValue | path value}\n *\n * @VueI18nGeneral\n */\nfunction resolveValue(obj, path) {\n    // check object\n    if (!isObject(obj)) {\n        return null;\n    }\n    // parse path\n    let hit = cache.get(path);\n    if (!hit) {\n        hit = parse(path);\n        if (hit) {\n            cache.set(path, hit);\n        }\n    }\n    // check hit\n    if (!hit) {\n        return null;\n    }\n    // resolve path value\n    const len = hit.length;\n    let last = obj;\n    let i = 0;\n    while (i < len) {\n        const key = hit[i];\n        /**\n         * NOTE:\n         * if `key` is intlify message format AST node key and `last` is intlify message format AST, skip it.\n         * because the AST node is not a key-value structure.\n         */\n        if (AST_NODE_PROPS_KEYS.includes(key) && isMessageAST(last)) {\n            return null;\n        }\n        const val = last[key];\n        if (val === undefined) {\n            return null;\n        }\n        if (isFunction(last)) {\n            return null;\n        }\n        last = val;\n        i++;\n    }\n    return last;\n}\n\nconst DEFAULT_MODIFIER = (str) => str;\nconst DEFAULT_MESSAGE = (ctx) => ''; // eslint-disable-line\nconst DEFAULT_MESSAGE_DATA_TYPE = 'text';\nconst DEFAULT_NORMALIZE = (values) => values.length === 0 ? '' : join(values);\nconst DEFAULT_INTERPOLATE = toDisplayString;\nfunction pluralDefault(choice, choicesLength) {\n    choice = Math.abs(choice);\n    if (choicesLength === 2) {\n        // prettier-ignore\n        return choice\n            ? choice > 1\n                ? 1\n                : 0\n            : 1;\n    }\n    return choice ? Math.min(choice, 2) : 0;\n}\nfunction getPluralIndex(options) {\n    // prettier-ignore\n    const index = isNumber(options.pluralIndex)\n        ? options.pluralIndex\n        : -1;\n    // prettier-ignore\n    return options.named && (isNumber(options.named.count) || isNumber(options.named.n))\n        ? isNumber(options.named.count)\n            ? options.named.count\n            : isNumber(options.named.n)\n                ? options.named.n\n                : index\n        : index;\n}\nfunction normalizeNamed(pluralIndex, props) {\n    if (!props.count) {\n        props.count = pluralIndex;\n    }\n    if (!props.n) {\n        props.n = pluralIndex;\n    }\n}\nfunction createMessageContext(options = {}) {\n    const locale = options.locale;\n    const pluralIndex = getPluralIndex(options);\n    const pluralRule = isObject(options.pluralRules) &&\n        isString(locale) &&\n        isFunction(options.pluralRules[locale])\n        ? options.pluralRules[locale]\n        : pluralDefault;\n    const orgPluralRule = isObject(options.pluralRules) &&\n        isString(locale) &&\n        isFunction(options.pluralRules[locale])\n        ? pluralDefault\n        : undefined;\n    const plural = (messages) => {\n        return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];\n    };\n    const _list = options.list || [];\n    const list = (index) => _list[index];\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const _named = options.named || create();\n    isNumber(options.pluralIndex) && normalizeNamed(pluralIndex, _named);\n    const named = (key) => _named[key];\n    function message(key) {\n        // prettier-ignore\n        const msg = isFunction(options.messages)\n            ? options.messages(key)\n            : isObject(options.messages)\n                ? options.messages[key]\n                : false;\n        return !msg\n            ? options.parent\n                ? options.parent.message(key) // resolve from parent messages\n                : DEFAULT_MESSAGE\n            : msg;\n    }\n    const _modifier = (name) => options.modifiers\n        ? options.modifiers[name]\n        : DEFAULT_MODIFIER;\n    const normalize = isPlainObject(options.processor) && isFunction(options.processor.normalize)\n        ? options.processor.normalize\n        : DEFAULT_NORMALIZE;\n    const interpolate = isPlainObject(options.processor) &&\n        isFunction(options.processor.interpolate)\n        ? options.processor.interpolate\n        : DEFAULT_INTERPOLATE;\n    const type = isPlainObject(options.processor) && isString(options.processor.type)\n        ? options.processor.type\n        : DEFAULT_MESSAGE_DATA_TYPE;\n    const linked = (key, ...args) => {\n        const [arg1, arg2] = args;\n        let type = 'text';\n        let modifier = '';\n        if (args.length === 1) {\n            if (isObject(arg1)) {\n                modifier = arg1.modifier || modifier;\n                type = arg1.type || type;\n            }\n            else if (isString(arg1)) {\n                modifier = arg1 || modifier;\n            }\n        }\n        else if (args.length === 2) {\n            if (isString(arg1)) {\n                modifier = arg1 || modifier;\n            }\n            if (isString(arg2)) {\n                type = arg2 || type;\n            }\n        }\n        const ret = message(key)(ctx);\n        const msg = \n        // The message in vnode resolved with linked are returned as an array by processor.nomalize\n        type === 'vnode' && isArray(ret) && modifier\n            ? ret[0]\n            : ret;\n        return modifier ? _modifier(modifier)(msg, type) : msg;\n    };\n    const ctx = {\n        [\"list\" /* HelperNameMap.LIST */]: list,\n        [\"named\" /* HelperNameMap.NAMED */]: named,\n        [\"plural\" /* HelperNameMap.PLURAL */]: plural,\n        [\"linked\" /* HelperNameMap.LINKED */]: linked,\n        [\"message\" /* HelperNameMap.MESSAGE */]: message,\n        [\"type\" /* HelperNameMap.TYPE */]: type,\n        [\"interpolate\" /* HelperNameMap.INTERPOLATE */]: interpolate,\n        [\"normalize\" /* HelperNameMap.NORMALIZE */]: normalize,\n        [\"values\" /* HelperNameMap.VALUES */]: assign(create(), _list, _named)\n    };\n    return ctx;\n}\n\nlet devtools = null;\nfunction setDevToolsHook(hook) {\n    devtools = hook;\n}\nfunction getDevToolsHook() {\n    return devtools;\n}\nfunction initI18nDevTools(i18n, version, meta) {\n    // TODO: queue if devtools is undefined\n    devtools &&\n        devtools.emit(\"i18n:init\" /* IntlifyDevToolsHooks.I18nInit */, {\n            timestamp: Date.now(),\n            i18n,\n            version,\n            meta\n        });\n}\nconst translateDevTools = /* #__PURE__*/ createDevToolsHook(\"function:translate\" /* IntlifyDevToolsHooks.FunctionTranslate */);\nfunction createDevToolsHook(hook) {\n    return (payloads) => devtools && devtools.emit(hook, payloads);\n}\n\nconst code$1 = CompileWarnCodes.__EXTEND_POINT__;\nconst inc$1 = incrementer(code$1);\nconst CoreWarnCodes = {\n    NOT_FOUND_KEY: code$1, // 2\n    FALLBACK_TO_TRANSLATE: inc$1(), // 3\n    CANNOT_FORMAT_NUMBER: inc$1(), // 4\n    FALLBACK_TO_NUMBER_FORMAT: inc$1(), // 5\n    CANNOT_FORMAT_DATE: inc$1(), // 6\n    FALLBACK_TO_DATE_FORMAT: inc$1(), // 7\n    EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: inc$1(), // 8\n    __EXTEND_POINT__: inc$1() // 9\n};\n/** @internal */\nconst warnMessages = {\n    [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,\n    [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,\n    [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,\n    [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,\n    [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,\n    [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`,\n    [CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]: `This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future.`\n};\nfunction getWarnMessage(code, ...args) {\n    return format$1(warnMessages[code], ...args);\n}\n\nconst code = CompileErrorCodes.__EXTEND_POINT__;\nconst inc = incrementer(code);\nconst CoreErrorCodes = {\n    INVALID_ARGUMENT: code, // 17\n    INVALID_DATE_ARGUMENT: inc(), // 18\n    INVALID_ISO_DATE_ARGUMENT: inc(), // 19\n    NOT_SUPPORT_NON_STRING_MESSAGE: inc(), // 20\n    NOT_SUPPORT_LOCALE_PROMISE_VALUE: inc(), // 21\n    NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: inc(), // 22\n    NOT_SUPPORT_LOCALE_TYPE: inc(), // 23\n    __EXTEND_POINT__: inc() // 24\n};\nfunction createCoreError(code) {\n    return createCompileError(code, null, (process.env.NODE_ENV !== 'production') ? { messages: errorMessages } : undefined);\n}\n/** @internal */\nconst errorMessages = {\n    [CoreErrorCodes.INVALID_ARGUMENT]: 'Invalid arguments',\n    [CoreErrorCodes.INVALID_DATE_ARGUMENT]: 'The date provided is an invalid Date object.' +\n        'Make sure your Date represents a valid date.',\n    [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: 'The argument provided is not a valid ISO date string',\n    [CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE]: 'Not support non-string message',\n    [CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE]: 'cannot support promise value',\n    [CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]: 'cannot support async function',\n    [CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE]: 'cannot support locale type'\n};\n\n/** @internal */\nfunction getLocale(context, options) {\n    return options.locale != null\n        ? resolveLocale(options.locale)\n        : resolveLocale(context.locale);\n}\nlet _resolveLocale;\n/** @internal */\nfunction resolveLocale(locale) {\n    if (isString(locale)) {\n        return locale;\n    }\n    else {\n        if (isFunction(locale)) {\n            if (locale.resolvedOnce && _resolveLocale != null) {\n                return _resolveLocale;\n            }\n            else if (locale.constructor.name === 'Function') {\n                const resolve = locale();\n                if (isPromise(resolve)) {\n                    throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);\n                }\n                return (_resolveLocale = resolve);\n            }\n            else {\n                throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);\n            }\n        }\n        else {\n            throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE);\n        }\n    }\n}\n/**\n * Fallback with simple implemenation\n *\n * @remarks\n * A fallback locale function implemented with a simple fallback algorithm.\n *\n * Basically, it returns the value as specified in the `fallbackLocale` props, and is processed with the fallback inside intlify.\n *\n * @param ctx - A {@link CoreContext | context}\n * @param fallback - A {@link FallbackLocale | fallback locale}\n * @param start - A starting {@link Locale | locale}\n *\n * @returns Fallback locales\n *\n * @VueI18nGeneral\n */\nfunction fallbackWithSimple(ctx, fallback, start // eslint-disable-line @typescript-eslint/no-unused-vars\n) {\n    // prettier-ignore\n    return [...new Set([\n            start,\n            ...(isArray(fallback)\n                ? fallback\n                : isObject(fallback)\n                    ? Object.keys(fallback)\n                    : isString(fallback)\n                        ? [fallback]\n                        : [start])\n        ])];\n}\n/**\n * Fallback with locale chain\n *\n * @remarks\n * A fallback locale function implemented with a fallback chain algorithm. It's used in VueI18n as default.\n *\n * @param ctx - A {@link CoreContext | context}\n * @param fallback - A {@link FallbackLocale | fallback locale}\n * @param start - A starting {@link Locale | locale}\n *\n * @returns Fallback locales\n *\n * @VueI18nSee [Fallbacking](../guide/essentials/fallback)\n *\n * @VueI18nGeneral\n */\nfunction fallbackWithLocaleChain(ctx, fallback, start) {\n    const startLocale = isString(start) ? start : DEFAULT_LOCALE;\n    const context = ctx;\n    if (!context.__localeChainCache) {\n        context.__localeChainCache = new Map();\n    }\n    let chain = context.__localeChainCache.get(startLocale);\n    if (!chain) {\n        chain = [];\n        // first block defined by start\n        let block = [start];\n        // while any intervening block found\n        while (isArray(block)) {\n            block = appendBlockToChain(chain, block, fallback);\n        }\n        // prettier-ignore\n        // last block defined by default\n        const defaults = isArray(fallback) || !isPlainObject(fallback)\n            ? fallback\n            : fallback['default']\n                ? fallback['default']\n                : null;\n        // convert defaults to array\n        block = isString(defaults) ? [defaults] : defaults;\n        if (isArray(block)) {\n            appendBlockToChain(chain, block, false);\n        }\n        context.__localeChainCache.set(startLocale, chain);\n    }\n    return chain;\n}\nfunction appendBlockToChain(chain, block, blocks) {\n    let follow = true;\n    for (let i = 0; i < block.length && isBoolean(follow); i++) {\n        const locale = block[i];\n        if (isString(locale)) {\n            follow = appendLocaleToChain(chain, block[i], blocks);\n        }\n    }\n    return follow;\n}\nfunction appendLocaleToChain(chain, locale, blocks) {\n    let follow;\n    const tokens = locale.split('-');\n    do {\n        const target = tokens.join('-');\n        follow = appendItemToChain(chain, target, blocks);\n        tokens.splice(-1, 1);\n    } while (tokens.length && follow === true);\n    return follow;\n}\nfunction appendItemToChain(chain, target, blocks) {\n    let follow = false;\n    if (!chain.includes(target)) {\n        follow = true;\n        if (target) {\n            follow = target[target.length - 1] !== '!';\n            const locale = target.replace(/!/g, '');\n            chain.push(locale);\n            if ((isArray(blocks) || isPlainObject(blocks)) &&\n                blocks[locale] // eslint-disable-line @typescript-eslint/no-explicit-any\n            ) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                follow = blocks[locale];\n            }\n        }\n    }\n    return follow;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Intlify core-base version\n * @internal\n */\nconst VERSION = '9.14.5';\nconst NOT_REOSLVED = -1;\nconst DEFAULT_LOCALE = 'en-US';\nconst MISSING_RESOLVE_VALUE = '';\nconst capitalize = (str) => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;\nfunction getDefaultLinkedModifiers() {\n    return {\n        upper: (val, type) => {\n            // prettier-ignore\n            return type === 'text' && isString(val)\n                ? val.toUpperCase()\n                : type === 'vnode' && isObject(val) && '__v_isVNode' in val\n                    ? val.children.toUpperCase()\n                    : val;\n        },\n        lower: (val, type) => {\n            // prettier-ignore\n            return type === 'text' && isString(val)\n                ? val.toLowerCase()\n                : type === 'vnode' && isObject(val) && '__v_isVNode' in val\n                    ? val.children.toLowerCase()\n                    : val;\n        },\n        capitalize: (val, type) => {\n            // prettier-ignore\n            return (type === 'text' && isString(val)\n                ? capitalize(val)\n                : type === 'vnode' && isObject(val) && '__v_isVNode' in val\n                    ? capitalize(val.children)\n                    : val);\n        }\n    };\n}\nlet _compiler;\nfunction registerMessageCompiler(compiler) {\n    _compiler = compiler;\n}\nlet _resolver;\n/**\n * Register the message resolver\n *\n * @param resolver - A {@link MessageResolver} function\n *\n * @VueI18nGeneral\n */\nfunction registerMessageResolver(resolver) {\n    _resolver = resolver;\n}\nlet _fallbacker;\n/**\n * Register the locale fallbacker\n *\n * @param fallbacker - A {@link LocaleFallbacker} function\n *\n * @VueI18nGeneral\n */\nfunction registerLocaleFallbacker(fallbacker) {\n    _fallbacker = fallbacker;\n}\n// Additional Meta for Intlify DevTools\nlet _additionalMeta =  null;\n/* #__NO_SIDE_EFFECTS__ */\nconst setAdditionalMeta = (meta) => {\n    _additionalMeta = meta;\n};\n/* #__NO_SIDE_EFFECTS__ */\nconst getAdditionalMeta = () => _additionalMeta;\nlet _fallbackContext = null;\nconst setFallbackContext = (context) => {\n    _fallbackContext = context;\n};\nconst getFallbackContext = () => _fallbackContext;\n// ID for CoreContext\nlet _cid = 0;\nfunction createCoreContext(options = {}) {\n    // setup options\n    const onWarn = isFunction(options.onWarn) ? options.onWarn : warn;\n    const version = isString(options.version) ? options.version : VERSION;\n    const locale = isString(options.locale) || isFunction(options.locale)\n        ? options.locale\n        : DEFAULT_LOCALE;\n    const _locale = isFunction(locale) ? DEFAULT_LOCALE : locale;\n    const fallbackLocale = isArray(options.fallbackLocale) ||\n        isPlainObject(options.fallbackLocale) ||\n        isString(options.fallbackLocale) ||\n        options.fallbackLocale === false\n        ? options.fallbackLocale\n        : _locale;\n    const messages = isPlainObject(options.messages)\n        ? options.messages\n        : createResources(_locale);\n    const datetimeFormats = isPlainObject(options.datetimeFormats)\n            ? options.datetimeFormats\n            : createResources(_locale)\n        ;\n    const numberFormats = isPlainObject(options.numberFormats)\n            ? options.numberFormats\n            : createResources(_locale)\n        ;\n    const modifiers = assign(create(), options.modifiers, getDefaultLinkedModifiers());\n    const pluralRules = options.pluralRules || create();\n    const missing = isFunction(options.missing) ? options.missing : null;\n    const missingWarn = isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\n        ? options.missingWarn\n        : true;\n    const fallbackWarn = isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\n        ? options.fallbackWarn\n        : true;\n    const fallbackFormat = !!options.fallbackFormat;\n    const unresolving = !!options.unresolving;\n    const postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : null;\n    const processor = isPlainObject(options.processor) ? options.processor : null;\n    const warnHtmlMessage = isBoolean(options.warnHtmlMessage)\n        ? options.warnHtmlMessage\n        : true;\n    const escapeParameter = !!options.escapeParameter;\n    const messageCompiler = isFunction(options.messageCompiler)\n        ? options.messageCompiler\n        : _compiler;\n    if ((process.env.NODE_ENV !== 'production') &&\n        !false &&\n        !false &&\n        isFunction(options.messageCompiler)) {\n        warnOnce(getWarnMessage(CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER));\n    }\n    const messageResolver = isFunction(options.messageResolver)\n        ? options.messageResolver\n        : _resolver || resolveWithKeyValue;\n    const localeFallbacker = isFunction(options.localeFallbacker)\n        ? options.localeFallbacker\n        : _fallbacker || fallbackWithSimple;\n    const fallbackContext = isObject(options.fallbackContext)\n        ? options.fallbackContext\n        : undefined;\n    // setup internal options\n    const internalOptions = options;\n    const __datetimeFormatters = isObject(internalOptions.__datetimeFormatters)\n            ? internalOptions.__datetimeFormatters\n            : new Map()\n        ;\n    const __numberFormatters = isObject(internalOptions.__numberFormatters)\n            ? internalOptions.__numberFormatters\n            : new Map()\n        ;\n    const __meta = isObject(internalOptions.__meta) ? internalOptions.__meta : {};\n    _cid++;\n    const context = {\n        version,\n        cid: _cid,\n        locale,\n        fallbackLocale,\n        messages,\n        modifiers,\n        pluralRules,\n        missing,\n        missingWarn,\n        fallbackWarn,\n        fallbackFormat,\n        unresolving,\n        postTranslation,\n        processor,\n        warnHtmlMessage,\n        escapeParameter,\n        messageCompiler,\n        messageResolver,\n        localeFallbacker,\n        fallbackContext,\n        onWarn,\n        __meta\n    };\n    {\n        context.datetimeFormats = datetimeFormats;\n        context.numberFormats = numberFormats;\n        context.__datetimeFormatters = __datetimeFormatters;\n        context.__numberFormatters = __numberFormatters;\n    }\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production')) {\n        context.__v_emitter =\n            internalOptions.__v_emitter != null\n                ? internalOptions.__v_emitter\n                : undefined;\n    }\n    // NOTE: experimental !!\n    if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n        initI18nDevTools(context, version, __meta);\n    }\n    return context;\n}\nconst createResources = (locale) => ({ [locale]: create() });\n/** @internal */\nfunction isTranslateFallbackWarn(fallback, key) {\n    return fallback instanceof RegExp ? fallback.test(key) : fallback;\n}\n/** @internal */\nfunction isTranslateMissingWarn(missing, key) {\n    return missing instanceof RegExp ? missing.test(key) : missing;\n}\n/** @internal */\nfunction handleMissing(context, key, locale, missingWarn, type) {\n    const { missing, onWarn } = context;\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production')) {\n        const emitter = context.__v_emitter;\n        if (emitter) {\n            emitter.emit(\"missing\" /* VueDevToolsTimelineEvents.MISSING */, {\n                locale,\n                key,\n                type,\n                groupId: `${type}:${key}`\n            });\n        }\n    }\n    if (missing !== null) {\n        const ret = missing(context, locale, key, type);\n        return isString(ret) ? ret : key;\n    }\n    else {\n        if ((process.env.NODE_ENV !== 'production') && isTranslateMissingWarn(missingWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, { key, locale }));\n        }\n        return key;\n    }\n}\n/** @internal */\nfunction updateFallbackLocale(ctx, locale, fallback) {\n    const context = ctx;\n    context.__localeChainCache = new Map();\n    ctx.localeFallbacker(ctx, fallback, locale);\n}\n/** @internal */\nfunction isAlmostSameLocale(locale, compareLocale) {\n    if (locale === compareLocale)\n        return false;\n    return locale.split('-')[0] === compareLocale.split('-')[0];\n}\n/** @internal */\nfunction isImplicitFallback(targetLocale, locales) {\n    const index = locales.indexOf(targetLocale);\n    if (index === -1) {\n        return false;\n    }\n    for (let i = index + 1; i < locales.length; i++) {\n        if (isAlmostSameLocale(targetLocale, locales[i])) {\n            return true;\n        }\n    }\n    return false;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nfunction format(ast) {\n    const msg = (ctx) => formatParts(ctx, ast);\n    return msg;\n}\nfunction formatParts(ctx, ast) {\n    const body = resolveBody(ast);\n    if (body == null) {\n        throw createUnhandleNodeError(0 /* NodeTypes.Resource */);\n    }\n    const type = resolveType(body);\n    if (type === 1 /* NodeTypes.Plural */) {\n        const plural = body;\n        const cases = resolveCases(plural);\n        return ctx.plural(cases.reduce((messages, c) => [\n            ...messages,\n            formatMessageParts(ctx, c)\n        ], []));\n    }\n    else {\n        return formatMessageParts(ctx, body);\n    }\n}\nfunction formatMessageParts(ctx, node) {\n    const static_ = resolveStatic(node);\n    if (static_ != null) {\n        return ctx.type === 'text'\n            ? static_\n            : ctx.normalize([static_]);\n    }\n    else {\n        const messages = resolveItems(node).reduce((acm, c) => [...acm, formatMessagePart(ctx, c)], []);\n        return ctx.normalize(messages);\n    }\n}\nfunction formatMessagePart(ctx, node) {\n    const type = resolveType(node);\n    switch (type) {\n        case 3 /* NodeTypes.Text */: {\n            return resolveValue$1(node, type);\n        }\n        case 9 /* NodeTypes.Literal */: {\n            return resolveValue$1(node, type);\n        }\n        case 4 /* NodeTypes.Named */: {\n            const named = node;\n            if (hasOwn(named, 'k') && named.k) {\n                return ctx.interpolate(ctx.named(named.k));\n            }\n            if (hasOwn(named, 'key') && named.key) {\n                return ctx.interpolate(ctx.named(named.key));\n            }\n            throw createUnhandleNodeError(type);\n        }\n        case 5 /* NodeTypes.List */: {\n            const list = node;\n            if (hasOwn(list, 'i') && isNumber(list.i)) {\n                return ctx.interpolate(ctx.list(list.i));\n            }\n            if (hasOwn(list, 'index') && isNumber(list.index)) {\n                return ctx.interpolate(ctx.list(list.index));\n            }\n            throw createUnhandleNodeError(type);\n        }\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            const modifier = resolveLinkedModifier(linked);\n            const key = resolveLinkedKey(linked);\n            return ctx.linked(formatMessagePart(ctx, key), modifier ? formatMessagePart(ctx, modifier) : undefined, ctx.type);\n        }\n        case 7 /* NodeTypes.LinkedKey */: {\n            return resolveValue$1(node, type);\n        }\n        case 8 /* NodeTypes.LinkedModifier */: {\n            return resolveValue$1(node, type);\n        }\n        default:\n            throw new Error(`unhandled node on format message part: ${type}`);\n    }\n}\n\nconst WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;\nfunction checkHtmlMessage(source, warnHtmlMessage) {\n    if (warnHtmlMessage && detectHtmlTag(source)) {\n        warn(format$1(WARN_MESSAGE, { source }));\n    }\n}\nconst defaultOnCacheKey = (message) => message;\nlet compileCache = create();\nfunction onCompileWarn(_warn) {\n    if (_warn.code === CompileWarnCodes.USE_MODULO_SYNTAX) {\n        warn(`The use of named interpolation with modulo syntax is deprecated. ` +\n            `It will be removed in v10.\\n` +\n            `reference: https://vue-i18n.intlify.dev/guide/essentials/syntax#rails-i18n-format \\n` +\n            `(message compiler warning message: ${_warn.message})`);\n    }\n}\nfunction clearCompileCache() {\n    compileCache = create();\n}\nfunction baseCompile(message, options = {}) {\n    // error detecting on compile\n    let detectError = false;\n    const onError = options.onError || defaultOnError;\n    options.onError = (err) => {\n        detectError = true;\n        onError(err);\n    };\n    // compile with mesasge-compiler\n    return { ...baseCompile$1(message, options), detectError };\n}\n/* #__NO_SIDE_EFFECTS__ */\nconst compileToFunction = (message, context) => {\n    if (!isString(message)) {\n        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE);\n    }\n    // set onWarn\n    if ((process.env.NODE_ENV !== 'production')) {\n        context.onWarn = onCompileWarn;\n    }\n    {\n        // check HTML message\n        const warnHtmlMessage = isBoolean(context.warnHtmlMessage)\n            ? context.warnHtmlMessage\n            : true;\n        (process.env.NODE_ENV !== 'production') && checkHtmlMessage(message, warnHtmlMessage);\n        // check caches\n        const onCacheKey = context.onCacheKey || defaultOnCacheKey;\n        const cacheKey = onCacheKey(message);\n        const cached = compileCache[cacheKey];\n        if (cached) {\n            return cached;\n        }\n        // compile\n        const { code, detectError } = baseCompile(message, context);\n        // evaluate function\n        const msg = new Function(`return ${code}`)();\n        // if occurred compile error, don't cache\n        return !detectError\n            ? (compileCache[cacheKey] = msg)\n            : msg;\n    }\n};\nfunction compile(message, context) {\n    // set onWarn\n    if ((process.env.NODE_ENV !== 'production')) {\n        context.onWarn = onCompileWarn;\n    }\n    if (((__INTLIFY_JIT_COMPILATION__ && !__INTLIFY_DROP_MESSAGE_COMPILER__)) &&\n        isString(message)) {\n        // check HTML message\n        const warnHtmlMessage = isBoolean(context.warnHtmlMessage)\n            ? context.warnHtmlMessage\n            : true;\n        (process.env.NODE_ENV !== 'production') && checkHtmlMessage(message, warnHtmlMessage);\n        // check caches\n        const onCacheKey = context.onCacheKey || defaultOnCacheKey;\n        const cacheKey = onCacheKey(message);\n        const cached = compileCache[cacheKey];\n        if (cached) {\n            return cached;\n        }\n        // compile with JIT mode\n        const { ast, detectError } = baseCompile(message, {\n            ...context,\n            location: (process.env.NODE_ENV !== 'production'),\n            jit: true\n        });\n        // compose message function from AST\n        const msg = format(ast);\n        // if occurred compile error, don't cache\n        return !detectError\n            ? (compileCache[cacheKey] = msg)\n            : msg;\n    }\n    else {\n        if ((process.env.NODE_ENV !== 'production') && !isMessageAST(message)) {\n            warn(`the message that is resolve with key '${context.key}' is not supported for jit compilation`);\n            return (() => message);\n        }\n        // AST case (passed from bundler)\n        const cacheKey = message.cacheKey;\n        if (cacheKey) {\n            const cached = compileCache[cacheKey];\n            if (cached) {\n                return cached;\n            }\n            // compose message function from message (AST)\n            return (compileCache[cacheKey] =\n                format(message));\n        }\n        else {\n            return format(message);\n        }\n    }\n}\n\nconst NOOP_MESSAGE_FUNCTION = () => '';\nconst isMessageFunction = (val) => isFunction(val);\n// implementation of `translate` function\nfunction translate(context, ...args) {\n    const { fallbackFormat, postTranslation, unresolving, messageCompiler, fallbackLocale, messages } = context;\n    const [key, options] = parseTranslateArgs(...args);\n    const missingWarn = isBoolean(options.missingWarn)\n        ? options.missingWarn\n        : context.missingWarn;\n    const fallbackWarn = isBoolean(options.fallbackWarn)\n        ? options.fallbackWarn\n        : context.fallbackWarn;\n    const escapeParameter = isBoolean(options.escapeParameter)\n        ? options.escapeParameter\n        : context.escapeParameter;\n    const resolvedMessage = !!options.resolvedMessage;\n    // prettier-ignore\n    const defaultMsgOrKey = isString(options.default) || isBoolean(options.default) // default by function option\n        ? !isBoolean(options.default)\n            ? options.default\n            : (!messageCompiler ? () => key : key)\n        : fallbackFormat // default by `fallbackFormat` option\n            ? (!messageCompiler ? () => key : key)\n            : '';\n    const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== '';\n    const locale = getLocale(context, options);\n    // escape params\n    escapeParameter && escapeParams(options);\n    // resolve message format\n    // eslint-disable-next-line prefer-const\n    let [formatScope, targetLocale, message] = !resolvedMessage\n        ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn)\n        : [\n            key,\n            locale,\n            messages[locale] || create()\n        ];\n    // NOTE:\n    //  Fix to work around `ssrTransfrom` bug in Vite.\n    //  https://github.com/vitejs/vite/issues/4306\n    //  To get around this, use temporary variables.\n    //  https://github.com/nuxt/framework/issues/1461#issuecomment-954606243\n    let format = formatScope;\n    // if you use default message, set it as message format!\n    let cacheBaseKey = key;\n    if (!resolvedMessage &&\n        !(isString(format) ||\n            isMessageAST(format) ||\n            isMessageFunction(format))) {\n        if (enableDefaultMsg) {\n            format = defaultMsgOrKey;\n            cacheBaseKey = format;\n        }\n    }\n    // checking message format and target locale\n    if (!resolvedMessage &&\n        (!(isString(format) ||\n            isMessageAST(format) ||\n            isMessageFunction(format)) ||\n            !isString(targetLocale))) {\n        return unresolving ? NOT_REOSLVED : key;\n    }\n    // TODO: refactor\n    if ((process.env.NODE_ENV !== 'production') && isString(format) && context.messageCompiler == null) {\n        warn(`The message format compilation is not supported in this build. ` +\n            `Because message compiler isn't included. ` +\n            `You need to pre-compilation all message format. ` +\n            `So translate function return '${key}'.`);\n        return key;\n    }\n    // setup compile error detecting\n    let occurred = false;\n    const onError = () => {\n        occurred = true;\n    };\n    // compile message format\n    const msg = !isMessageFunction(format)\n        ? compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError)\n        : format;\n    // if occurred compile error, return the message format\n    if (occurred) {\n        return format;\n    }\n    // evaluate message with context\n    const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);\n    const msgContext = createMessageContext(ctxOptions);\n    const messaged = evaluateMessage(context, msg, msgContext);\n    // if use post translation option, proceed it with handler\n    let ret = postTranslation\n        ? postTranslation(messaged, key)\n        : messaged;\n    // apply HTML sanitization for security\n    if (escapeParameter && isString(ret)) {\n        ret = sanitizeTranslatedHtml(ret);\n    }\n    // NOTE: experimental !!\n    if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n        // prettier-ignore\n        const payloads = {\n            timestamp: Date.now(),\n            key: isString(key)\n                ? key\n                : isMessageFunction(format)\n                    ? format.key\n                    : '',\n            locale: targetLocale || (isMessageFunction(format)\n                ? format.locale\n                : ''),\n            format: isString(format)\n                ? format\n                : isMessageFunction(format)\n                    ? format.source\n                    : '',\n            message: ret\n        };\n        payloads.meta = assign({}, context.__meta, getAdditionalMeta() || {});\n        translateDevTools(payloads);\n    }\n    return ret;\n}\nfunction escapeParams(options) {\n    if (isArray(options.list)) {\n        options.list = options.list.map(item => isString(item) ? escapeHtml(item) : item);\n    }\n    else if (isObject(options.named)) {\n        Object.keys(options.named).forEach(key => {\n            if (isString(options.named[key])) {\n                options.named[key] = escapeHtml(options.named[key]);\n            }\n        });\n    }\n}\nfunction resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {\n    const { messages, onWarn, messageResolver: resolveValue, localeFallbacker } = context;\n    const locales = localeFallbacker(context, fallbackLocale, locale); // eslint-disable-line @typescript-eslint/no-explicit-any\n    let message = create();\n    let targetLocale;\n    let format = null;\n    let from = locale;\n    let to = null;\n    const type = 'translate';\n    for (let i = 0; i < locales.length; i++) {\n        targetLocale = to = locales[i];\n        if ((process.env.NODE_ENV !== 'production') &&\n            locale !== targetLocale &&\n            !isAlmostSameLocale(locale, targetLocale) &&\n            isTranslateFallbackWarn(fallbackWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {\n                key,\n                target: targetLocale\n            }));\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\n            const emitter = context.__v_emitter;\n            if (emitter) {\n                emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                    type,\n                    key,\n                    from,\n                    to,\n                    groupId: `${type}:${key}`\n                });\n            }\n        }\n        message =\n            messages[targetLocale] || create();\n        // for vue-devtools timeline event\n        let start = null;\n        let startTag;\n        let endTag;\n        if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n            start = window.performance.now();\n            startTag = 'intlify-message-resolve-start';\n            endTag = 'intlify-message-resolve-end';\n            mark && mark(startTag);\n        }\n        if ((format = resolveValue(message, key)) === null) {\n            // if null, resolve with object key path\n            format = message[key]; // eslint-disable-line @typescript-eslint/no-explicit-any\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n            const end = window.performance.now();\n            const emitter = context.__v_emitter;\n            if (emitter && start && format) {\n                emitter.emit(\"message-resolve\" /* VueDevToolsTimelineEvents.MESSAGE_RESOLVE */, {\n                    type: \"message-resolve\" /* VueDevToolsTimelineEvents.MESSAGE_RESOLVE */,\n                    key,\n                    message: format,\n                    time: end - start,\n                    groupId: `${type}:${key}`\n                });\n            }\n            if (startTag && endTag && mark && measure) {\n                mark(endTag);\n                measure('intlify message resolve', startTag, endTag);\n            }\n        }\n        if (isString(format) || isMessageAST(format) || isMessageFunction(format)) {\n            break;\n        }\n        if (!isImplicitFallback(targetLocale, locales)) {\n            const missingRet = handleMissing(context, // eslint-disable-line @typescript-eslint/no-explicit-any\n            key, targetLocale, missingWarn, type);\n            if (missingRet !== key) {\n                format = missingRet;\n            }\n        }\n        from = to;\n    }\n    return [format, targetLocale, message];\n}\nfunction compileMessageFormat(context, key, targetLocale, format, cacheBaseKey, onError) {\n    const { messageCompiler, warnHtmlMessage } = context;\n    if (isMessageFunction(format)) {\n        const msg = format;\n        msg.locale = msg.locale || targetLocale;\n        msg.key = msg.key || key;\n        return msg;\n    }\n    if (messageCompiler == null) {\n        const msg = (() => format);\n        msg.locale = targetLocale;\n        msg.key = key;\n        return msg;\n    }\n    // for vue-devtools timeline event\n    let start = null;\n    let startTag;\n    let endTag;\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        start = window.performance.now();\n        startTag = 'intlify-message-compilation-start';\n        endTag = 'intlify-message-compilation-end';\n        mark && mark(startTag);\n    }\n    const msg = messageCompiler(format, getCompileContext(context, targetLocale, cacheBaseKey, format, warnHtmlMessage, onError));\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        const end = window.performance.now();\n        const emitter = context.__v_emitter;\n        if (emitter && start) {\n            emitter.emit(\"message-compilation\" /* VueDevToolsTimelineEvents.MESSAGE_COMPILATION */, {\n                type: \"message-compilation\" /* VueDevToolsTimelineEvents.MESSAGE_COMPILATION */,\n                message: format,\n                time: end - start,\n                groupId: `${'translate'}:${key}`\n            });\n        }\n        if (startTag && endTag && mark && measure) {\n            mark(endTag);\n            measure('intlify message compilation', startTag, endTag);\n        }\n    }\n    msg.locale = targetLocale;\n    msg.key = key;\n    msg.source = format;\n    return msg;\n}\nfunction evaluateMessage(context, msg, msgCtx) {\n    // for vue-devtools timeline event\n    let start = null;\n    let startTag;\n    let endTag;\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        start = window.performance.now();\n        startTag = 'intlify-message-evaluation-start';\n        endTag = 'intlify-message-evaluation-end';\n        mark && mark(startTag);\n    }\n    const messaged = msg(msgCtx);\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production') && inBrowser) {\n        const end = window.performance.now();\n        const emitter = context.__v_emitter;\n        if (emitter && start) {\n            emitter.emit(\"message-evaluation\" /* VueDevToolsTimelineEvents.MESSAGE_EVALUATION */, {\n                type: \"message-evaluation\" /* VueDevToolsTimelineEvents.MESSAGE_EVALUATION */,\n                value: messaged,\n                time: end - start,\n                groupId: `${'translate'}:${msg.key}`\n            });\n        }\n        if (startTag && endTag && mark && measure) {\n            mark(endTag);\n            measure('intlify message evaluation', startTag, endTag);\n        }\n    }\n    return messaged;\n}\n/** @internal */\nfunction parseTranslateArgs(...args) {\n    const [arg1, arg2, arg3] = args;\n    const options = create();\n    if (!isString(arg1) &&\n        !isNumber(arg1) &&\n        !isMessageFunction(arg1) &&\n        !isMessageAST(arg1)) {\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n    }\n    // prettier-ignore\n    const key = isNumber(arg1)\n        ? String(arg1)\n        : isMessageFunction(arg1)\n            ? arg1\n            : arg1;\n    if (isNumber(arg2)) {\n        options.plural = arg2;\n    }\n    else if (isString(arg2)) {\n        options.default = arg2;\n    }\n    else if (isPlainObject(arg2) && !isEmptyObject(arg2)) {\n        options.named = arg2;\n    }\n    else if (isArray(arg2)) {\n        options.list = arg2;\n    }\n    if (isNumber(arg3)) {\n        options.plural = arg3;\n    }\n    else if (isString(arg3)) {\n        options.default = arg3;\n    }\n    else if (isPlainObject(arg3)) {\n        assign(options, arg3);\n    }\n    return [key, options];\n}\nfunction getCompileContext(context, locale, key, source, warnHtmlMessage, onError) {\n    return {\n        locale,\n        key,\n        warnHtmlMessage,\n        onError: (err) => {\n            onError && onError(err);\n            if ((process.env.NODE_ENV !== 'production')) {\n                const _source = getSourceForCodeFrame(source);\n                const message = `Message compilation error: ${err.message}`;\n                const codeFrame = err.location &&\n                    _source &&\n                    generateCodeFrame(_source, err.location.start.offset, err.location.end.offset);\n                const emitter = context.__v_emitter;\n                if (emitter && _source) {\n                    emitter.emit(\"compile-error\" /* VueDevToolsTimelineEvents.COMPILE_ERROR */, {\n                        message: _source,\n                        error: err.message,\n                        start: err.location && err.location.start.offset,\n                        end: err.location && err.location.end.offset,\n                        groupId: `${'translate'}:${key}`\n                    });\n                }\n                console.error(codeFrame ? `${message}\\n${codeFrame}` : message);\n            }\n            else {\n                throw err;\n            }\n        },\n        onCacheKey: (source) => generateFormatCacheKey(locale, key, source)\n    };\n}\nfunction getSourceForCodeFrame(source) {\n    if (isString(source)) {\n        return source;\n    }\n    else {\n        if (source.loc && source.loc.source) {\n            return source.loc.source;\n        }\n    }\n}\nfunction getMessageContextOptions(context, locale, message, options) {\n    const { modifiers, pluralRules, messageResolver: resolveValue, fallbackLocale, fallbackWarn, missingWarn, fallbackContext } = context;\n    const resolveMessage = (key) => {\n        let val = resolveValue(message, key);\n        // fallback to root context\n        if (val == null && fallbackContext) {\n            const [, , message] = resolveMessageFormat(fallbackContext, key, locale, fallbackLocale, fallbackWarn, missingWarn);\n            val = resolveValue(message, key);\n        }\n        if (isString(val) || isMessageAST(val)) {\n            let occurred = false;\n            const onError = () => {\n                occurred = true;\n            };\n            const msg = compileMessageFormat(context, key, locale, val, key, onError);\n            return !occurred\n                ? msg\n                : NOOP_MESSAGE_FUNCTION;\n        }\n        else if (isMessageFunction(val)) {\n            return val;\n        }\n        else {\n            // TODO: should be implemented warning message\n            return NOOP_MESSAGE_FUNCTION;\n        }\n    };\n    const ctxOptions = {\n        locale,\n        modifiers,\n        pluralRules,\n        messages: resolveMessage\n    };\n    if (context.processor) {\n        ctxOptions.processor = context.processor;\n    }\n    if (options.list) {\n        ctxOptions.list = options.list;\n    }\n    if (options.named) {\n        ctxOptions.named = options.named;\n    }\n    if (isNumber(options.plural)) {\n        ctxOptions.pluralIndex = options.plural;\n    }\n    return ctxOptions;\n}\n\nconst intlDefined = typeof Intl !== 'undefined';\nconst Availabilities = {\n    dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== 'undefined',\n    numberFormat: intlDefined && typeof Intl.NumberFormat !== 'undefined'\n};\n\n// implementation of `datetime` function\nfunction datetime(context, ...args) {\n    const { datetimeFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;\n    const { __datetimeFormatters } = context;\n    if ((process.env.NODE_ENV !== 'production') && !Availabilities.dateTimeFormat) {\n        onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));\n        return MISSING_RESOLVE_VALUE;\n    }\n    const [key, value, options, overrides] = parseDateTimeArgs(...args);\n    const missingWarn = isBoolean(options.missingWarn)\n        ? options.missingWarn\n        : context.missingWarn;\n    const fallbackWarn = isBoolean(options.fallbackWarn)\n        ? options.fallbackWarn\n        : context.fallbackWarn;\n    const part = !!options.part;\n    const locale = getLocale(context, options);\n    const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any\n    fallbackLocale, locale);\n    if (!isString(key) || key === '') {\n        return new Intl.DateTimeFormat(locale, overrides).format(value);\n    }\n    // resolve format\n    let datetimeFormat = {};\n    let targetLocale;\n    let format = null;\n    let from = locale;\n    let to = null;\n    const type = 'datetime format';\n    for (let i = 0; i < locales.length; i++) {\n        targetLocale = to = locales[i];\n        if ((process.env.NODE_ENV !== 'production') &&\n            locale !== targetLocale &&\n            isTranslateFallbackWarn(fallbackWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {\n                key,\n                target: targetLocale\n            }));\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\n            const emitter = context.__v_emitter;\n            if (emitter) {\n                emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                    type,\n                    key,\n                    from,\n                    to,\n                    groupId: `${type}:${key}`\n                });\n            }\n        }\n        datetimeFormat =\n            datetimeFormats[targetLocale] || {};\n        format = datetimeFormat[key];\n        if (isPlainObject(format))\n            break;\n        handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\n        from = to;\n    }\n    // checking format and target locale\n    if (!isPlainObject(format) || !isString(targetLocale)) {\n        return unresolving ? NOT_REOSLVED : key;\n    }\n    let id = `${targetLocale}__${key}`;\n    if (!isEmptyObject(overrides)) {\n        id = `${id}__${JSON.stringify(overrides)}`;\n    }\n    let formatter = __datetimeFormatters.get(id);\n    if (!formatter) {\n        formatter = new Intl.DateTimeFormat(targetLocale, assign({}, format, overrides));\n        __datetimeFormatters.set(id, formatter);\n    }\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\n}\n/** @internal */\nconst DATETIME_FORMAT_OPTIONS_KEYS = [\n    'localeMatcher',\n    'weekday',\n    'era',\n    'year',\n    'month',\n    'day',\n    'hour',\n    'minute',\n    'second',\n    'timeZoneName',\n    'formatMatcher',\n    'hour12',\n    'timeZone',\n    'dateStyle',\n    'timeStyle',\n    'calendar',\n    'dayPeriod',\n    'numberingSystem',\n    'hourCycle',\n    'fractionalSecondDigits'\n];\n/** @internal */\nfunction parseDateTimeArgs(...args) {\n    const [arg1, arg2, arg3, arg4] = args;\n    const options = create();\n    let overrides = create();\n    let value;\n    if (isString(arg1)) {\n        // Only allow ISO strings - other date formats are often supported,\n        // but may cause different results in different browsers.\n        const matches = arg1.match(/(\\d{4}-\\d{2}-\\d{2})(T|\\s)?(.*)/);\n        if (!matches) {\n            throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\n        }\n        // Some browsers can not parse the iso datetime separated by space,\n        // this is a compromise solution by replace the 'T'/' ' with 'T'\n        const dateTime = matches[3]\n            ? matches[3].trim().startsWith('T')\n                ? `${matches[1].trim()}${matches[3].trim()}`\n                : `${matches[1].trim()}T${matches[3].trim()}`\n            : matches[1].trim();\n        value = new Date(dateTime);\n        try {\n            // This will fail if the date is not valid\n            value.toISOString();\n        }\n        catch (e) {\n            throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);\n        }\n    }\n    else if (isDate(arg1)) {\n        if (isNaN(arg1.getTime())) {\n            throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);\n        }\n        value = arg1;\n    }\n    else if (isNumber(arg1)) {\n        value = arg1;\n    }\n    else {\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n    }\n    if (isString(arg2)) {\n        options.key = arg2;\n    }\n    else if (isPlainObject(arg2)) {\n        Object.keys(arg2).forEach(key => {\n            if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {\n                overrides[key] = arg2[key];\n            }\n            else {\n                options[key] = arg2[key];\n            }\n        });\n    }\n    if (isString(arg3)) {\n        options.locale = arg3;\n    }\n    else if (isPlainObject(arg3)) {\n        overrides = arg3;\n    }\n    if (isPlainObject(arg4)) {\n        overrides = arg4;\n    }\n    return [options.key || '', value, options, overrides];\n}\n/** @internal */\nfunction clearDateTimeFormat(ctx, locale, format) {\n    const context = ctx;\n    for (const key in format) {\n        const id = `${locale}__${key}`;\n        if (!context.__datetimeFormatters.has(id)) {\n            continue;\n        }\n        context.__datetimeFormatters.delete(id);\n    }\n}\n\n// implementation of `number` function\nfunction number(context, ...args) {\n    const { numberFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;\n    const { __numberFormatters } = context;\n    if ((process.env.NODE_ENV !== 'production') && !Availabilities.numberFormat) {\n        onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));\n        return MISSING_RESOLVE_VALUE;\n    }\n    const [key, value, options, overrides] = parseNumberArgs(...args);\n    const missingWarn = isBoolean(options.missingWarn)\n        ? options.missingWarn\n        : context.missingWarn;\n    const fallbackWarn = isBoolean(options.fallbackWarn)\n        ? options.fallbackWarn\n        : context.fallbackWarn;\n    const part = !!options.part;\n    const locale = getLocale(context, options);\n    const locales = localeFallbacker(context, // eslint-disable-line @typescript-eslint/no-explicit-any\n    fallbackLocale, locale);\n    if (!isString(key) || key === '') {\n        return new Intl.NumberFormat(locale, overrides).format(value);\n    }\n    // resolve format\n    let numberFormat = {};\n    let targetLocale;\n    let format = null;\n    let from = locale;\n    let to = null;\n    const type = 'number format';\n    for (let i = 0; i < locales.length; i++) {\n        targetLocale = to = locales[i];\n        if ((process.env.NODE_ENV !== 'production') &&\n            locale !== targetLocale &&\n            isTranslateFallbackWarn(fallbackWarn, key)) {\n            onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {\n                key,\n                target: targetLocale\n            }));\n        }\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production') && locale !== targetLocale) {\n            const emitter = context.__v_emitter;\n            if (emitter) {\n                emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                    type,\n                    key,\n                    from,\n                    to,\n                    groupId: `${type}:${key}`\n                });\n            }\n        }\n        numberFormat =\n            numberFormats[targetLocale] || {};\n        format = numberFormat[key];\n        if (isPlainObject(format))\n            break;\n        handleMissing(context, key, targetLocale, missingWarn, type); // eslint-disable-line @typescript-eslint/no-explicit-any\n        from = to;\n    }\n    // checking format and target locale\n    if (!isPlainObject(format) || !isString(targetLocale)) {\n        return unresolving ? NOT_REOSLVED : key;\n    }\n    let id = `${targetLocale}__${key}`;\n    if (!isEmptyObject(overrides)) {\n        id = `${id}__${JSON.stringify(overrides)}`;\n    }\n    let formatter = __numberFormatters.get(id);\n    if (!formatter) {\n        formatter = new Intl.NumberFormat(targetLocale, assign({}, format, overrides));\n        __numberFormatters.set(id, formatter);\n    }\n    return !part ? formatter.format(value) : formatter.formatToParts(value);\n}\n/** @internal */\nconst NUMBER_FORMAT_OPTIONS_KEYS = [\n    'localeMatcher',\n    'style',\n    'currency',\n    'currencyDisplay',\n    'currencySign',\n    'useGrouping',\n    'minimumIntegerDigits',\n    'minimumFractionDigits',\n    'maximumFractionDigits',\n    'minimumSignificantDigits',\n    'maximumSignificantDigits',\n    'compactDisplay',\n    'notation',\n    'signDisplay',\n    'unit',\n    'unitDisplay',\n    'roundingMode',\n    'roundingPriority',\n    'roundingIncrement',\n    'trailingZeroDisplay'\n];\n/** @internal */\nfunction parseNumberArgs(...args) {\n    const [arg1, arg2, arg3, arg4] = args;\n    const options = create();\n    let overrides = create();\n    if (!isNumber(arg1)) {\n        throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);\n    }\n    const value = arg1;\n    if (isString(arg2)) {\n        options.key = arg2;\n    }\n    else if (isPlainObject(arg2)) {\n        Object.keys(arg2).forEach(key => {\n            if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {\n                overrides[key] = arg2[key];\n            }\n            else {\n                options[key] = arg2[key];\n            }\n        });\n    }\n    if (isString(arg3)) {\n        options.locale = arg3;\n    }\n    else if (isPlainObject(arg3)) {\n        overrides = arg3;\n    }\n    if (isPlainObject(arg4)) {\n        overrides = arg4;\n    }\n    return [options.key || '', value, options, overrides];\n}\n/** @internal */\nfunction clearNumberFormat(ctx, locale, format) {\n    const context = ctx;\n    for (const key in format) {\n        const id = `${locale}__${key}`;\n        if (!context.__numberFormatters.has(id)) {\n            continue;\n        }\n        context.__numberFormatters.delete(id);\n    }\n}\n\n{\n    initFeatureFlags();\n}\n\nexport { AST_NODE_PROPS_KEYS, CoreErrorCodes, CoreWarnCodes, DATETIME_FORMAT_OPTIONS_KEYS, DEFAULT_LOCALE, DEFAULT_MESSAGE_DATA_TYPE, MISSING_RESOLVE_VALUE, NOT_REOSLVED, NUMBER_FORMAT_OPTIONS_KEYS, VERSION, clearCompileCache, clearDateTimeFormat, clearNumberFormat, compile, compileToFunction, createCoreContext, createCoreError, createMessageContext, datetime, fallbackWithLocaleChain, fallbackWithSimple, getAdditionalMeta, getDevToolsHook, getFallbackContext, getLocale, getWarnMessage, handleMissing, initI18nDevTools, isAlmostSameLocale, isImplicitFallback, isMessageAST, isMessageFunction, isTranslateFallbackWarn, isTranslateMissingWarn, number, parse, parseDateTimeArgs, parseNumberArgs, parseTranslateArgs, registerLocaleFallbacker, registerMessageCompiler, registerMessageResolver, resolveLocale, resolveValue, resolveWithKeyValue, setAdditionalMeta, setDevToolsHook, setFallbackContext, translate, translateDevTools, updateFallbackLocale };\n", "/*!\n  * vue-i18n v9.14.5\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { getGlobalThis, incrementer, format, makeSymbol, isPlainObject, isArray, create, deepCopy, isString, hasOwn, isObject, warn, warnOnce, isBoolean, isRegExp, isFunction, inBrowser, assign, isNumber, createEmitter, isEmptyObject } from '@intlify/shared';\nimport { CoreWarnCodes, CoreErrorCodes, createCompileError, isMessageAST, AST_NODE_PROPS_KEYS, DEFAULT_LOCALE, updateFallbackLocale, setFallbackContext, createCoreContext, clearDateTimeFormat, clearNumberFormat, setAdditionalMeta, getFallbackContext, NOT_REOSLVED, isTranslateFallbackWarn, isTranslateMissingWarn, parseTranslateArgs, translate, MISSING_RESOLVE_VALUE, parseDateTimeArgs, datetime, parseNumberArgs, number, isMessageFunction, fallbackWithLocale<PERSON>hain, NUMBER_FORMAT_OPTIONS_KEYS, DATETIME_FORMAT_OPTIONS_KEYS, registerMessageCompiler, compile, compileToFunction, registerMessageResolver, resolveValue, registerLocaleFallbacker, setDevToolsHook } from '@intlify/core-base';\nimport { createVNode, Text, computed, watch, getCurrentInstance, ref, shallowRef, Fragment, defineComponent, h, effectScope, inject, onMounted, onUnmounted, onBeforeMount, isRef } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\n/**\n * Vue I18n Version\n *\n * @remarks\n * Semver format. Same format as the package.json `version` field.\n *\n * @VueI18nGeneral\n */\nconst VERSION = '9.14.5';\n/**\n * This is only called in esm-bundler builds.\n * istanbul-ignore-next\n */\nfunction initFeatureFlags() {\n    if (typeof __VUE_I18N_FULL_INSTALL__ !== 'boolean') {\n        getGlobalThis().__VUE_I18N_FULL_INSTALL__ = true;\n    }\n    if (typeof __VUE_I18N_LEGACY_API__ !== 'boolean') {\n        getGlobalThis().__VUE_I18N_LEGACY_API__ = true;\n    }\n    if (typeof __INTLIFY_JIT_COMPILATION__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_JIT_COMPILATION__ = false;\n    }\n    if (typeof __INTLIFY_DROP_MESSAGE_COMPILER__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_DROP_MESSAGE_COMPILER__ = false;\n    }\n    if (typeof __INTLIFY_PROD_DEVTOOLS__ !== 'boolean') {\n        getGlobalThis().__INTLIFY_PROD_DEVTOOLS__ = false;\n    }\n}\n\nconst code$1 = CoreWarnCodes.__EXTEND_POINT__;\nconst inc$1 = incrementer(code$1);\nconst I18nWarnCodes = {\n    FALLBACK_TO_ROOT: code$1, // 9\n    NOT_SUPPORTED_PRESERVE: inc$1(), // 10\n    NOT_SUPPORTED_FORMATTER: inc$1(), // 11\n    NOT_SUPPORTED_PRESERVE_DIRECTIVE: inc$1(), // 12\n    NOT_SUPPORTED_GET_CHOICE_INDEX: inc$1(), // 13\n    COMPONENT_NAME_LEGACY_COMPATIBLE: inc$1(), // 14\n    NOT_FOUND_PARENT_SCOPE: inc$1(), // 15\n    IGNORE_OBJ_FLATTEN: inc$1(), // 16\n    NOTICE_DROP_ALLOW_COMPOSITION: inc$1(), // 17\n    NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG: inc$1() // 18\n};\nconst warnMessages = {\n    [I18nWarnCodes.FALLBACK_TO_ROOT]: `Fall back to {type} '{key}' with root locale.`,\n    [I18nWarnCodes.NOT_SUPPORTED_PRESERVE]: `Not supported 'preserve'.`,\n    [I18nWarnCodes.NOT_SUPPORTED_FORMATTER]: `Not supported 'formatter'.`,\n    [I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE]: `Not supported 'preserveDirectiveContent'.`,\n    [I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX]: `Not supported 'getChoiceIndex'.`,\n    [I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE]: `Component name legacy compatible: '{name}' -> 'i18n'`,\n    [I18nWarnCodes.NOT_FOUND_PARENT_SCOPE]: `Not found parent scope. use the global scope.`,\n    [I18nWarnCodes.IGNORE_OBJ_FLATTEN]: `Ignore object flatten: '{key}' key has an string value`,\n    [I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION]: `'allowComposition' option will be dropped in the next major version. For more information, please see 👉 https://tinyurl.com/2p97mcze`,\n    [I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG]: `'translateExistCompatible' option will be dropped in the next major version.`\n};\nfunction getWarnMessage(code, ...args) {\n    return format(warnMessages[code], ...args);\n}\n\nconst code = CoreErrorCodes.__EXTEND_POINT__;\nconst inc = incrementer(code);\nconst I18nErrorCodes = {\n    // composer module errors\n    UNEXPECTED_RETURN_TYPE: code, // 24\n    // legacy module errors\n    INVALID_ARGUMENT: inc(), // 25\n    // i18n module errors\n    MUST_BE_CALL_SETUP_TOP: inc(), // 26\n    NOT_INSTALLED: inc(), // 27\n    NOT_AVAILABLE_IN_LEGACY_MODE: inc(), // 28\n    // directive module errors\n    REQUIRED_VALUE: inc(), // 29\n    INVALID_VALUE: inc(), // 30\n    // vue-devtools errors\n    CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: inc(), // 31\n    NOT_INSTALLED_WITH_PROVIDE: inc(), // 32\n    // unexpected error\n    UNEXPECTED_ERROR: inc(), // 33\n    // not compatible legacy vue-i18n constructor\n    NOT_COMPATIBLE_LEGACY_VUE_I18N: inc(), // 34\n    // bridge support vue 2.x only\n    BRIDGE_SUPPORT_VUE_2_ONLY: inc(), // 35\n    // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``\n    MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: inc(), // 36\n    // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly\n    NOT_AVAILABLE_COMPOSITION_IN_LEGACY: inc(), // 37\n    // for enhancement\n    __EXTEND_POINT__: inc() // 38\n};\nfunction createI18nError(code, ...args) {\n    return createCompileError(code, null, (process.env.NODE_ENV !== 'production') ? { messages: errorMessages, args } : undefined);\n}\nconst errorMessages = {\n    [I18nErrorCodes.UNEXPECTED_RETURN_TYPE]: 'Unexpected return type in composer',\n    [I18nErrorCodes.INVALID_ARGUMENT]: 'Invalid argument',\n    [I18nErrorCodes.MUST_BE_CALL_SETUP_TOP]: 'Must be called at the top of a `setup` function',\n    [I18nErrorCodes.NOT_INSTALLED]: 'Need to install with `app.use` function',\n    [I18nErrorCodes.UNEXPECTED_ERROR]: 'Unexpected error',\n    [I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE]: 'Not available in legacy mode',\n    [I18nErrorCodes.REQUIRED_VALUE]: `Required in value: {0}`,\n    [I18nErrorCodes.INVALID_VALUE]: `Invalid value`,\n    [I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]: `Cannot setup vue-devtools plugin`,\n    [I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE]: 'Need to install with `provide` function',\n    [I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N]: 'Not compatible legacy VueI18n.',\n    [I18nErrorCodes.BRIDGE_SUPPORT_VUE_2_ONLY]: 'vue-i18n-bridge support Vue 2.x only',\n    [I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]: 'Must define ‘i18n’ option or custom block in Composition API with using local scope in Legacy API mode',\n    [I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]: 'Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly'\n};\n\nconst TranslateVNodeSymbol = \n/* #__PURE__*/ makeSymbol('__translateVNode');\nconst DatetimePartsSymbol = /* #__PURE__*/ makeSymbol('__datetimeParts');\nconst NumberPartsSymbol = /* #__PURE__*/ makeSymbol('__numberParts');\nconst EnableEmitter = /* #__PURE__*/ makeSymbol('__enableEmitter');\nconst DisableEmitter = /* #__PURE__*/ makeSymbol('__disableEmitter');\nconst SetPluralRulesSymbol = makeSymbol('__setPluralRules');\nmakeSymbol('__intlifyMeta');\nconst InejctWithOptionSymbol = \n/* #__PURE__*/ makeSymbol('__injectWithOption');\nconst DisposeSymbol = /* #__PURE__*/ makeSymbol('__dispose');\nconst __VUE_I18N_BRIDGE__ =  '__VUE_I18N_BRIDGE__';\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Transform flat json in obj to normal json in obj\n */\nfunction handleFlatJson(obj) {\n    // check obj\n    if (!isObject(obj)) {\n        return obj;\n    }\n    if (isMessageAST(obj)) {\n        return obj;\n    }\n    for (const key in obj) {\n        // check key\n        if (!hasOwn(obj, key)) {\n            continue;\n        }\n        // handle for normal json\n        if (!key.includes('.')) {\n            // recursive process value if value is also a object\n            if (isObject(obj[key])) {\n                handleFlatJson(obj[key]);\n            }\n        }\n        // handle for flat json, transform to normal json\n        else {\n            // go to the last object\n            const subKeys = key.split('.');\n            const lastIndex = subKeys.length - 1;\n            let currentObj = obj;\n            let hasStringValue = false;\n            for (let i = 0; i < lastIndex; i++) {\n                if (subKeys[i] === '__proto__') {\n                    throw new Error(`unsafe key: ${subKeys[i]}`);\n                }\n                if (!(subKeys[i] in currentObj)) {\n                    currentObj[subKeys[i]] = create();\n                }\n                if (!isObject(currentObj[subKeys[i]])) {\n                    (process.env.NODE_ENV !== 'production') &&\n                        warn(getWarnMessage(I18nWarnCodes.IGNORE_OBJ_FLATTEN, {\n                            key: subKeys[i]\n                        }));\n                    hasStringValue = true;\n                    break;\n                }\n                currentObj = currentObj[subKeys[i]];\n            }\n            // update last object value, delete old property\n            if (!hasStringValue) {\n                if (!isMessageAST(currentObj)) {\n                    currentObj[subKeys[lastIndex]] = obj[key];\n                    delete obj[key];\n                }\n                else {\n                    /**\n                     * NOTE:\n                     * if the last object is a message AST and subKeys[lastIndex] has message AST prop key, ignore to copy and key deletion\n                     */\n                    if (!AST_NODE_PROPS_KEYS.includes(subKeys[lastIndex])) {\n                        delete obj[key];\n                    }\n                }\n            }\n            // recursive process value if value is also a object\n            if (!isMessageAST(currentObj)) {\n                const target = currentObj[subKeys[lastIndex]];\n                if (isObject(target)) {\n                    handleFlatJson(target);\n                }\n            }\n        }\n    }\n    return obj;\n}\nfunction getLocaleMessages(locale, options) {\n    const { messages, __i18n, messageResolver, flatJson } = options;\n    // prettier-ignore\n    const ret = (isPlainObject(messages)\n        ? messages\n        : isArray(__i18n)\n            ? create()\n            : { [locale]: create() });\n    // merge locale messages of i18n custom block\n    if (isArray(__i18n)) {\n        __i18n.forEach(custom => {\n            if ('locale' in custom && 'resource' in custom) {\n                const { locale, resource } = custom;\n                if (locale) {\n                    ret[locale] = ret[locale] || create();\n                    deepCopy(resource, ret[locale]);\n                }\n                else {\n                    deepCopy(resource, ret);\n                }\n            }\n            else {\n                isString(custom) && deepCopy(JSON.parse(custom), ret);\n            }\n        });\n    }\n    // handle messages for flat json\n    if (messageResolver == null && flatJson) {\n        for (const key in ret) {\n            if (hasOwn(ret, key)) {\n                handleFlatJson(ret[key]);\n            }\n        }\n    }\n    return ret;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getComponentOptions(instance) {\n    return instance.type ;\n}\nfunction adjustI18nResources(gl, options, componentOptions // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n    let messages = isObject(options.messages)\n        ? options.messages\n        : create();\n    if ('__i18nGlobal' in componentOptions) {\n        messages = getLocaleMessages(gl.locale.value, {\n            messages,\n            __i18n: componentOptions.__i18nGlobal\n        });\n    }\n    // merge locale messages\n    const locales = Object.keys(messages);\n    if (locales.length) {\n        locales.forEach(locale => {\n            gl.mergeLocaleMessage(locale, messages[locale]);\n        });\n    }\n    {\n        // merge datetime formats\n        if (isObject(options.datetimeFormats)) {\n            const locales = Object.keys(options.datetimeFormats);\n            if (locales.length) {\n                locales.forEach(locale => {\n                    gl.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);\n                });\n            }\n        }\n        // merge number formats\n        if (isObject(options.numberFormats)) {\n            const locales = Object.keys(options.numberFormats);\n            if (locales.length) {\n                locales.forEach(locale => {\n                    gl.mergeNumberFormat(locale, options.numberFormats[locale]);\n                });\n            }\n        }\n    }\n}\nfunction createTextNode(key) {\n    return createVNode(Text, null, key, 0)\n        ;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n// extend VNode interface\nconst DEVTOOLS_META = '__INTLIFY_META__';\nconst NOOP_RETURN_ARRAY = () => [];\nconst NOOP_RETURN_FALSE = () => false;\nlet composerID = 0;\nfunction defineCoreMissingHandler(missing) {\n    return ((ctx, locale, key, type) => {\n        return missing(locale, key, getCurrentInstance() || undefined, type);\n    });\n}\n// for Intlify DevTools\n/* #__NO_SIDE_EFFECTS__ */\nconst getMetaInfo = () => {\n    const instance = getCurrentInstance();\n    let meta = null; // eslint-disable-line @typescript-eslint/no-explicit-any\n    return instance && (meta = getComponentOptions(instance)[DEVTOOLS_META])\n        ? { [DEVTOOLS_META]: meta } // eslint-disable-line @typescript-eslint/no-explicit-any\n        : null;\n};\n/**\n * Create composer interface factory\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction createComposer(options = {}, VueI18nLegacy) {\n    const { __root, __injectWithOption } = options;\n    const _isGlobal = __root === undefined;\n    const flatJson = options.flatJson;\n    const _ref = inBrowser ? ref : shallowRef;\n    const translateExistCompatible = !!options.translateExistCompatible;\n    if ((process.env.NODE_ENV !== 'production')) {\n        if (translateExistCompatible && !false) {\n            warnOnce(getWarnMessage(I18nWarnCodes.NOTICE_DROP_TRANSLATE_EXIST_COMPATIBLE_FLAG));\n        }\n    }\n    let _inheritLocale = isBoolean(options.inheritLocale)\n        ? options.inheritLocale\n        : true;\n    const _locale = _ref(\n    // prettier-ignore\n    __root && _inheritLocale\n        ? __root.locale.value\n        : isString(options.locale)\n            ? options.locale\n            : DEFAULT_LOCALE);\n    const _fallbackLocale = _ref(\n    // prettier-ignore\n    __root && _inheritLocale\n        ? __root.fallbackLocale.value\n        : isString(options.fallbackLocale) ||\n            isArray(options.fallbackLocale) ||\n            isPlainObject(options.fallbackLocale) ||\n            options.fallbackLocale === false\n            ? options.fallbackLocale\n            : _locale.value);\n    const _messages = _ref(getLocaleMessages(_locale.value, options));\n    // prettier-ignore\n    const _datetimeFormats = _ref(isPlainObject(options.datetimeFormats)\n            ? options.datetimeFormats\n            : { [_locale.value]: {} })\n        ;\n    // prettier-ignore\n    const _numberFormats = _ref(isPlainObject(options.numberFormats)\n            ? options.numberFormats\n            : { [_locale.value]: {} })\n        ;\n    // warning suppress options\n    // prettier-ignore\n    let _missingWarn = __root\n        ? __root.missingWarn\n        : isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\n            ? options.missingWarn\n            : true;\n    // prettier-ignore\n    let _fallbackWarn = __root\n        ? __root.fallbackWarn\n        : isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\n            ? options.fallbackWarn\n            : true;\n    // prettier-ignore\n    let _fallbackRoot = __root\n        ? __root.fallbackRoot\n        : isBoolean(options.fallbackRoot)\n            ? options.fallbackRoot\n            : true;\n    // configure fall back to root\n    let _fallbackFormat = !!options.fallbackFormat;\n    // runtime missing\n    let _missing = isFunction(options.missing) ? options.missing : null;\n    let _runtimeMissing = isFunction(options.missing)\n        ? defineCoreMissingHandler(options.missing)\n        : null;\n    // postTranslation handler\n    let _postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : null;\n    // prettier-ignore\n    let _warnHtmlMessage = __root\n        ? __root.warnHtmlMessage\n        : isBoolean(options.warnHtmlMessage)\n            ? options.warnHtmlMessage\n            : true;\n    let _escapeParameter = !!options.escapeParameter;\n    // custom linked modifiers\n    // prettier-ignore\n    const _modifiers = __root\n        ? __root.modifiers\n        : isPlainObject(options.modifiers)\n            ? options.modifiers\n            : {};\n    // pluralRules\n    let _pluralRules = options.pluralRules || (__root && __root.pluralRules);\n    // runtime context\n    // eslint-disable-next-line prefer-const\n    let _context;\n    const getCoreContext = () => {\n        _isGlobal && setFallbackContext(null);\n        const ctxOptions = {\n            version: VERSION,\n            locale: _locale.value,\n            fallbackLocale: _fallbackLocale.value,\n            messages: _messages.value,\n            modifiers: _modifiers,\n            pluralRules: _pluralRules,\n            missing: _runtimeMissing === null ? undefined : _runtimeMissing,\n            missingWarn: _missingWarn,\n            fallbackWarn: _fallbackWarn,\n            fallbackFormat: _fallbackFormat,\n            unresolving: true,\n            postTranslation: _postTranslation === null ? undefined : _postTranslation,\n            warnHtmlMessage: _warnHtmlMessage,\n            escapeParameter: _escapeParameter,\n            messageResolver: options.messageResolver,\n            messageCompiler: options.messageCompiler,\n            __meta: { framework: 'vue' }\n        };\n        {\n            ctxOptions.datetimeFormats = _datetimeFormats.value;\n            ctxOptions.numberFormats = _numberFormats.value;\n            ctxOptions.__datetimeFormatters = isPlainObject(_context)\n                ? _context.__datetimeFormatters\n                : undefined;\n            ctxOptions.__numberFormatters = isPlainObject(_context)\n                ? _context.__numberFormatters\n                : undefined;\n        }\n        if ((process.env.NODE_ENV !== 'production')) {\n            ctxOptions.__v_emitter = isPlainObject(_context)\n                ? _context.__v_emitter\n                : undefined;\n        }\n        const ctx = createCoreContext(ctxOptions);\n        _isGlobal && setFallbackContext(ctx);\n        return ctx;\n    };\n    _context = getCoreContext();\n    updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n    // track reactivity\n    function trackReactivityValues() {\n        return [\n                _locale.value,\n                _fallbackLocale.value,\n                _messages.value,\n                _datetimeFormats.value,\n                _numberFormats.value\n            ]\n            ;\n    }\n    // locale\n    const locale = computed({\n        get: () => _locale.value,\n        set: val => {\n            _locale.value = val;\n            _context.locale = _locale.value;\n        }\n    });\n    // fallbackLocale\n    const fallbackLocale = computed({\n        get: () => _fallbackLocale.value,\n        set: val => {\n            _fallbackLocale.value = val;\n            _context.fallbackLocale = _fallbackLocale.value;\n            updateFallbackLocale(_context, _locale.value, val);\n        }\n    });\n    // messages\n    const messages = computed(() => _messages.value);\n    // datetimeFormats\n    const datetimeFormats = /* #__PURE__*/ computed(() => _datetimeFormats.value);\n    // numberFormats\n    const numberFormats = /* #__PURE__*/ computed(() => _numberFormats.value);\n    // getPostTranslationHandler\n    function getPostTranslationHandler() {\n        return isFunction(_postTranslation) ? _postTranslation : null;\n    }\n    // setPostTranslationHandler\n    function setPostTranslationHandler(handler) {\n        _postTranslation = handler;\n        _context.postTranslation = handler;\n    }\n    // getMissingHandler\n    function getMissingHandler() {\n        return _missing;\n    }\n    // setMissingHandler\n    function setMissingHandler(handler) {\n        if (handler !== null) {\n            _runtimeMissing = defineCoreMissingHandler(handler);\n        }\n        _missing = handler;\n        _context.missing = _runtimeMissing;\n    }\n    function isResolvedTranslateMessage(type, arg // eslint-disable-line @typescript-eslint/no-explicit-any\n    ) {\n        return type !== 'translate' || !arg.resolvedMessage;\n    }\n    const wrapWithDeps = (fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) => {\n        trackReactivityValues(); // track reactive dependency\n        // NOTE: experimental !!\n        let ret;\n        try {\n            if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n                setAdditionalMeta(getMetaInfo());\n            }\n            if (!_isGlobal) {\n                _context.fallbackContext = __root\n                    ? getFallbackContext()\n                    : undefined;\n            }\n            ret = fn(_context);\n        }\n        finally {\n            if ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n                setAdditionalMeta(null);\n            }\n            if (!_isGlobal) {\n                _context.fallbackContext = undefined;\n            }\n        }\n        if ((warnType !== 'translate exists' && // for not `te` (e.g `t`)\n            isNumber(ret) &&\n            ret === NOT_REOSLVED) ||\n            (warnType === 'translate exists' && !ret) // for `te`\n        ) {\n            const [key, arg2] = argumentParser();\n            if ((process.env.NODE_ENV !== 'production') &&\n                __root &&\n                isString(key) &&\n                isResolvedTranslateMessage(warnType, arg2)) {\n                if (_fallbackRoot &&\n                    (isTranslateFallbackWarn(_fallbackWarn, key) ||\n                        isTranslateMissingWarn(_missingWarn, key))) {\n                    warn(getWarnMessage(I18nWarnCodes.FALLBACK_TO_ROOT, {\n                        key,\n                        type: warnType\n                    }));\n                }\n                // for vue-devtools timeline event\n                if ((process.env.NODE_ENV !== 'production')) {\n                    const { __v_emitter: emitter } = _context;\n                    if (emitter && _fallbackRoot) {\n                        emitter.emit(\"fallback\" /* VueDevToolsTimelineEvents.FALBACK */, {\n                            type: warnType,\n                            key,\n                            to: 'global',\n                            groupId: `${warnType}:${key}`\n                        });\n                    }\n                }\n            }\n            return __root && _fallbackRoot\n                ? fallbackSuccess(__root)\n                : fallbackFail(key);\n        }\n        else if (successCondition(ret)) {\n            return ret;\n        }\n        else {\n            /* istanbul ignore next */\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_RETURN_TYPE);\n        }\n    };\n    // t\n    function t(...args) {\n        return wrapWithDeps(context => Reflect.apply(translate, null, [context, ...args]), () => parseTranslateArgs(...args), 'translate', root => Reflect.apply(root.t, root, [...args]), key => key, val => isString(val));\n    }\n    // rt\n    function rt(...args) {\n        const [arg1, arg2, arg3] = args;\n        if (arg3 && !isObject(arg3)) {\n            throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n        }\n        return t(...[arg1, arg2, assign({ resolvedMessage: true }, arg3 || {})]);\n    }\n    // d\n    function d(...args) {\n        return wrapWithDeps(context => Reflect.apply(datetime, null, [context, ...args]), () => parseDateTimeArgs(...args), 'datetime format', root => Reflect.apply(root.d, root, [...args]), () => MISSING_RESOLVE_VALUE, val => isString(val));\n    }\n    // n\n    function n(...args) {\n        return wrapWithDeps(context => Reflect.apply(number, null, [context, ...args]), () => parseNumberArgs(...args), 'number format', root => Reflect.apply(root.n, root, [...args]), () => MISSING_RESOLVE_VALUE, val => isString(val));\n    }\n    // for custom processor\n    function normalize(values) {\n        return values.map(val => isString(val) || isNumber(val) || isBoolean(val)\n            ? createTextNode(String(val))\n            : val);\n    }\n    const interpolate = (val) => val;\n    const processor = {\n        normalize,\n        interpolate,\n        type: 'vnode'\n    };\n    // translateVNode, using for `i18n-t` component\n    function translateVNode(...args) {\n        return wrapWithDeps(context => {\n            let ret;\n            const _context = context;\n            try {\n                _context.processor = processor;\n                ret = Reflect.apply(translate, null, [_context, ...args]);\n            }\n            finally {\n                _context.processor = null;\n            }\n            return ret;\n        }, () => parseTranslateArgs(...args), 'translate', \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        root => root[TranslateVNodeSymbol](...args), key => [createTextNode(key)], val => isArray(val));\n    }\n    // numberParts, using for `i18n-n` component\n    function numberParts(...args) {\n        return wrapWithDeps(context => Reflect.apply(number, null, [context, ...args]), () => parseNumberArgs(...args), 'number format', \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        root => root[NumberPartsSymbol](...args), NOOP_RETURN_ARRAY, val => isString(val) || isArray(val));\n    }\n    // datetimeParts, using for `i18n-d` component\n    function datetimeParts(...args) {\n        return wrapWithDeps(context => Reflect.apply(datetime, null, [context, ...args]), () => parseDateTimeArgs(...args), 'datetime format', \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        root => root[DatetimePartsSymbol](...args), NOOP_RETURN_ARRAY, val => isString(val) || isArray(val));\n    }\n    function setPluralRules(rules) {\n        _pluralRules = rules;\n        _context.pluralRules = _pluralRules;\n    }\n    // te\n    function te(key, locale) {\n        return wrapWithDeps(() => {\n            if (!key) {\n                return false;\n            }\n            const targetLocale = isString(locale) ? locale : _locale.value;\n            const message = getLocaleMessage(targetLocale);\n            const resolved = _context.messageResolver(message, key);\n            return !translateExistCompatible\n                ? isMessageAST(resolved) ||\n                    isMessageFunction(resolved) ||\n                    isString(resolved)\n                : resolved != null;\n        }, () => [key], 'translate exists', root => {\n            return Reflect.apply(root.te, root, [key, locale]);\n        }, NOOP_RETURN_FALSE, val => isBoolean(val));\n    }\n    function resolveMessages(key) {\n        let messages = null;\n        const locales = fallbackWithLocaleChain(_context, _fallbackLocale.value, _locale.value);\n        for (let i = 0; i < locales.length; i++) {\n            const targetLocaleMessages = _messages.value[locales[i]] || {};\n            const messageValue = _context.messageResolver(targetLocaleMessages, key);\n            if (messageValue != null) {\n                messages = messageValue;\n                break;\n            }\n        }\n        return messages;\n    }\n    // tm\n    function tm(key) {\n        const messages = resolveMessages(key);\n        // prettier-ignore\n        return messages != null\n            ? messages\n            : __root\n                ? __root.tm(key) || {}\n                : {};\n    }\n    // getLocaleMessage\n    function getLocaleMessage(locale) {\n        return (_messages.value[locale] || {});\n    }\n    // setLocaleMessage\n    function setLocaleMessage(locale, message) {\n        if (flatJson) {\n            const _message = { [locale]: message };\n            for (const key in _message) {\n                if (hasOwn(_message, key)) {\n                    handleFlatJson(_message[key]);\n                }\n            }\n            message = _message[locale];\n        }\n        _messages.value[locale] = message;\n        _context.messages = _messages.value;\n    }\n    // mergeLocaleMessage\n    function mergeLocaleMessage(locale, message) {\n        _messages.value[locale] = _messages.value[locale] || {};\n        const _message = { [locale]: message };\n        if (flatJson) {\n            for (const key in _message) {\n                if (hasOwn(_message, key)) {\n                    handleFlatJson(_message[key]);\n                }\n            }\n        }\n        message = _message[locale];\n        deepCopy(message, _messages.value[locale]);\n        _context.messages = _messages.value;\n    }\n    // getDateTimeFormat\n    function getDateTimeFormat(locale) {\n        return _datetimeFormats.value[locale] || {};\n    }\n    // setDateTimeFormat\n    function setDateTimeFormat(locale, format) {\n        _datetimeFormats.value[locale] = format;\n        _context.datetimeFormats = _datetimeFormats.value;\n        clearDateTimeFormat(_context, locale, format);\n    }\n    // mergeDateTimeFormat\n    function mergeDateTimeFormat(locale, format) {\n        _datetimeFormats.value[locale] = assign(_datetimeFormats.value[locale] || {}, format);\n        _context.datetimeFormats = _datetimeFormats.value;\n        clearDateTimeFormat(_context, locale, format);\n    }\n    // getNumberFormat\n    function getNumberFormat(locale) {\n        return _numberFormats.value[locale] || {};\n    }\n    // setNumberFormat\n    function setNumberFormat(locale, format) {\n        _numberFormats.value[locale] = format;\n        _context.numberFormats = _numberFormats.value;\n        clearNumberFormat(_context, locale, format);\n    }\n    // mergeNumberFormat\n    function mergeNumberFormat(locale, format) {\n        _numberFormats.value[locale] = assign(_numberFormats.value[locale] || {}, format);\n        _context.numberFormats = _numberFormats.value;\n        clearNumberFormat(_context, locale, format);\n    }\n    // for debug\n    composerID++;\n    // watch root locale & fallbackLocale\n    if (__root && inBrowser) {\n        watch(__root.locale, (val) => {\n            if (_inheritLocale) {\n                _locale.value = val;\n                _context.locale = val;\n                updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n            }\n        });\n        watch(__root.fallbackLocale, (val) => {\n            if (_inheritLocale) {\n                _fallbackLocale.value = val;\n                _context.fallbackLocale = val;\n                updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n            }\n        });\n    }\n    // define basic composition API!\n    const composer = {\n        id: composerID,\n        locale,\n        fallbackLocale,\n        get inheritLocale() {\n            return _inheritLocale;\n        },\n        set inheritLocale(val) {\n            _inheritLocale = val;\n            if (val && __root) {\n                _locale.value = __root.locale.value;\n                _fallbackLocale.value = __root.fallbackLocale.value;\n                updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);\n            }\n        },\n        get availableLocales() {\n            return Object.keys(_messages.value).sort();\n        },\n        messages,\n        get modifiers() {\n            return _modifiers;\n        },\n        get pluralRules() {\n            return _pluralRules || {};\n        },\n        get isGlobal() {\n            return _isGlobal;\n        },\n        get missingWarn() {\n            return _missingWarn;\n        },\n        set missingWarn(val) {\n            _missingWarn = val;\n            _context.missingWarn = _missingWarn;\n        },\n        get fallbackWarn() {\n            return _fallbackWarn;\n        },\n        set fallbackWarn(val) {\n            _fallbackWarn = val;\n            _context.fallbackWarn = _fallbackWarn;\n        },\n        get fallbackRoot() {\n            return _fallbackRoot;\n        },\n        set fallbackRoot(val) {\n            _fallbackRoot = val;\n        },\n        get fallbackFormat() {\n            return _fallbackFormat;\n        },\n        set fallbackFormat(val) {\n            _fallbackFormat = val;\n            _context.fallbackFormat = _fallbackFormat;\n        },\n        get warnHtmlMessage() {\n            return _warnHtmlMessage;\n        },\n        set warnHtmlMessage(val) {\n            _warnHtmlMessage = val;\n            _context.warnHtmlMessage = val;\n        },\n        get escapeParameter() {\n            return _escapeParameter;\n        },\n        set escapeParameter(val) {\n            _escapeParameter = val;\n            _context.escapeParameter = val;\n        },\n        t,\n        getLocaleMessage,\n        setLocaleMessage,\n        mergeLocaleMessage,\n        getPostTranslationHandler,\n        setPostTranslationHandler,\n        getMissingHandler,\n        setMissingHandler,\n        [SetPluralRulesSymbol]: setPluralRules\n    };\n    {\n        composer.datetimeFormats = datetimeFormats;\n        composer.numberFormats = numberFormats;\n        composer.rt = rt;\n        composer.te = te;\n        composer.tm = tm;\n        composer.d = d;\n        composer.n = n;\n        composer.getDateTimeFormat = getDateTimeFormat;\n        composer.setDateTimeFormat = setDateTimeFormat;\n        composer.mergeDateTimeFormat = mergeDateTimeFormat;\n        composer.getNumberFormat = getNumberFormat;\n        composer.setNumberFormat = setNumberFormat;\n        composer.mergeNumberFormat = mergeNumberFormat;\n        composer[InejctWithOptionSymbol] = __injectWithOption;\n        composer[TranslateVNodeSymbol] = translateVNode;\n        composer[DatetimePartsSymbol] = datetimeParts;\n        composer[NumberPartsSymbol] = numberParts;\n    }\n    // for vue-devtools timeline event\n    if ((process.env.NODE_ENV !== 'production')) {\n        composer[EnableEmitter] = (emitter) => {\n            _context.__v_emitter = emitter;\n        };\n        composer[DisableEmitter] = () => {\n            _context.__v_emitter = undefined;\n        };\n    }\n    return composer;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\n * Convert to I18n Composer Options from VueI18n Options\n *\n * @internal\n */\nfunction convertComposerOptions(options) {\n    const locale = isString(options.locale) ? options.locale : DEFAULT_LOCALE;\n    const fallbackLocale = isString(options.fallbackLocale) ||\n        isArray(options.fallbackLocale) ||\n        isPlainObject(options.fallbackLocale) ||\n        options.fallbackLocale === false\n        ? options.fallbackLocale\n        : locale;\n    const missing = isFunction(options.missing) ? options.missing : undefined;\n    const missingWarn = isBoolean(options.silentTranslationWarn) ||\n        isRegExp(options.silentTranslationWarn)\n        ? !options.silentTranslationWarn\n        : true;\n    const fallbackWarn = isBoolean(options.silentFallbackWarn) ||\n        isRegExp(options.silentFallbackWarn)\n        ? !options.silentFallbackWarn\n        : true;\n    const fallbackRoot = isBoolean(options.fallbackRoot)\n        ? options.fallbackRoot\n        : true;\n    const fallbackFormat = !!options.formatFallbackMessages;\n    const modifiers = isPlainObject(options.modifiers) ? options.modifiers : {};\n    const pluralizationRules = options.pluralizationRules;\n    const postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : undefined;\n    const warnHtmlMessage = isString(options.warnHtmlInMessage)\n        ? options.warnHtmlInMessage !== 'off'\n        : true;\n    const escapeParameter = !!options.escapeParameterHtml;\n    const inheritLocale = isBoolean(options.sync) ? options.sync : true;\n    if ((process.env.NODE_ENV !== 'production') && options.formatter) {\n        warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n    }\n    if ((process.env.NODE_ENV !== 'production') && options.preserveDirectiveContent) {\n        warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n    }\n    let messages = options.messages;\n    if (isPlainObject(options.sharedMessages)) {\n        const sharedMessages = options.sharedMessages;\n        const locales = Object.keys(sharedMessages);\n        messages = locales.reduce((messages, locale) => {\n            const message = messages[locale] || (messages[locale] = {});\n            assign(message, sharedMessages[locale]);\n            return messages;\n        }, (messages || {}));\n    }\n    const { __i18n, __root, __injectWithOption } = options;\n    const datetimeFormats = options.datetimeFormats;\n    const numberFormats = options.numberFormats;\n    const flatJson = options.flatJson;\n    const translateExistCompatible = options\n        .translateExistCompatible;\n    return {\n        locale,\n        fallbackLocale,\n        messages,\n        flatJson,\n        datetimeFormats,\n        numberFormats,\n        missing,\n        missingWarn,\n        fallbackWarn,\n        fallbackRoot,\n        fallbackFormat,\n        modifiers,\n        pluralRules: pluralizationRules,\n        postTranslation,\n        warnHtmlMessage,\n        escapeParameter,\n        messageResolver: options.messageResolver,\n        inheritLocale,\n        translateExistCompatible,\n        __i18n,\n        __root,\n        __injectWithOption\n    };\n}\n/**\n * create VueI18n interface factory\n *\n * @internal\n */\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction createVueI18n(options = {}, VueI18nLegacy) {\n    {\n        const composer = createComposer(convertComposerOptions(options));\n        const { __extender } = options;\n        // defines VueI18n\n        const vueI18n = {\n            // id\n            id: composer.id,\n            // locale\n            get locale() {\n                return composer.locale.value;\n            },\n            set locale(val) {\n                composer.locale.value = val;\n            },\n            // fallbackLocale\n            get fallbackLocale() {\n                return composer.fallbackLocale.value;\n            },\n            set fallbackLocale(val) {\n                composer.fallbackLocale.value = val;\n            },\n            // messages\n            get messages() {\n                return composer.messages.value;\n            },\n            // datetimeFormats\n            get datetimeFormats() {\n                return composer.datetimeFormats.value;\n            },\n            // numberFormats\n            get numberFormats() {\n                return composer.numberFormats.value;\n            },\n            // availableLocales\n            get availableLocales() {\n                return composer.availableLocales;\n            },\n            // formatter\n            get formatter() {\n                (process.env.NODE_ENV !== 'production') && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n                // dummy\n                return {\n                    interpolate() {\n                        return [];\n                    }\n                };\n            },\n            set formatter(val) {\n                (process.env.NODE_ENV !== 'production') && warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));\n            },\n            // missing\n            get missing() {\n                return composer.getMissingHandler();\n            },\n            set missing(handler) {\n                composer.setMissingHandler(handler);\n            },\n            // silentTranslationWarn\n            get silentTranslationWarn() {\n                return isBoolean(composer.missingWarn)\n                    ? !composer.missingWarn\n                    : composer.missingWarn;\n            },\n            set silentTranslationWarn(val) {\n                composer.missingWarn = isBoolean(val) ? !val : val;\n            },\n            // silentFallbackWarn\n            get silentFallbackWarn() {\n                return isBoolean(composer.fallbackWarn)\n                    ? !composer.fallbackWarn\n                    : composer.fallbackWarn;\n            },\n            set silentFallbackWarn(val) {\n                composer.fallbackWarn = isBoolean(val) ? !val : val;\n            },\n            // modifiers\n            get modifiers() {\n                return composer.modifiers;\n            },\n            // formatFallbackMessages\n            get formatFallbackMessages() {\n                return composer.fallbackFormat;\n            },\n            set formatFallbackMessages(val) {\n                composer.fallbackFormat = val;\n            },\n            // postTranslation\n            get postTranslation() {\n                return composer.getPostTranslationHandler();\n            },\n            set postTranslation(handler) {\n                composer.setPostTranslationHandler(handler);\n            },\n            // sync\n            get sync() {\n                return composer.inheritLocale;\n            },\n            set sync(val) {\n                composer.inheritLocale = val;\n            },\n            // warnInHtmlMessage\n            get warnHtmlInMessage() {\n                return composer.warnHtmlMessage ? 'warn' : 'off';\n            },\n            set warnHtmlInMessage(val) {\n                composer.warnHtmlMessage = val !== 'off';\n            },\n            // escapeParameterHtml\n            get escapeParameterHtml() {\n                return composer.escapeParameter;\n            },\n            set escapeParameterHtml(val) {\n                composer.escapeParameter = val;\n            },\n            // preserveDirectiveContent\n            get preserveDirectiveContent() {\n                (process.env.NODE_ENV !== 'production') &&\n                    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n                return true;\n            },\n            set preserveDirectiveContent(val) {\n                (process.env.NODE_ENV !== 'production') &&\n                    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));\n            },\n            // pluralizationRules\n            get pluralizationRules() {\n                return composer.pluralRules || {};\n            },\n            // for internal\n            __composer: composer,\n            // t\n            t(...args) {\n                const [arg1, arg2, arg3] = args;\n                const options = {};\n                let list = null;\n                let named = null;\n                if (!isString(arg1)) {\n                    throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n                }\n                const key = arg1;\n                if (isString(arg2)) {\n                    options.locale = arg2;\n                }\n                else if (isArray(arg2)) {\n                    list = arg2;\n                }\n                else if (isPlainObject(arg2)) {\n                    named = arg2;\n                }\n                if (isArray(arg3)) {\n                    list = arg3;\n                }\n                else if (isPlainObject(arg3)) {\n                    named = arg3;\n                }\n                // return composer.t(key, (list || named || {}) as any, options)\n                return Reflect.apply(composer.t, composer, [\n                    key,\n                    (list || named || {}),\n                    options\n                ]);\n            },\n            rt(...args) {\n                return Reflect.apply(composer.rt, composer, [...args]);\n            },\n            // tc\n            tc(...args) {\n                const [arg1, arg2, arg3] = args;\n                const options = { plural: 1 };\n                let list = null;\n                let named = null;\n                if (!isString(arg1)) {\n                    throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);\n                }\n                const key = arg1;\n                if (isString(arg2)) {\n                    options.locale = arg2;\n                }\n                else if (isNumber(arg2)) {\n                    options.plural = arg2;\n                }\n                else if (isArray(arg2)) {\n                    list = arg2;\n                }\n                else if (isPlainObject(arg2)) {\n                    named = arg2;\n                }\n                if (isString(arg3)) {\n                    options.locale = arg3;\n                }\n                else if (isArray(arg3)) {\n                    list = arg3;\n                }\n                else if (isPlainObject(arg3)) {\n                    named = arg3;\n                }\n                // return composer.t(key, (list || named || {}) as any, options)\n                return Reflect.apply(composer.t, composer, [\n                    key,\n                    (list || named || {}),\n                    options\n                ]);\n            },\n            // te\n            te(key, locale) {\n                return composer.te(key, locale);\n            },\n            // tm\n            tm(key) {\n                return composer.tm(key);\n            },\n            // getLocaleMessage\n            getLocaleMessage(locale) {\n                return composer.getLocaleMessage(locale);\n            },\n            // setLocaleMessage\n            setLocaleMessage(locale, message) {\n                composer.setLocaleMessage(locale, message);\n            },\n            // mergeLocaleMessage\n            mergeLocaleMessage(locale, message) {\n                composer.mergeLocaleMessage(locale, message);\n            },\n            // d\n            d(...args) {\n                return Reflect.apply(composer.d, composer, [...args]);\n            },\n            // getDateTimeFormat\n            getDateTimeFormat(locale) {\n                return composer.getDateTimeFormat(locale);\n            },\n            // setDateTimeFormat\n            setDateTimeFormat(locale, format) {\n                composer.setDateTimeFormat(locale, format);\n            },\n            // mergeDateTimeFormat\n            mergeDateTimeFormat(locale, format) {\n                composer.mergeDateTimeFormat(locale, format);\n            },\n            // n\n            n(...args) {\n                return Reflect.apply(composer.n, composer, [...args]);\n            },\n            // getNumberFormat\n            getNumberFormat(locale) {\n                return composer.getNumberFormat(locale);\n            },\n            // setNumberFormat\n            setNumberFormat(locale, format) {\n                composer.setNumberFormat(locale, format);\n            },\n            // mergeNumberFormat\n            mergeNumberFormat(locale, format) {\n                composer.mergeNumberFormat(locale, format);\n            },\n            // getChoiceIndex\n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            getChoiceIndex(choice, choicesLength) {\n                (process.env.NODE_ENV !== 'production') &&\n                    warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX));\n                return -1;\n            }\n        };\n        vueI18n.__extender = __extender;\n        // for vue-devtools timeline event\n        if ((process.env.NODE_ENV !== 'production')) {\n            vueI18n.__enableEmitter = (emitter) => {\n                const __composer = composer;\n                __composer[EnableEmitter] && __composer[EnableEmitter](emitter);\n            };\n            vueI18n.__disableEmitter = () => {\n                const __composer = composer;\n                __composer[DisableEmitter] && __composer[DisableEmitter]();\n            };\n        }\n        return vueI18n;\n    }\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\nconst baseFormatProps = {\n    tag: {\n        type: [String, Object]\n    },\n    locale: {\n        type: String\n    },\n    scope: {\n        type: String,\n        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\n        validator: (val /* ComponentI18nScope */) => val === 'parent' || val === 'global',\n        default: 'parent' /* ComponentI18nScope */\n    },\n    i18n: {\n        type: Object\n    }\n};\n\nfunction getInterpolateArg(\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n{ slots }, // SetupContext,\nkeys) {\n    if (keys.length === 1 && keys[0] === 'default') {\n        // default slot with list\n        const ret = slots.default ? slots.default() : [];\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        return ret.reduce((slot, current) => {\n            return [\n                ...slot,\n                // prettier-ignore\n                ...(current.type === Fragment ? current.children : [current]\n                    )\n            ];\n        }, []);\n    }\n    else {\n        // named slots\n        return keys.reduce((arg, key) => {\n            const slot = slots[key];\n            if (slot) {\n                arg[key] = slot();\n            }\n            return arg;\n        }, create());\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getFragmentableTag(tag) {\n    return Fragment ;\n}\n\nconst TranslationImpl = /*#__PURE__*/ defineComponent({\n    /* eslint-disable */\n    name: 'i18n-t',\n    props: assign({\n        keypath: {\n            type: String,\n            required: true\n        },\n        plural: {\n            type: [Number, String],\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            validator: (val) => isNumber(val) || !isNaN(val)\n        }\n    }, baseFormatProps),\n    /* eslint-enable */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setup(props, context) {\n        const { slots, attrs } = context;\n        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050\n        const i18n = props.i18n ||\n            useI18n({\n                useScope: props.scope,\n                __useComponent: true\n            });\n        return () => {\n            const keys = Object.keys(slots).filter(key => key !== '_');\n            const options = create();\n            if (props.locale) {\n                options.locale = props.locale;\n            }\n            if (props.plural !== undefined) {\n                options.plural = isString(props.plural) ? +props.plural : props.plural;\n            }\n            const arg = getInterpolateArg(context, keys);\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const children = i18n[TranslateVNodeSymbol](props.keypath, arg, options);\n            const assignedAttrs = assign(create(), attrs);\n            const tag = isString(props.tag) || isObject(props.tag)\n                ? props.tag\n                : getFragmentableTag();\n            return h(tag, assignedAttrs, children);\n        };\n    }\n});\n/**\n * export the public type for h/tsx inference\n * also to avoid inline import() in generated d.ts files\n */\n/**\n * Translation Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [TranslationProps](component#translationprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Component Interpolation](../guide/advanced/component)\n *\n * @example\n * ```html\n * <div id=\"app\">\n *   <!-- ... -->\n *   <i18n keypath=\"term\" tag=\"label\" for=\"tos\">\n *     <a :href=\"url\" target=\"_blank\">{{ $t('tos') }}</a>\n *   </i18n>\n *   <!-- ... -->\n * </div>\n * ```\n * ```js\n * import { createApp } from 'vue'\n * import { createI18n } from 'vue-i18n'\n *\n * const messages = {\n *   en: {\n *     tos: 'Term of Service',\n *     term: 'I accept xxx {0}.'\n *   },\n *   ja: {\n *     tos: '利用規約',\n *     term: '私は xxx の{0}に同意します。'\n *   }\n * }\n *\n * const i18n = createI18n({\n *   locale: 'en',\n *   messages\n * })\n *\n * const app = createApp({\n *   data: {\n *     url: '/term'\n *   }\n * }).use(i18n).mount('#app')\n * ```\n *\n * @VueI18nComponent\n */\nconst Translation = TranslationImpl;\nconst I18nT = Translation;\n\nfunction isVNode(target) {\n    return isArray(target) && !isString(target[0]);\n}\nfunction renderFormatter(props, context, slotKeys, partFormatter) {\n    const { slots, attrs } = context;\n    return () => {\n        const options = { part: true };\n        let overrides = create();\n        if (props.locale) {\n            options.locale = props.locale;\n        }\n        if (isString(props.format)) {\n            options.key = props.format;\n        }\n        else if (isObject(props.format)) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            if (isString(props.format.key)) {\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                options.key = props.format.key;\n            }\n            // Filter out number format options only\n            overrides = Object.keys(props.format).reduce((options, prop) => {\n                return slotKeys.includes(prop)\n                    ? assign(create(), options, { [prop]: props.format[prop] }) // eslint-disable-line @typescript-eslint/no-explicit-any\n                    : options;\n            }, create());\n        }\n        const parts = partFormatter(...[props.value, options, overrides]);\n        let children = [options.key];\n        if (isArray(parts)) {\n            children = parts.map((part, index) => {\n                const slot = slots[part.type];\n                const node = slot\n                    ? slot({ [part.type]: part.value, index, parts })\n                    : [part.value];\n                if (isVNode(node)) {\n                    node[0].key = `${part.type}-${index}`;\n                }\n                return node;\n            });\n        }\n        else if (isString(parts)) {\n            children = [parts];\n        }\n        const assignedAttrs = assign(create(), attrs);\n        const tag = isString(props.tag) || isObject(props.tag)\n            ? props.tag\n            : getFragmentableTag();\n        return h(tag, assignedAttrs, children);\n    };\n}\n\nconst NumberFormatImpl = /*#__PURE__*/ defineComponent({\n    /* eslint-disable */\n    name: 'i18n-n',\n    props: assign({\n        value: {\n            type: Number,\n            required: true\n        },\n        format: {\n            type: [String, Object]\n        }\n    }, baseFormatProps),\n    /* eslint-enable */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setup(props, context) {\n        const i18n = props.i18n ||\n            useI18n({\n                useScope: props.scope,\n                __useComponent: true\n            });\n        return renderFormatter(props, context, NUMBER_FORMAT_OPTIONS_KEYS, (...args) => \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        i18n[NumberPartsSymbol](...args));\n    }\n});\n/**\n * export the public type for h/tsx inference\n * also to avoid inline import() in generated d.ts files\n */\n/**\n * Number Format Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [FormattableProps](component#formattableprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Custom Formatting](../guide/essentials/number#custom-formatting)\n *\n * @VueI18nDanger\n * Not supported IE, due to no support `Intl.NumberFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat/formatToParts)\n *\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-numberformat)\n *\n * @VueI18nComponent\n */\nconst NumberFormat = NumberFormatImpl;\nconst I18nN = NumberFormat;\n\nconst DatetimeFormatImpl = /* #__PURE__*/ defineComponent({\n    /* eslint-disable */\n    name: 'i18n-d',\n    props: assign({\n        value: {\n            type: [Number, Date],\n            required: true\n        },\n        format: {\n            type: [String, Object]\n        }\n    }, baseFormatProps),\n    /* eslint-enable */\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setup(props, context) {\n        const i18n = props.i18n ||\n            useI18n({\n                useScope: props.scope,\n                __useComponent: true\n            });\n        return renderFormatter(props, context, DATETIME_FORMAT_OPTIONS_KEYS, (...args) => \n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        i18n[DatetimePartsSymbol](...args));\n    }\n});\n/**\n * Datetime Format Component\n *\n * @remarks\n * See the following items for property about details\n *\n * @VueI18nSee [FormattableProps](component#formattableprops)\n * @VueI18nSee [BaseFormatProps](component#baseformatprops)\n * @VueI18nSee [Custom Formatting](../guide/essentials/datetime#custom-formatting)\n *\n * @VueI18nDanger\n * Not supported IE, due to no support `Intl.DateTimeFormat#formatToParts` in [IE](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/formatToParts)\n *\n * If you want to use it, you need to use [polyfill](https://github.com/formatjs/formatjs/tree/main/packages/intl-datetimeformat)\n *\n * @VueI18nComponent\n */\nconst DatetimeFormat = DatetimeFormatImpl;\nconst I18nD = DatetimeFormat;\n\nfunction getComposer$2(i18n, instance) {\n    const i18nInternal = i18n;\n    if (i18n.mode === 'composition') {\n        return (i18nInternal.__getInstance(instance) || i18n.global);\n    }\n    else {\n        const vueI18n = i18nInternal.__getInstance(instance);\n        return vueI18n != null\n            ? vueI18n.__composer\n            : i18n.global.__composer;\n    }\n}\nfunction vTDirective(i18n) {\n    const _process = (binding) => {\n        const { instance, modifiers, value } = binding;\n        /* istanbul ignore if */\n        if (!instance || !instance.$) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        const composer = getComposer$2(i18n, instance.$);\n        if ((process.env.NODE_ENV !== 'production') && modifiers.preserve) {\n            warn(getWarnMessage(I18nWarnCodes.NOT_SUPPORTED_PRESERVE));\n        }\n        const parsedValue = parseValue(value);\n        return [\n            Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]),\n            composer\n        ];\n    };\n    const register = (el, binding) => {\n        const [textContent, composer] = _process(binding);\n        if (inBrowser && i18n.global === composer) {\n            // global scope only\n            el.__i18nWatcher = watch(composer.locale, () => {\n                binding.instance && binding.instance.$forceUpdate();\n            });\n        }\n        el.__composer = composer;\n        el.textContent = textContent;\n    };\n    const unregister = (el) => {\n        if (inBrowser && el.__i18nWatcher) {\n            el.__i18nWatcher();\n            el.__i18nWatcher = undefined;\n            delete el.__i18nWatcher;\n        }\n        if (el.__composer) {\n            el.__composer = undefined;\n            delete el.__composer;\n        }\n    };\n    const update = (el, { value }) => {\n        if (el.__composer) {\n            const composer = el.__composer;\n            const parsedValue = parseValue(value);\n            el.textContent = Reflect.apply(composer.t, composer, [\n                ...makeParams(parsedValue)\n            ]);\n        }\n    };\n    const getSSRProps = (binding) => {\n        const [textContent] = _process(binding);\n        return { textContent };\n    };\n    return {\n        created: register,\n        unmounted: unregister,\n        beforeUpdate: update,\n        getSSRProps\n    };\n}\nfunction parseValue(value) {\n    if (isString(value)) {\n        return { path: value };\n    }\n    else if (isPlainObject(value)) {\n        if (!('path' in value)) {\n            throw createI18nError(I18nErrorCodes.REQUIRED_VALUE, 'path');\n        }\n        return value;\n    }\n    else {\n        throw createI18nError(I18nErrorCodes.INVALID_VALUE);\n    }\n}\nfunction makeParams(value) {\n    const { path, locale, args, choice, plural } = value;\n    const options = {};\n    const named = args || {};\n    if (isString(locale)) {\n        options.locale = locale;\n    }\n    if (isNumber(choice)) {\n        options.plural = choice;\n    }\n    if (isNumber(plural)) {\n        options.plural = plural;\n    }\n    return [path, named, options];\n}\n\nfunction apply(app, i18n, ...options) {\n    const pluginOptions = isPlainObject(options[0])\n        ? options[0]\n        : {};\n    const useI18nComponentName = !!pluginOptions.useI18nComponentName;\n    const globalInstall = isBoolean(pluginOptions.globalInstall)\n        ? pluginOptions.globalInstall\n        : true;\n    if ((process.env.NODE_ENV !== 'production') && globalInstall && useI18nComponentName) {\n        warn(getWarnMessage(I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE, {\n            name: Translation.name\n        }));\n    }\n    if (globalInstall) {\n        [!useI18nComponentName ? Translation.name : 'i18n', 'I18nT'].forEach(name => app.component(name, Translation));\n        [NumberFormat.name, 'I18nN'].forEach(name => app.component(name, NumberFormat));\n        [DatetimeFormat.name, 'I18nD'].forEach(name => app.component(name, DatetimeFormat));\n    }\n    // install directive\n    {\n        app.directive('t', vTDirective(i18n));\n    }\n}\n\nconst VueDevToolsLabels = {\n    [\"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */]: 'Vue I18n devtools',\n    [\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]: 'I18n Resources',\n    [\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]: 'Vue I18n'\n};\nconst VueDevToolsPlaceholders = {\n    [\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]: 'Search for scopes ...'\n};\nconst VueDevToolsTimelineColors = {\n    [\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]: 0xffcd19\n};\n\nconst VUE_I18N_COMPONENT_TYPES = 'vue-i18n: composer properties';\nlet devtoolsApi;\nasync function enableDevTools(app, i18n) {\n    return new Promise((resolve, reject) => {\n        try {\n            setupDevtoolsPlugin({\n                id: \"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */,\n                label: VueDevToolsLabels[\"vue-devtools-plugin-vue-i18n\" /* VueDevToolsIDs.PLUGIN */],\n                packageName: 'vue-i18n',\n                homepage: 'https://vue-i18n.intlify.dev',\n                logo: 'https://vue-i18n.intlify.dev/vue-i18n-devtools-logo.png',\n                componentStateTypes: [VUE_I18N_COMPONENT_TYPES],\n                app: app // eslint-disable-line @typescript-eslint/no-explicit-any\n            }, api => {\n                devtoolsApi = api;\n                api.on.visitComponentTree(({ componentInstance, treeNode }) => {\n                    updateComponentTreeTags(componentInstance, treeNode, i18n);\n                });\n                api.on.inspectComponent(({ componentInstance, instanceData }) => {\n                    if (componentInstance.vnode.el &&\n                        componentInstance.vnode.el.__VUE_I18N__ &&\n                        instanceData) {\n                        if (i18n.mode === 'legacy') {\n                            // ignore global scope on legacy mode\n                            if (componentInstance.vnode.el.__VUE_I18N__ !==\n                                i18n.global.__composer) {\n                                inspectComposer(instanceData, componentInstance.vnode.el.__VUE_I18N__);\n                            }\n                        }\n                        else {\n                            inspectComposer(instanceData, componentInstance.vnode.el.__VUE_I18N__);\n                        }\n                    }\n                });\n                api.addInspector({\n                    id: \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */,\n                    label: VueDevToolsLabels[\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */],\n                    icon: 'language',\n                    treeFilterPlaceholder: VueDevToolsPlaceholders[\"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */]\n                });\n                api.on.getInspectorTree(payload => {\n                    if (payload.app === app &&\n                        payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n                        registerScope(payload, i18n);\n                    }\n                });\n                const roots = new Map();\n                api.on.getInspectorState(async (payload) => {\n                    if (payload.app === app &&\n                        payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n                        api.unhighlightElement();\n                        inspectScope(payload, i18n);\n                        if (payload.nodeId === 'global') {\n                            if (!roots.has(payload.app)) {\n                                const [root] = await api.getComponentInstances(payload.app);\n                                roots.set(payload.app, root);\n                            }\n                            api.highlightElement(roots.get(payload.app));\n                        }\n                        else {\n                            const instance = getComponentInstance(payload.nodeId, i18n);\n                            instance && api.highlightElement(instance);\n                        }\n                    }\n                });\n                api.on.editInspectorState(payload => {\n                    if (payload.app === app &&\n                        payload.inspectorId === \"vue-i18n-resource-inspector\" /* VueDevToolsIDs.CUSTOM_INSPECTOR */) {\n                        editScope(payload, i18n);\n                    }\n                });\n                api.addTimelineLayer({\n                    id: \"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */,\n                    label: VueDevToolsLabels[\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */],\n                    color: VueDevToolsTimelineColors[\"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */]\n                });\n                resolve(true);\n            });\n        }\n        catch (e) {\n            console.error(e);\n            reject(false);\n        }\n    });\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getI18nScopeLable(instance) {\n    return (instance.type.name ||\n        instance.type.displayName ||\n        instance.type.__file ||\n        'Anonymous');\n}\nfunction updateComponentTreeTags(instance, // eslint-disable-line @typescript-eslint/no-explicit-any\ntreeNode, i18n) {\n    // prettier-ignore\n    const global = i18n.mode === 'composition'\n        ? i18n.global\n        : i18n.global.__composer;\n    if (instance && instance.vnode.el && instance.vnode.el.__VUE_I18N__) {\n        // add custom tags local scope only\n        if (instance.vnode.el.__VUE_I18N__ !== global) {\n            const tag = {\n                label: `i18n (${getI18nScopeLable(instance)} Scope)`,\n                textColor: 0x000000,\n                backgroundColor: 0xffcd19\n            };\n            treeNode.tags.push(tag);\n        }\n    }\n}\nfunction inspectComposer(instanceData, composer) {\n    const type = VUE_I18N_COMPONENT_TYPES;\n    instanceData.state.push({\n        type,\n        key: 'locale',\n        editable: true,\n        value: composer.locale.value\n    });\n    instanceData.state.push({\n        type,\n        key: 'availableLocales',\n        editable: false,\n        value: composer.availableLocales\n    });\n    instanceData.state.push({\n        type,\n        key: 'fallbackLocale',\n        editable: true,\n        value: composer.fallbackLocale.value\n    });\n    instanceData.state.push({\n        type,\n        key: 'inheritLocale',\n        editable: true,\n        value: composer.inheritLocale\n    });\n    instanceData.state.push({\n        type,\n        key: 'messages',\n        editable: false,\n        value: getLocaleMessageValue(composer.messages.value)\n    });\n    {\n        instanceData.state.push({\n            type,\n            key: 'datetimeFormats',\n            editable: false,\n            value: composer.datetimeFormats.value\n        });\n        instanceData.state.push({\n            type,\n            key: 'numberFormats',\n            editable: false,\n            value: composer.numberFormats.value\n        });\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getLocaleMessageValue(messages) {\n    const value = {};\n    Object.keys(messages).forEach((key) => {\n        const v = messages[key];\n        if (isFunction(v) && 'source' in v) {\n            value[key] = getMessageFunctionDetails(v);\n        }\n        else if (isMessageAST(v) && v.loc && v.loc.source) {\n            value[key] = v.loc.source;\n        }\n        else if (isObject(v)) {\n            value[key] = getLocaleMessageValue(v);\n        }\n        else {\n            value[key] = v;\n        }\n    });\n    return value;\n}\nconst ESC = {\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '&': '&amp;'\n};\nfunction escape(s) {\n    return s.replace(/[<>\"&]/g, escapeChar);\n}\nfunction escapeChar(a) {\n    return ESC[a] || a;\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getMessageFunctionDetails(func) {\n    const argString = func.source ? `(\"${escape(func.source)}\")` : `(?)`;\n    return {\n        _custom: {\n            type: 'function',\n            display: `<span>ƒ</span> ${argString}`\n        }\n    };\n}\nfunction registerScope(payload, i18n) {\n    payload.rootNodes.push({\n        id: 'global',\n        label: 'Global Scope'\n    });\n    // prettier-ignore\n    const global = i18n.mode === 'composition'\n        ? i18n.global\n        : i18n.global.__composer;\n    for (const [keyInstance, instance] of i18n.__instances) {\n        // prettier-ignore\n        const composer = i18n.mode === 'composition'\n            ? instance\n            : instance.__composer;\n        if (global === composer) {\n            continue;\n        }\n        payload.rootNodes.push({\n            id: composer.id.toString(),\n            label: `${getI18nScopeLable(keyInstance)} Scope`\n        });\n    }\n}\nfunction getComponentInstance(nodeId, i18n) {\n    let instance = null;\n    if (nodeId !== 'global') {\n        for (const [component, composer] of i18n.__instances.entries()) {\n            if (composer.id.toString() === nodeId) {\n                instance = component;\n                break;\n            }\n        }\n    }\n    return instance;\n}\nfunction getComposer$1(nodeId, i18n) {\n    if (nodeId === 'global') {\n        return i18n.mode === 'composition'\n            ? i18n.global\n            : i18n.global.__composer;\n    }\n    else {\n        const instance = Array.from(i18n.__instances.values()).find(item => item.id.toString() === nodeId);\n        if (instance) {\n            return i18n.mode === 'composition'\n                ? instance\n                : instance.__composer;\n        }\n        else {\n            return null;\n        }\n    }\n}\nfunction inspectScope(payload, i18n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) {\n    const composer = getComposer$1(payload.nodeId, i18n);\n    if (composer) {\n        // TODO:\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        payload.state = makeScopeInspectState(composer);\n    }\n    return null;\n}\nfunction makeScopeInspectState(composer) {\n    const state = {};\n    const localeType = 'Locale related info';\n    const localeStates = [\n        {\n            type: localeType,\n            key: 'locale',\n            editable: true,\n            value: composer.locale.value\n        },\n        {\n            type: localeType,\n            key: 'fallbackLocale',\n            editable: true,\n            value: composer.fallbackLocale.value\n        },\n        {\n            type: localeType,\n            key: 'availableLocales',\n            editable: false,\n            value: composer.availableLocales\n        },\n        {\n            type: localeType,\n            key: 'inheritLocale',\n            editable: true,\n            value: composer.inheritLocale\n        }\n    ];\n    state[localeType] = localeStates;\n    const localeMessagesType = 'Locale messages info';\n    const localeMessagesStates = [\n        {\n            type: localeMessagesType,\n            key: 'messages',\n            editable: false,\n            value: getLocaleMessageValue(composer.messages.value)\n        }\n    ];\n    state[localeMessagesType] = localeMessagesStates;\n    {\n        const datetimeFormatsType = 'Datetime formats info';\n        const datetimeFormatsStates = [\n            {\n                type: datetimeFormatsType,\n                key: 'datetimeFormats',\n                editable: false,\n                value: composer.datetimeFormats.value\n            }\n        ];\n        state[datetimeFormatsType] = datetimeFormatsStates;\n        const numberFormatsType = 'Datetime formats info';\n        const numberFormatsStates = [\n            {\n                type: numberFormatsType,\n                key: 'numberFormats',\n                editable: false,\n                value: composer.numberFormats.value\n            }\n        ];\n        state[numberFormatsType] = numberFormatsStates;\n    }\n    return state;\n}\nfunction addTimelineEvent(event, payload) {\n    if (devtoolsApi) {\n        let groupId;\n        if (payload && 'groupId' in payload) {\n            groupId = payload.groupId;\n            delete payload.groupId;\n        }\n        devtoolsApi.addTimelineEvent({\n            layerId: \"vue-i18n-timeline\" /* VueDevToolsIDs.TIMELINE */,\n            event: {\n                title: event,\n                groupId,\n                time: Date.now(),\n                meta: {},\n                data: payload || {},\n                logType: event === \"compile-error\" /* VueDevToolsTimelineEvents.COMPILE_ERROR */\n                    ? 'error'\n                    : event === \"fallback\" /* VueDevToolsTimelineEvents.FALBACK */ ||\n                        event === \"missing\" /* VueDevToolsTimelineEvents.MISSING */\n                        ? 'warning'\n                        : 'default'\n            }\n        });\n    }\n}\nfunction editScope(payload, i18n) {\n    const composer = getComposer$1(payload.nodeId, i18n);\n    if (composer) {\n        const [field] = payload.path;\n        if (field === 'locale' && isString(payload.state.value)) {\n            composer.locale.value = payload.state.value;\n        }\n        else if (field === 'fallbackLocale' &&\n            (isString(payload.state.value) ||\n                isArray(payload.state.value) ||\n                isObject(payload.state.value))) {\n            composer.fallbackLocale.value = payload.state.value;\n        }\n        else if (field === 'inheritLocale' && isBoolean(payload.state.value)) {\n            composer.inheritLocale = payload.state.value;\n        }\n    }\n}\n\n/**\n * Supports compatibility for legacy vue-i18n APIs\n * This mixin is used when we use vue-i18n@v9.x or later\n */\nfunction defineMixin(vuei18n, composer, i18n) {\n    return {\n        beforeCreate() {\n            const instance = getCurrentInstance();\n            /* istanbul ignore if */\n            if (!instance) {\n                throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n            }\n            const options = this.$options;\n            if (options.i18n) {\n                const optionsI18n = options.i18n;\n                if (options.__i18n) {\n                    optionsI18n.__i18n = options.__i18n;\n                }\n                optionsI18n.__root = composer;\n                if (this === this.$root) {\n                    // merge option and gttach global\n                    this.$i18n = mergeToGlobal(vuei18n, optionsI18n);\n                }\n                else {\n                    optionsI18n.__injectWithOption = true;\n                    optionsI18n.__extender = i18n.__vueI18nExtend;\n                    // atttach local VueI18n instance\n                    this.$i18n = createVueI18n(optionsI18n);\n                    // extend VueI18n instance\n                    const _vueI18n = this.$i18n;\n                    if (_vueI18n.__extender) {\n                        _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);\n                    }\n                }\n            }\n            else if (options.__i18n) {\n                if (this === this.$root) {\n                    // merge option and gttach global\n                    this.$i18n = mergeToGlobal(vuei18n, options);\n                }\n                else {\n                    // atttach local VueI18n instance\n                    this.$i18n = createVueI18n({\n                        __i18n: options.__i18n,\n                        __injectWithOption: true,\n                        __extender: i18n.__vueI18nExtend,\n                        __root: composer\n                    });\n                    // extend VueI18n instance\n                    const _vueI18n = this.$i18n;\n                    if (_vueI18n.__extender) {\n                        _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);\n                    }\n                }\n            }\n            else {\n                // attach global VueI18n instance\n                this.$i18n = vuei18n;\n            }\n            if (options.__i18nGlobal) {\n                adjustI18nResources(composer, options, options);\n            }\n            // defines vue-i18n legacy APIs\n            this.$t = (...args) => this.$i18n.t(...args);\n            this.$rt = (...args) => this.$i18n.rt(...args);\n            this.$tc = (...args) => this.$i18n.tc(...args);\n            this.$te = (key, locale) => this.$i18n.te(key, locale);\n            this.$d = (...args) => this.$i18n.d(...args);\n            this.$n = (...args) => this.$i18n.n(...args);\n            this.$tm = (key) => this.$i18n.tm(key);\n            i18n.__setInstance(instance, this.$i18n);\n        },\n        mounted() {\n            /* istanbul ignore if */\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                this.$el &&\n                this.$i18n) {\n                const _vueI18n = this.$i18n;\n                this.$el.__VUE_I18N__ = _vueI18n.__composer;\n                const emitter = (this.__v_emitter =\n                    createEmitter());\n                _vueI18n.__enableEmitter && _vueI18n.__enableEmitter(emitter);\n                emitter.on('*', addTimelineEvent);\n            }\n        },\n        unmounted() {\n            const instance = getCurrentInstance();\n            /* istanbul ignore if */\n            if (!instance) {\n                throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n            }\n            const _vueI18n = this.$i18n;\n            /* istanbul ignore if */\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                this.$el &&\n                this.$el.__VUE_I18N__) {\n                if (this.__v_emitter) {\n                    this.__v_emitter.off('*', addTimelineEvent);\n                    delete this.__v_emitter;\n                }\n                if (this.$i18n) {\n                    _vueI18n.__disableEmitter && _vueI18n.__disableEmitter();\n                    delete this.$el.__VUE_I18N__;\n                }\n            }\n            delete this.$t;\n            delete this.$rt;\n            delete this.$tc;\n            delete this.$te;\n            delete this.$d;\n            delete this.$n;\n            delete this.$tm;\n            if (_vueI18n.__disposer) {\n                _vueI18n.__disposer();\n                delete _vueI18n.__disposer;\n                delete _vueI18n.__extender;\n            }\n            i18n.__deleteInstance(instance);\n            delete this.$i18n;\n        }\n    };\n}\nfunction mergeToGlobal(g, options) {\n    g.locale = options.locale || g.locale;\n    g.fallbackLocale = options.fallbackLocale || g.fallbackLocale;\n    g.missing = options.missing || g.missing;\n    g.silentTranslationWarn =\n        options.silentTranslationWarn || g.silentFallbackWarn;\n    g.silentFallbackWarn = options.silentFallbackWarn || g.silentFallbackWarn;\n    g.formatFallbackMessages =\n        options.formatFallbackMessages || g.formatFallbackMessages;\n    g.postTranslation = options.postTranslation || g.postTranslation;\n    g.warnHtmlInMessage = options.warnHtmlInMessage || g.warnHtmlInMessage;\n    g.escapeParameterHtml = options.escapeParameterHtml || g.escapeParameterHtml;\n    g.sync = options.sync || g.sync;\n    g.__composer[SetPluralRulesSymbol](options.pluralizationRules || g.pluralizationRules);\n    const messages = getLocaleMessages(g.locale, {\n        messages: options.messages,\n        __i18n: options.__i18n\n    });\n    Object.keys(messages).forEach(locale => g.mergeLocaleMessage(locale, messages[locale]));\n    if (options.datetimeFormats) {\n        Object.keys(options.datetimeFormats).forEach(locale => g.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));\n    }\n    if (options.numberFormats) {\n        Object.keys(options.numberFormats).forEach(locale => g.mergeNumberFormat(locale, options.numberFormats[locale]));\n    }\n    return g;\n}\n\n/**\n * Injection key for {@link useI18n}\n *\n * @remarks\n * The global injection key for I18n instances with `useI18n`. this injection key is used in Web Components.\n * Specify the i18n instance created by {@link createI18n} together with `provide` function.\n *\n * @VueI18nGeneral\n */\nconst I18nInjectionKey = \n/* #__PURE__*/ makeSymbol('global-vue-i18n');\n// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/explicit-module-boundary-types\nfunction createI18n(options = {}, VueI18nLegacy) {\n    // prettier-ignore\n    const __legacyMode = __VUE_I18N_LEGACY_API__ && isBoolean(options.legacy)\n            ? options.legacy\n            : __VUE_I18N_LEGACY_API__;\n    // prettier-ignore\n    const __globalInjection = isBoolean(options.globalInjection)\n        ? options.globalInjection\n        : true;\n    // prettier-ignore\n    const __allowComposition = __VUE_I18N_LEGACY_API__ && __legacyMode\n            ? !!options.allowComposition\n            : true;\n    const __instances = new Map();\n    const [globalScope, __global] = createGlobal(options, __legacyMode);\n    const symbol = /* #__PURE__*/ makeSymbol((process.env.NODE_ENV !== 'production') ? 'vue-i18n' : '');\n    if ((process.env.NODE_ENV !== 'production')) {\n        if (__legacyMode && __allowComposition && !false) {\n            warn(getWarnMessage(I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION));\n        }\n    }\n    function __getInstance(component) {\n        return __instances.get(component) || null;\n    }\n    function __setInstance(component, instance) {\n        __instances.set(component, instance);\n    }\n    function __deleteInstance(component) {\n        __instances.delete(component);\n    }\n    {\n        const i18n = {\n            // mode\n            get mode() {\n                return __VUE_I18N_LEGACY_API__ && __legacyMode\n                    ? 'legacy'\n                    : 'composition';\n            },\n            // allowComposition\n            get allowComposition() {\n                return __allowComposition;\n            },\n            // install plugin\n            async install(app, ...options) {\n                if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                    !false) {\n                    app.__VUE_I18N__ = i18n;\n                }\n                // setup global provider\n                app.__VUE_I18N_SYMBOL__ = symbol;\n                app.provide(app.__VUE_I18N_SYMBOL__, i18n);\n                // set composer & vuei18n extend hook options from plugin options\n                if (isPlainObject(options[0])) {\n                    const opts = options[0];\n                    i18n.__composerExtend =\n                        opts.__composerExtend;\n                    i18n.__vueI18nExtend =\n                        opts.__vueI18nExtend;\n                }\n                // global method and properties injection for Composition API\n                let globalReleaseHandler = null;\n                if (!__legacyMode && __globalInjection) {\n                    globalReleaseHandler = injectGlobalFields(app, i18n.global);\n                }\n                // install built-in components and directive\n                if (__VUE_I18N_FULL_INSTALL__) {\n                    apply(app, i18n, ...options);\n                }\n                // setup mixin for Legacy API\n                if (__VUE_I18N_LEGACY_API__ && __legacyMode) {\n                    app.mixin(defineMixin(__global, __global.__composer, i18n));\n                }\n                // release global scope\n                const unmountApp = app.unmount;\n                app.unmount = () => {\n                    globalReleaseHandler && globalReleaseHandler();\n                    i18n.dispose();\n                    unmountApp();\n                };\n                // setup vue-devtools plugin\n                if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && !false) {\n                    const ret = await enableDevTools(app, i18n);\n                    if (!ret) {\n                        throw createI18nError(I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN);\n                    }\n                    const emitter = createEmitter();\n                    if (__legacyMode) {\n                        const _vueI18n = __global;\n                        _vueI18n.__enableEmitter && _vueI18n.__enableEmitter(emitter);\n                    }\n                    else {\n                        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                        const _composer = __global;\n                        _composer[EnableEmitter] && _composer[EnableEmitter](emitter);\n                    }\n                    emitter.on('*', addTimelineEvent);\n                }\n            },\n            // global accessor\n            get global() {\n                return __global;\n            },\n            dispose() {\n                globalScope.stop();\n            },\n            // @internal\n            __instances,\n            // @internal\n            __getInstance,\n            // @internal\n            __setInstance,\n            // @internal\n            __deleteInstance\n        };\n        return i18n;\n    }\n}\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction useI18n(options = {}) {\n    const instance = getCurrentInstance();\n    if (instance == null) {\n        throw createI18nError(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);\n    }\n    if (!instance.isCE &&\n        instance.appContext.app != null &&\n        !instance.appContext.app.__VUE_I18N_SYMBOL__) {\n        throw createI18nError(I18nErrorCodes.NOT_INSTALLED);\n    }\n    const i18n = getI18nInstance(instance);\n    const gl = getGlobalComposer(i18n);\n    const componentOptions = getComponentOptions(instance);\n    const scope = getScope(options, componentOptions);\n    if (__VUE_I18N_LEGACY_API__) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        if (i18n.mode === 'legacy' && !options.__useComponent) {\n            if (!i18n.allowComposition) {\n                throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);\n            }\n            return useI18nForLegacy(instance, scope, gl, options);\n        }\n    }\n    if (scope === 'global') {\n        adjustI18nResources(gl, options, componentOptions);\n        return gl;\n    }\n    if (scope === 'parent') {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        let composer = getComposer(i18n, instance, options.__useComponent);\n        if (composer == null) {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn(getWarnMessage(I18nWarnCodes.NOT_FOUND_PARENT_SCOPE));\n            }\n            composer = gl;\n        }\n        return composer;\n    }\n    const i18nInternal = i18n;\n    let composer = i18nInternal.__getInstance(instance);\n    if (composer == null) {\n        const composerOptions = assign({}, options);\n        if ('__i18n' in componentOptions) {\n            composerOptions.__i18n = componentOptions.__i18n;\n        }\n        if (gl) {\n            composerOptions.__root = gl;\n        }\n        composer = createComposer(composerOptions);\n        if (i18nInternal.__composerExtend) {\n            composer[DisposeSymbol] =\n                i18nInternal.__composerExtend(composer);\n        }\n        setupLifeCycle(i18nInternal, instance, composer);\n        i18nInternal.__setInstance(instance, composer);\n    }\n    return composer;\n}\n/**\n * Cast to VueI18n legacy compatible type\n *\n * @remarks\n * This API is provided only with [vue-i18n-bridge](https://vue-i18n.intlify.dev/guide/migration/ways.html#what-is-vue-i18n-bridge).\n *\n * The purpose of this function is to convert an {@link I18n} instance created with {@link createI18n | createI18n(legacy: true)} into a `vue-i18n@v8.x` compatible instance of `new VueI18n` in a TypeScript environment.\n *\n * @param i18n - An instance of {@link I18n}\n * @returns A i18n instance which is casted to {@link VueI18n} type\n *\n * @VueI18nTip\n * :new: provided by **vue-i18n-bridge only**\n *\n * @VueI18nGeneral\n */\n/* #__NO_SIDE_EFFECTS__ */\nconst castToVueI18n = (i18n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n) => {\n    if (!(__VUE_I18N_BRIDGE__ in i18n)) {\n        throw createI18nError(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);\n    }\n    return i18n;\n};\nfunction createGlobal(options, legacyMode, VueI18nLegacy // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n    const scope = effectScope();\n    {\n        const obj = __VUE_I18N_LEGACY_API__ && legacyMode\n            ? scope.run(() => createVueI18n(options))\n            : scope.run(() => createComposer(options));\n        if (obj == null) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        return [scope, obj];\n    }\n}\nfunction getI18nInstance(instance) {\n    {\n        const i18n = inject(!instance.isCE\n            ? instance.appContext.app.__VUE_I18N_SYMBOL__\n            : I18nInjectionKey);\n        /* istanbul ignore if */\n        if (!i18n) {\n            throw createI18nError(!instance.isCE\n                ? I18nErrorCodes.UNEXPECTED_ERROR\n                : I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE);\n        }\n        return i18n;\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction getScope(options, componentOptions) {\n    // prettier-ignore\n    return isEmptyObject(options)\n        ? ('__i18n' in componentOptions)\n            ? 'local'\n            : 'global'\n        : !options.useScope\n            ? 'local'\n            : options.useScope;\n}\nfunction getGlobalComposer(i18n) {\n    // prettier-ignore\n    return i18n.mode === 'composition'\n            ? i18n.global\n            : i18n.global.__composer\n        ;\n}\nfunction getComposer(i18n, target, useComponent = false) {\n    let composer = null;\n    const root = target.root;\n    let current = getParentComponentInstance(target, useComponent);\n    while (current != null) {\n        const i18nInternal = i18n;\n        if (i18n.mode === 'composition') {\n            composer = i18nInternal.__getInstance(current);\n        }\n        else {\n            if (__VUE_I18N_LEGACY_API__) {\n                const vueI18n = i18nInternal.__getInstance(current);\n                if (vueI18n != null) {\n                    composer = vueI18n\n                        .__composer;\n                    if (useComponent &&\n                        composer &&\n                        !composer[InejctWithOptionSymbol] // eslint-disable-line @typescript-eslint/no-explicit-any\n                    ) {\n                        composer = null;\n                    }\n                }\n            }\n        }\n        if (composer != null) {\n            break;\n        }\n        if (root === current) {\n            break;\n        }\n        current = current.parent;\n    }\n    return composer;\n}\nfunction getParentComponentInstance(target, useComponent = false) {\n    if (target == null) {\n        return null;\n    }\n    {\n        // if `useComponent: true` will be specified, we get lexical scope owner instance for use-case slots\n        return !useComponent\n            ? target.parent\n            : target.vnode.ctx || target.parent; // eslint-disable-line @typescript-eslint/no-explicit-any\n    }\n}\nfunction setupLifeCycle(i18n, target, composer) {\n    let emitter = null;\n    {\n        onMounted(() => {\n            // inject composer instance to DOM for intlify-devtools\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                target.vnode.el) {\n                target.vnode.el.__VUE_I18N__ = composer;\n                emitter = createEmitter();\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                const _composer = composer;\n                _composer[EnableEmitter] && _composer[EnableEmitter](emitter);\n                emitter.on('*', addTimelineEvent);\n            }\n        }, target);\n        onUnmounted(() => {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const _composer = composer;\n            // remove composer instance from DOM for intlify-devtools\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                !false &&\n                target.vnode.el &&\n                target.vnode.el.__VUE_I18N__) {\n                emitter && emitter.off('*', addTimelineEvent);\n                _composer[DisableEmitter] && _composer[DisableEmitter]();\n                delete target.vnode.el.__VUE_I18N__;\n            }\n            i18n.__deleteInstance(target);\n            // dispose extended resources\n            const dispose = _composer[DisposeSymbol];\n            if (dispose) {\n                dispose();\n                delete _composer[DisposeSymbol];\n            }\n        }, target);\n    }\n}\nfunction useI18nForLegacy(instance, scope, root, options = {} // eslint-disable-line @typescript-eslint/no-explicit-any\n) {\n    const isLocalScope = scope === 'local';\n    const _composer = shallowRef(null);\n    if (isLocalScope &&\n        instance.proxy &&\n        !(instance.proxy.$options.i18n || instance.proxy.$options.__i18n)) {\n        throw createI18nError(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);\n    }\n    const _inheritLocale = isBoolean(options.inheritLocale)\n        ? options.inheritLocale\n        : !isString(options.locale);\n    const _locale = ref(\n    // prettier-ignore\n    !isLocalScope || _inheritLocale\n        ? root.locale.value\n        : isString(options.locale)\n            ? options.locale\n            : DEFAULT_LOCALE);\n    const _fallbackLocale = ref(\n    // prettier-ignore\n    !isLocalScope || _inheritLocale\n        ? root.fallbackLocale.value\n        : isString(options.fallbackLocale) ||\n            isArray(options.fallbackLocale) ||\n            isPlainObject(options.fallbackLocale) ||\n            options.fallbackLocale === false\n            ? options.fallbackLocale\n            : _locale.value);\n    const _messages = ref(getLocaleMessages(_locale.value, options));\n    // prettier-ignore\n    const _datetimeFormats = ref(isPlainObject(options.datetimeFormats)\n        ? options.datetimeFormats\n        : { [_locale.value]: {} });\n    // prettier-ignore\n    const _numberFormats = ref(isPlainObject(options.numberFormats)\n        ? options.numberFormats\n        : { [_locale.value]: {} });\n    // prettier-ignore\n    const _missingWarn = isLocalScope\n        ? root.missingWarn\n        : isBoolean(options.missingWarn) || isRegExp(options.missingWarn)\n            ? options.missingWarn\n            : true;\n    // prettier-ignore\n    const _fallbackWarn = isLocalScope\n        ? root.fallbackWarn\n        : isBoolean(options.fallbackWarn) || isRegExp(options.fallbackWarn)\n            ? options.fallbackWarn\n            : true;\n    // prettier-ignore\n    const _fallbackRoot = isLocalScope\n        ? root.fallbackRoot\n        : isBoolean(options.fallbackRoot)\n            ? options.fallbackRoot\n            : true;\n    // configure fall back to root\n    const _fallbackFormat = !!options.fallbackFormat;\n    // runtime missing\n    const _missing = isFunction(options.missing) ? options.missing : null;\n    // postTranslation handler\n    const _postTranslation = isFunction(options.postTranslation)\n        ? options.postTranslation\n        : null;\n    // prettier-ignore\n    const _warnHtmlMessage = isLocalScope\n        ? root.warnHtmlMessage\n        : isBoolean(options.warnHtmlMessage)\n            ? options.warnHtmlMessage\n            : true;\n    const _escapeParameter = !!options.escapeParameter;\n    // prettier-ignore\n    const _modifiers = isLocalScope\n        ? root.modifiers\n        : isPlainObject(options.modifiers)\n            ? options.modifiers\n            : {};\n    // pluralRules\n    const _pluralRules = options.pluralRules || (isLocalScope && root.pluralRules);\n    // track reactivity\n    function trackReactivityValues() {\n        return [\n            _locale.value,\n            _fallbackLocale.value,\n            _messages.value,\n            _datetimeFormats.value,\n            _numberFormats.value\n        ];\n    }\n    // locale\n    const locale = computed({\n        get: () => {\n            return _composer.value ? _composer.value.locale.value : _locale.value;\n        },\n        set: val => {\n            if (_composer.value) {\n                _composer.value.locale.value = val;\n            }\n            _locale.value = val;\n        }\n    });\n    // fallbackLocale\n    const fallbackLocale = computed({\n        get: () => {\n            return _composer.value\n                ? _composer.value.fallbackLocale.value\n                : _fallbackLocale.value;\n        },\n        set: val => {\n            if (_composer.value) {\n                _composer.value.fallbackLocale.value = val;\n            }\n            _fallbackLocale.value = val;\n        }\n    });\n    // messages\n    const messages = computed(() => {\n        if (_composer.value) {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return _composer.value.messages.value;\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            return _messages.value;\n        }\n    });\n    const datetimeFormats = computed(() => _datetimeFormats.value);\n    const numberFormats = computed(() => _numberFormats.value);\n    function getPostTranslationHandler() {\n        return _composer.value\n            ? _composer.value.getPostTranslationHandler()\n            : _postTranslation;\n    }\n    function setPostTranslationHandler(handler) {\n        if (_composer.value) {\n            _composer.value.setPostTranslationHandler(handler);\n        }\n    }\n    function getMissingHandler() {\n        return _composer.value ? _composer.value.getMissingHandler() : _missing;\n    }\n    function setMissingHandler(handler) {\n        if (_composer.value) {\n            _composer.value.setMissingHandler(handler);\n        }\n    }\n    function warpWithDeps(fn) {\n        trackReactivityValues();\n        return fn();\n    }\n    function t(...args) {\n        return _composer.value\n            ? warpWithDeps(() => Reflect.apply(_composer.value.t, null, [...args]))\n            : warpWithDeps(() => '');\n    }\n    function rt(...args) {\n        return _composer.value\n            ? Reflect.apply(_composer.value.rt, null, [...args])\n            : '';\n    }\n    function d(...args) {\n        return _composer.value\n            ? warpWithDeps(() => Reflect.apply(_composer.value.d, null, [...args]))\n            : warpWithDeps(() => '');\n    }\n    function n(...args) {\n        return _composer.value\n            ? warpWithDeps(() => Reflect.apply(_composer.value.n, null, [...args]))\n            : warpWithDeps(() => '');\n    }\n    function tm(key) {\n        return _composer.value ? _composer.value.tm(key) : {};\n    }\n    function te(key, locale) {\n        return _composer.value ? _composer.value.te(key, locale) : false;\n    }\n    function getLocaleMessage(locale) {\n        return _composer.value ? _composer.value.getLocaleMessage(locale) : {};\n    }\n    function setLocaleMessage(locale, message) {\n        if (_composer.value) {\n            _composer.value.setLocaleMessage(locale, message);\n            _messages.value[locale] = message;\n        }\n    }\n    function mergeLocaleMessage(locale, message) {\n        if (_composer.value) {\n            _composer.value.mergeLocaleMessage(locale, message);\n        }\n    }\n    function getDateTimeFormat(locale) {\n        return _composer.value ? _composer.value.getDateTimeFormat(locale) : {};\n    }\n    function setDateTimeFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.setDateTimeFormat(locale, format);\n            _datetimeFormats.value[locale] = format;\n        }\n    }\n    function mergeDateTimeFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.mergeDateTimeFormat(locale, format);\n        }\n    }\n    function getNumberFormat(locale) {\n        return _composer.value ? _composer.value.getNumberFormat(locale) : {};\n    }\n    function setNumberFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.setNumberFormat(locale, format);\n            _numberFormats.value[locale] = format;\n        }\n    }\n    function mergeNumberFormat(locale, format) {\n        if (_composer.value) {\n            _composer.value.mergeNumberFormat(locale, format);\n        }\n    }\n    const wrapper = {\n        get id() {\n            return _composer.value ? _composer.value.id : -1;\n        },\n        locale,\n        fallbackLocale,\n        messages,\n        datetimeFormats,\n        numberFormats,\n        get inheritLocale() {\n            return _composer.value ? _composer.value.inheritLocale : _inheritLocale;\n        },\n        set inheritLocale(val) {\n            if (_composer.value) {\n                _composer.value.inheritLocale = val;\n            }\n        },\n        get availableLocales() {\n            return _composer.value\n                ? _composer.value.availableLocales\n                : Object.keys(_messages.value);\n        },\n        get modifiers() {\n            return (_composer.value ? _composer.value.modifiers : _modifiers);\n        },\n        get pluralRules() {\n            return (_composer.value ? _composer.value.pluralRules : _pluralRules);\n        },\n        get isGlobal() {\n            return _composer.value ? _composer.value.isGlobal : false;\n        },\n        get missingWarn() {\n            return _composer.value ? _composer.value.missingWarn : _missingWarn;\n        },\n        set missingWarn(val) {\n            if (_composer.value) {\n                _composer.value.missingWarn = val;\n            }\n        },\n        get fallbackWarn() {\n            return _composer.value ? _composer.value.fallbackWarn : _fallbackWarn;\n        },\n        set fallbackWarn(val) {\n            if (_composer.value) {\n                _composer.value.missingWarn = val;\n            }\n        },\n        get fallbackRoot() {\n            return _composer.value ? _composer.value.fallbackRoot : _fallbackRoot;\n        },\n        set fallbackRoot(val) {\n            if (_composer.value) {\n                _composer.value.fallbackRoot = val;\n            }\n        },\n        get fallbackFormat() {\n            return _composer.value ? _composer.value.fallbackFormat : _fallbackFormat;\n        },\n        set fallbackFormat(val) {\n            if (_composer.value) {\n                _composer.value.fallbackFormat = val;\n            }\n        },\n        get warnHtmlMessage() {\n            return _composer.value\n                ? _composer.value.warnHtmlMessage\n                : _warnHtmlMessage;\n        },\n        set warnHtmlMessage(val) {\n            if (_composer.value) {\n                _composer.value.warnHtmlMessage = val;\n            }\n        },\n        get escapeParameter() {\n            return _composer.value\n                ? _composer.value.escapeParameter\n                : _escapeParameter;\n        },\n        set escapeParameter(val) {\n            if (_composer.value) {\n                _composer.value.escapeParameter = val;\n            }\n        },\n        t,\n        getPostTranslationHandler,\n        setPostTranslationHandler,\n        getMissingHandler,\n        setMissingHandler,\n        rt,\n        d,\n        n,\n        tm,\n        te,\n        getLocaleMessage,\n        setLocaleMessage,\n        mergeLocaleMessage,\n        getDateTimeFormat,\n        setDateTimeFormat,\n        mergeDateTimeFormat,\n        getNumberFormat,\n        setNumberFormat,\n        mergeNumberFormat\n    };\n    function sync(composer) {\n        composer.locale.value = _locale.value;\n        composer.fallbackLocale.value = _fallbackLocale.value;\n        Object.keys(_messages.value).forEach(locale => {\n            composer.mergeLocaleMessage(locale, _messages.value[locale]);\n        });\n        Object.keys(_datetimeFormats.value).forEach(locale => {\n            composer.mergeDateTimeFormat(locale, _datetimeFormats.value[locale]);\n        });\n        Object.keys(_numberFormats.value).forEach(locale => {\n            composer.mergeNumberFormat(locale, _numberFormats.value[locale]);\n        });\n        composer.escapeParameter = _escapeParameter;\n        composer.fallbackFormat = _fallbackFormat;\n        composer.fallbackRoot = _fallbackRoot;\n        composer.fallbackWarn = _fallbackWarn;\n        composer.missingWarn = _missingWarn;\n        composer.warnHtmlMessage = _warnHtmlMessage;\n    }\n    onBeforeMount(() => {\n        if (instance.proxy == null || instance.proxy.$i18n == null) {\n            throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);\n        }\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const composer = (_composer.value = instance.proxy.$i18n\n            .__composer);\n        if (scope === 'global') {\n            _locale.value = composer.locale.value;\n            _fallbackLocale.value = composer.fallbackLocale.value;\n            _messages.value = composer.messages.value;\n            _datetimeFormats.value = composer.datetimeFormats.value;\n            _numberFormats.value = composer.numberFormats.value;\n        }\n        else if (isLocalScope) {\n            sync(composer);\n        }\n    });\n    return wrapper;\n}\nconst globalExportProps = [\n    'locale',\n    'fallbackLocale',\n    'availableLocales'\n];\nconst globalExportMethods = ['t', 'rt', 'd', 'n', 'tm', 'te']\n    ;\nfunction injectGlobalFields(app, composer) {\n    const i18n = Object.create(null);\n    globalExportProps.forEach(prop => {\n        const desc = Object.getOwnPropertyDescriptor(composer, prop);\n        if (!desc) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        const wrap = isRef(desc.value) // check computed props\n            ? {\n                get() {\n                    return desc.value.value;\n                },\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                set(val) {\n                    desc.value.value = val;\n                }\n            }\n            : {\n                get() {\n                    return desc.get && desc.get();\n                }\n            };\n        Object.defineProperty(i18n, prop, wrap);\n    });\n    app.config.globalProperties.$i18n = i18n;\n    globalExportMethods.forEach(method => {\n        const desc = Object.getOwnPropertyDescriptor(composer, method);\n        if (!desc || !desc.value) {\n            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);\n        }\n        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);\n    });\n    const dispose = () => {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        delete app.config.globalProperties.$i18n;\n        globalExportMethods.forEach(method => {\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            delete app.config.globalProperties[`$${method}`];\n        });\n    };\n    return dispose;\n}\n\n{\n    initFeatureFlags();\n}\n// register message compiler at vue-i18n\nif (__INTLIFY_JIT_COMPILATION__) {\n    registerMessageCompiler(compile);\n}\nelse {\n    registerMessageCompiler(compileToFunction);\n}\n// register message resolver at vue-i18n\nregisterMessageResolver(resolveValue);\n// register fallback locale at vue-i18n\nregisterLocaleFallbacker(fallbackWithLocaleChain);\n// NOTE: experimental !!\nif ((process.env.NODE_ENV !== 'production') || __INTLIFY_PROD_DEVTOOLS__) {\n    const target = getGlobalThis();\n    target.__INTLIFY__ = true;\n    setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);\n}\nif ((process.env.NODE_ENV !== 'production')) ;\n\nexport { DatetimeFormat, I18nD, I18nInjectionKey, I18nN, I18nT, NumberFormat, Translation, VERSION, castToVueI18n, createI18n, useI18n, vTDirective };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAAS,KAAK,KAAK,KAAK;AACpB,MAAI,OAAO,YAAY,aAAa;AAChC,YAAQ,KAAK,eAAe,GAAG;AAE/B,QAAI,KAAK;AACL,cAAQ,KAAK,IAAI,KAAK;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,IAAM,YAAY,CAAC;AACnB,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,UAAU,GAAG,GAAG;AACjB,cAAU,GAAG,IAAI;AACjB,SAAK,GAAG;AAAA,EACZ;AACJ;AAMA,IAAM,YAAY,OAAO,WAAW;AACpC,IAAI;AACJ,IAAI;AACJ,IAAK,MAAwC;AACzC,QAAM,OAAO,aAAa,OAAO;AACjC,MAAI,QACA,KAAK,QACL,KAAK,WACL,KAAK;AAAA,EAEL,KAAK,eAAe;AACpB,WAAO,CAAC,QAAQ;AACZ,WAAK,KAAK,GAAG;AAAA,IACjB;AACA,cAAU,CAAC,MAAM,UAAU,WAAW;AAClC,WAAK,QAAQ,MAAM,UAAU,MAAM;AACnC,WAAK,WAAW,QAAQ;AACxB,WAAK,WAAW,MAAM;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,IAAM,UAAU;AAEhB,SAAS,OAAO,YAAY,MAAM;AAC9B,MAAI,KAAK,WAAW,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG;AACxC,WAAO,KAAK,CAAC;AAAA,EACjB;AACA,MAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,QAAQ,QAAQ,SAAS,CAAC,OAAO,eAAe;AACnD,WAAO,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,EAChE,CAAC;AACL;AACA,IAAM,aAAa,CAAC,MAAM,YAAY,UAAU,CAAC,YAAY,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI;AAC3F,IAAM,yBAAyB,CAAC,QAAQ,KAAK,WAAW,sBAAsB,EAAE,GAAG,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC;AAC9G,IAAM,wBAAwB,CAAC,SAAS,KAAK,UAAU,IAAI,EACtD,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS;AACjC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,SAAS,GAAG;AACjE,IAAM,SAAS,CAAC,QAAQ,aAAa,GAAG,MAAM;AAC9C,IAAM,WAAW,CAAC,QAAQ,aAAa,GAAG,MAAM;AAChD,IAAM,gBAAgB,CAAC,QAAQ,cAAc,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW;AACjF,IAAM,SAAS,OAAO;AACtB,IAAM,UAAU,OAAO;AACvB,IAAM,SAAS,CAAC,MAAM,SAAS,QAAQ,GAAG;AAC1C,IAAI;AACJ,IAAM,gBAAgB,MAAM;AAExB,SAAQ,gBACH,cACG,OAAO,eAAe,cAChB,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,OAAO;AACrC;AACA,SAAS,WAAW,SAAS;AACzB,SAAO,QACF,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,OAAO,QAAQ,EACvB,QAAQ,MAAM,QAAQ;AAC/B;AACA,SAAS,qBAAqB,OAAO;AACjC,SAAO,MACF,QAAQ,4BAA4B,OAAO,EAC3C,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM;AAC7B;AACA,SAAS,uBAAuB,MAAM;AAGlC,SAAO,KAAK,QAAQ,0BAA0B,CAAC,GAAG,UAAU,cAAc,GAAG,QAAQ,KAAK,qBAAqB,SAAS,CAAC,GAAG;AAE5H,SAAO,KAAK,QAAQ,0BAA0B,CAAC,GAAG,UAAU,cAAc,GAAG,QAAQ,KAAK,qBAAqB,SAAS,CAAC,GAAG;AAE5H,QAAM,sBAAsB;AAC5B,MAAI,oBAAoB,KAAK,IAAI,GAAG;AAChC,QAAK,MAAwC;AACzC,WAAK,wIACyE;AAAA,IAClF;AAEA,WAAO,KAAK,QAAQ,wBAAwB,aAAa;AAAA,EAC7D;AAEA,QAAM,uBAAuB;AAAA;AAAA,IAEzB;AAAA;AAAA,IAEA;AAAA,EACJ;AACA,uBAAqB,QAAQ,aAAW;AACpC,WAAO,KAAK,QAAQ,SAAS,mBAAmB;AAAA,EACpD,CAAC;AACD,SAAO;AACX;AACA,IAAM,iBAAiB,OAAO,UAAU;AACxC,SAAS,OAAO,KAAK,KAAK;AACtB,SAAO,eAAe,KAAK,KAAK,GAAG;AACvC;AASA,IAAM,UAAU,MAAM;AACtB,IAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAC3C,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ;AAG1C,IAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ;AAEzD,IAAM,YAAY,CAAC,QAAQ;AACvB,SAAO,SAAS,GAAG,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AACxE;AACA,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,eAAe,CAAC,UAAU,eAAe,KAAK,KAAK;AACzD,IAAM,gBAAgB,CAAC,QAAQ;AAC3B,MAAI,CAAC,SAAS,GAAG;AACb,WAAO;AACX,QAAM,QAAQ,OAAO,eAAe,GAAG;AACvC,SAAO,UAAU,QAAQ,MAAM,gBAAgB;AACnD;AAEA,IAAM,kBAAkB,CAAC,QAAQ;AAC7B,SAAO,OAAO,OACR,KACA,QAAQ,GAAG,KAAM,cAAc,GAAG,KAAK,IAAI,aAAa,iBACpD,KAAK,UAAU,KAAK,MAAM,CAAC,IAC3B,OAAO,GAAG;AACxB;AACA,SAAS,KAAK,OAAO,YAAY,IAAI;AACjC,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAW,UAAU,IAAI,MAAM,OAAO,MAAM,YAAY,MAAO,EAAE;AACrG;AACA,IAAM,QAAQ;AACd,SAAS,kBAAkB,QAAQ,QAAQ,GAAG,MAAM,OAAO,QAAQ;AAC/D,QAAM,QAAQ,OAAO,MAAM,OAAO;AAClC,MAAI,QAAQ;AACZ,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,aAAS,MAAM,CAAC,EAAE,SAAS;AAC3B,QAAI,SAAS,OAAO;AAChB,eAAS,IAAI,IAAI,OAAO,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK;AACxD,YAAI,IAAI,KAAK,KAAK,MAAM;AACpB;AACJ,cAAM,OAAO,IAAI;AACjB,YAAI,KAAK,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE;AACtE,cAAM,aAAa,MAAM,CAAC,EAAE;AAC5B,YAAI,MAAM,GAAG;AAET,gBAAM,MAAM,SAAS,QAAQ,cAAc;AAC3C,gBAAM,SAAS,KAAK,IAAI,GAAG,MAAM,QAAQ,aAAa,MAAM,MAAM,KAAK;AACvE,cAAI,KAAK,WAAW,IAAI,OAAO,GAAG,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,QAC5D,WACS,IAAI,GAAG;AACZ,cAAI,MAAM,OAAO;AACb,kBAAM,SAAS,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;AAC5D,gBAAI,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC;AAAA,UAC1C;AACA,mBAAS,aAAa;AAAA,QAC1B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,IAAI,KAAK,IAAI;AACxB;AACA,SAAS,YAAYA,OAAM;AACvB,MAAI,UAAUA;AACd,SAAO,MAAM,EAAE;AACnB;AAcA,SAAS,gBAAgB;AACrB,QAAM,SAAS,oBAAI,IAAI;AACvB,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,GAAG,OAAO,SAAS;AACf,YAAM,WAAW,OAAO,IAAI,KAAK;AACjC,YAAM,QAAQ,YAAY,SAAS,KAAK,OAAO;AAC/C,UAAI,CAAC,OAAO;AACR,eAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,IAAI,OAAO,SAAS;AAChB,YAAM,WAAW,OAAO,IAAI,KAAK;AACjC,UAAI,UAAU;AACV,iBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,KAAK,OAAO,SAAS;AACjB,OAAC,OAAO,IAAI,KAAK,KAAK,CAAC,GAClB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,CAAC;AACpC,OAAC,OAAO,IAAI,GAAG,KAAK,CAAC,GAChB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,IAC/C;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,uBAAuB,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,QAAQ,GAAG;AAEnE,SAAS,SAAS,KAAK,KAAK;AAExB,MAAI,qBAAqB,GAAG,KAAK,qBAAqB,GAAG,GAAG;AACxD,UAAM,IAAI,MAAM,eAAe;AAAA,EACnC;AACA,QAAM,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC;AAC3B,SAAO,MAAM,QAAQ;AACjB,UAAM,EAAE,KAAAC,MAAK,KAAAC,KAAI,IAAI,MAAM,IAAI;AAE/B,WAAO,KAAKD,IAAG,EAAE,QAAQ,SAAO;AAC5B,UAAI,QAAQ,aAAa;AACrB;AAAA,MACJ;AAGA,UAAI,SAASA,KAAI,GAAG,CAAC,KAAK,CAAC,SAASC,KAAI,GAAG,CAAC,GAAG;AAC3C,QAAAA,KAAI,GAAG,IAAI,MAAM,QAAQD,KAAI,GAAG,CAAC,IAAI,CAAC,IAAI,OAAO;AAAA,MACrD;AACA,UAAI,qBAAqBC,KAAI,GAAG,CAAC,KAAK,qBAAqBD,KAAI,GAAG,CAAC,GAAG;AAIlE,QAAAC,KAAI,GAAG,IAAID,KAAI,GAAG;AAAA,MACtB,OACK;AAED,cAAM,KAAK,EAAE,KAAKA,KAAI,GAAG,GAAG,KAAKC,KAAI,GAAG,EAAE,CAAC;AAAA,MAC/C;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;;;ACtRA,SAAS,eAAe,MAAM,QAAQ,QAAQ;AAC1C,SAAO,EAAE,MAAM,QAAQ,OAAO;AAClC;AACA,SAAS,eAAe,OAAO,KAAK,QAAQ;AACxC,QAAM,MAAM,EAAE,OAAO,IAAI;AACzB,MAAI,UAAU,MAAM;AAChB,QAAI,SAAS;AAAA,EACjB;AACA,SAAO;AACX;AAMA,IAAMC,WAAU;AAEhB,SAASC,QAAO,YAAY,MAAM;AAC9B,MAAI,KAAK,WAAW,KAAKC,UAAS,KAAK,CAAC,CAAC,GAAG;AACxC,WAAO,KAAK,CAAC;AAAA,EACjB;AACA,MAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,QAAQ,QAAQF,UAAS,CAAC,OAAO,eAAe;AACnD,WAAO,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,EAChE,CAAC;AACL;AACA,IAAMG,UAAS,OAAO;AACtB,IAAMC,YAAW,CAAC,QAAQ,OAAO,QAAQ;AAEzC,IAAMF,YAAW,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ;AACzD,SAASG,MAAK,OAAO,YAAY,IAAI;AACjC,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAW,UAAU,IAAI,MAAM,OAAO,MAAM,YAAY,MAAO,EAAE;AACrG;AAEA,IAAM,mBAAmB;AAAA,EACrB,mBAAmB;AAAA,EACnB,kBAAkB;AACtB;AAEA,IAAM,eAAe;AAAA,EACjB,CAAC,iBAAiB,iBAAiB,GAAG;AAC1C;AACA,SAAS,kBAAkBC,OAAM,QAAQ,MAAM;AAC3C,QAAM,MAAML,QAAO,aAAaK,KAAI,KAAK,IAAI,GAAI,QAAQ,CAAC,CAAE;AAC5D,QAAM,UAAU,EAAE,SAAS,OAAO,GAAG,GAAG,MAAAA,MAAK;AAC7C,MAAI,KAAK;AACL,YAAQ,WAAW;AAAA,EACvB;AACA,SAAO;AACX;AAEA,IAAM,oBAAoB;AAAA;AAAA,EAEtB,gBAAgB;AAAA,EAChB,8BAA8B;AAAA,EAC9B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,iCAAiC;AAAA,EACjC,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,mBAAmB;AAAA,EACnB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA;AAAA,EAEvB,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA;AAAA,EAE7B,6BAA6B;AAAA;AAAA,EAE7B,8BAA8B;AAAA;AAAA;AAAA;AAAA,EAI9B,kBAAkB;AACtB;AAEA,IAAM,gBAAgB;AAAA;AAAA,EAElB,CAAC,kBAAkB,cAAc,GAAG;AAAA,EACpC,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,EAClD,CAAC,kBAAkB,wCAAwC,GAAG;AAAA,EAC9D,CAAC,kBAAkB,uBAAuB,GAAG;AAAA,EAC7C,CAAC,kBAAkB,+BAA+B,GAAG;AAAA,EACrD,CAAC,kBAAkB,wBAAwB,GAAG;AAAA,EAC9C,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,EAChD,CAAC,kBAAkB,iBAAiB,GAAG;AAAA,EACvC,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,EAChD,CAAC,kBAAkB,qBAAqB,GAAG;AAAA;AAAA,EAE3C,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,EAClD,CAAC,kBAAkB,gCAAgC,GAAG;AAAA,EACtD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA,EACjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA;AAAA,EAEjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA;AAAA,EAEjD,CAAC,kBAAkB,4BAA4B,GAAG;AACtD;AACA,SAAS,mBAAmBA,OAAM,KAAK,UAAU,CAAC,GAAG;AACjD,QAAM,EAAE,QAAQ,UAAU,KAAK,IAAI;AACnC,QAAM,MAAML,SAAQ,YAAY,eAAeK,KAAI,KAAK,IAAI,GAAI,QAAQ,CAAC,CAAE;AAE3E,QAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,QAAM,OAAOA;AACb,MAAI,KAAK;AACL,UAAM,WAAW;AAAA,EACrB;AACA,QAAM,SAAS;AACf,SAAO;AACX;AAEA,SAAS,eAAe,OAAO;AAC3B,QAAM;AACV;AAGA,IAAM,cAAc;AACpB,IAAM,gBAAgB,CAAC,WAAW,YAAY,KAAK,MAAM;AAEzD,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,IAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,SAAS,cAAc,KAAK;AACxB,QAAM,OAAO;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,UAAU;AACd,MAAI,cAAc;AAClB,QAAM,SAAS,CAACC,WAAU,KAAKA,MAAK,MAAM,WAAW,KAAKA,SAAQ,CAAC,MAAM;AACzE,QAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,QAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,QAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,QAAM,YAAY,CAACA,WAAU,OAAOA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK;AACtF,QAAM,QAAQ,MAAM;AACpB,QAAM,OAAO,MAAM;AACnB,QAAM,SAAS,MAAM;AACrB,QAAM,aAAa,MAAM;AACzB,QAAM,SAAS,CAAC,WAAW,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;AACjG,QAAM,cAAc,MAAM,OAAO,MAAM;AACvC,QAAM,cAAc,MAAM,OAAO,SAAS,WAAW;AACrD,WAAS,OAAO;AACZ,kBAAc;AACd,QAAI,UAAU,MAAM,GAAG;AACnB;AACA,gBAAU;AAAA,IACd;AACA,QAAI,OAAO,MAAM,GAAG;AAChB;AAAA,IACJ;AACA;AACA;AACA,WAAO,KAAK,MAAM;AAAA,EACtB;AACA,WAAS,OAAO;AACZ,QAAI,OAAO,SAAS,WAAW,GAAG;AAC9B;AAAA,IACJ;AACA;AACA,WAAO,KAAK,SAAS,WAAW;AAAA,EACpC;AACA,WAAS,QAAQ;AACb,aAAS;AACT,YAAQ;AACR,cAAU;AACV,kBAAc;AAAA,EAClB;AACA,WAAS,UAAU,SAAS,GAAG;AAC3B,kBAAc;AAAA,EAClB;AACA,WAAS,aAAa;AAClB,UAAM,SAAS,SAAS;AAExB,WAAO,WAAW,QAAQ;AACtB,WAAK;AAAA,IACT;AACA,kBAAc;AAAA,EAClB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,SAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,QAAQ,cAAc,MAAM;AAClC,QAAM,gBAAgB,MAAM,MAAM,MAAM;AACxC,QAAM,kBAAkB,MAAM,eAAe,MAAM,KAAK,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;AACxF,QAAM,WAAW,gBAAgB;AACjC,QAAM,cAAc,cAAc;AAClC,QAAM,WAAW;AAAA,IACb,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,EACV;AACA,QAAM,UAAU,MAAM;AACtB,QAAM,EAAE,QAAQ,IAAI;AACpB,WAAS,UAAUD,OAAM,KAAK,WAAW,MAAM;AAC3C,UAAM,MAAM,QAAQ;AACpB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,WAAW,eAAe,IAAI,UAAU,GAAG,IAAI;AAC3D,YAAM,MAAM,mBAAmBA,OAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,cAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACA,WAAS,SAASE,UAAS,MAAM,OAAO;AACpC,IAAAA,SAAQ,SAAS,gBAAgB;AACjC,IAAAA,SAAQ,cAAc;AACtB,UAAM,QAAQ,EAAE,KAAK;AACrB,QAAI,UAAU;AACV,YAAM,MAAM,eAAeA,SAAQ,UAAUA,SAAQ,MAAM;AAAA,IAC/D;AACA,QAAI,SAAS,MAAM;AACf,YAAM,QAAQ;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,QAAM,cAAc,CAACA,aAAY;AAAA,IAASA;AAAA,IAAS;AAAA;AAAA,EAAuB;AAC1E,WAAS,IAAI,MAAM,IAAI;AACnB,QAAI,KAAK,YAAY,MAAM,IAAI;AAC3B,WAAK,KAAK;AACV,aAAO;AAAA,IACX,OACK;AACD,gBAAU,kBAAkB,gBAAgB,gBAAgB,GAAG,GAAG,EAAE;AACpE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,WAAS,WAAW,MAAM;AACtB,QAAI,MAAM;AACV,WAAO,KAAK,YAAY,MAAM,WAAW,KAAK,YAAY,MAAM,SAAS;AACrE,aAAO,KAAK,YAAY;AACxB,WAAK,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW,MAAM;AACtB,UAAM,MAAM,WAAW,IAAI;AAC3B,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,WAAS,kBAAkB,IAAI;AAC3B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,EAEf;AACA,WAAS,cAAc,IAAI;AACvB,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B;AACA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,KAAK,KAAK,YAAY,MAAM,MAAM,KAAK,KAAK,IAAI,KAAK,YAAY;AACvE,UAAM,MAAM,cAAc,EAAE;AAC5B,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,eAAe,MAAMA,UAAS;AACnC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,iBAAiB,MAAMA,UAAS;AACrC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAgC;AAChD,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,EAAE,gBAAgB,KAClB,gBAAgB,KAAqC;AACrD,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAMA,UAAS;AACvC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,IAAqC;AACrD,aAAO;AAAA,IACX;AACA,UAAM,KAAK,MAAM;AACb,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,KAAgC;AACvC,eAAO,kBAAkB,KAAK,KAAK,CAAC;AAAA,MACxC,WACS,OAAO,OACZ,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,WACP,CAAC,IAAI;AACL,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG;AAAA,MACd,OACK;AAED,eAAO,YAAY,MAAM,KAAK;AAAA,MAClC;AAAA,IACJ;AACA,UAAM,MAAM,GAAG;AACf,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,cAAc,MAAM;AACzB,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,kBAAkB,MAAM;AAC7B,UAAM,SAAS,WAAW,IAAI;AAC9B,UAAM,MAAM,KAAK,YAAY,MAAM,OAC/B,KAAK,KAAK,MAAM;AACpB,SAAK,UAAU;AACf,WAAO;AAAA,MACH,UAAU;AAAA,MACV,UAAU,OAAO,SAAS;AAAA,IAC9B;AAAA,EACJ;AACA,WAAS,YAAY,MAAM,QAAQ,MAAM;AACrC,UAAM,KAAK,CAAC,WAAW,OAAO,OAAO,IAAI,eAAe,UAAU;AAC9D,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,KAAgC;AACvC,eAAO,SAAS,MAA8B,QAAQ;AAAA,MAC1D,WACS,OAAO,OAAoC,CAAC,IAAI;AACrD,eAAO,SAAS,MAA8B,OAAO;AAAA,MACzD,WACS,OAAO,KAA6B;AACzC,aAAK,KAAK;AACV,eAAO,GAAG,UAAU,KAA6B,IAAI;AAAA,MACzD,WACS,OAAO,KAA2B;AACvC,eAAO,SAAS,OAA+B,eACzC,OACA,EAAE,SAAS,WAAW,SAAS;AAAA,MACzC,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,SAAS,YAAY;AAAA,MACzC,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,SAAS,YAAY;AAAA,MACzC,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,UAAM,MAAM,GAAG;AACf,aAAS,KAAK,UAAU;AACxB,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM,IAAI;AACxB,UAAM,KAAK,KAAK,YAAY;AAC5B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,QAAI,GAAG,EAAE,GAAG;AACR,WAAK,KAAK;AACV,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,WAAS,aAAa,IAAI;AACtB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,EAEf;AACA,WAAS,mBAAmB,MAAM;AAC9B,WAAO,SAAS,MAAM,YAAY;AAAA,EACtC;AACA,WAAS,kBAAkB,IAAI;AAC3B,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EAEf;AACA,WAAS,wBAAwB,MAAM;AACnC,WAAO,SAAS,MAAM,iBAAiB;AAAA,EAC3C;AACA,WAAS,QAAQ,IAAI;AACjB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B;AACA,WAAS,UAAU,MAAM;AACrB,WAAO,SAAS,MAAM,OAAO;AAAA,EACjC;AACA,WAAS,WAAW,IAAI;AACpB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,EAC3B;AACA,WAAS,aAAa,MAAM;AACxB,WAAO,SAAS,MAAM,UAAU;AAAA,EACpC;AACA,WAAS,UAAU,MAAM;AACrB,QAAI,KAAK;AACT,QAAI,MAAM;AACV,WAAQ,KAAK,UAAU,IAAI,GAAI;AAC3B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW,MAAM;AACtB,eAAW,IAAI;AACf,UAAM,KAAK,KAAK,YAAY;AAC5B,QAAI,OAAO,KAA6B;AACpC,gBAAU,kBAAkB,gBAAgB,gBAAgB,GAAG,GAAG,EAAE;AAAA,IACxE;AACA,SAAK,KAAK;AACV,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM;AACpB,QAAI,MAAM;AAEV,WAAO,MAAM;AACT,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL;AAAA,MACJ,WACS,OAAO,KAA6B;AACzC,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAK;AAAA,QACd,OACK;AACD;AAAA,QACJ;AAAA,MACJ,WACS,OAAO,WAAW,OAAO,SAAS;AACvC,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAK;AAAA,QACd,WACS,cAAc,IAAI,GAAG;AAC1B;AAAA,QACJ,OACK;AACD,iBAAO;AACP,eAAK,KAAK;AAAA,QACd;AAAA,MACJ,OACK;AACD,eAAO;AACP,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB,MAAM;AAC/B,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,wBAAwB,IAAI,GAAI;AACzC,cAAQ;AAAA,IACZ;AACA,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,gBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,IAChF;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,eAAW,IAAI;AACf,QAAI,QAAQ;AACZ,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,WAAK,KAAK;AACV,eAAS,IAAI,UAAU,IAAI,CAAC;AAAA,IAChC,OACK;AACD,eAAS,UAAU,IAAI;AAAA,IAC3B;AACA,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,gBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,IAChF;AACA,WAAO;AAAA,EACX;AACA,WAASC,WAAU,IAAI;AACnB,WAAO,OAAO,qBAAqB,OAAO;AAAA,EAC9C;AACA,WAAS,YAAY,MAAM;AACvB,eAAW,IAAI;AAEf,QAAI,MAAM,GAAI;AACd,QAAI,KAAK;AACT,QAAI,UAAU;AACd,WAAQ,KAAK,SAAS,MAAMA,UAAS,GAAI;AACrC,UAAI,OAAO,MAAM;AACb,mBAAW,mBAAmB,IAAI;AAAA,MACtC,OACK;AACD,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,YAAY,WAAW,YAAY,KAAK;AACxC,gBAAU,kBAAkB,0CAA0C,gBAAgB,GAAG,CAAC;AAE1F,UAAI,YAAY,SAAS;AACrB,aAAK,KAAK;AAEV,YAAI,MAAM,GAAI;AAAA,MAClB;AACA,aAAO;AAAA,IACX;AAEA,QAAI,MAAM,GAAI;AACd,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ,IAAI;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AACD,aAAK,KAAK;AACV,eAAO,KAAK,EAAE;AAAA,MAClB,KAAK;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,MAChD,KAAK;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,MAChD;AACI,kBAAU,kBAAkB,yBAAyB,gBAAgB,GAAG,GAAG,EAAE;AAC7E,eAAO;AAAA,IACf;AAAA,EACJ;AACA,WAAS,0BAA0B,MAAM,SAAS,QAAQ;AACtD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAM,KAAK,aAAa,IAAI;AAC5B,UAAI,CAAC,IAAI;AACL,kBAAU,kBAAkB,iCAAiC,gBAAgB,GAAG,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK,YAAY,CAAC,EAAE;AACjI;AAAA,MACJ;AACA,kBAAY;AAAA,IAChB;AACA,WAAO,KAAK,OAAO,GAAG,QAAQ;AAAA,EAClC;AACA,WAAS,oBAAoB,IAAI;AAC7B,WAAQ,OAAO,OACX,OAAO,OACP,OAAO,WACP,OAAO;AAAA,EACf;AACA,WAAS,sBAAsB,MAAM;AACjC,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,cAAc;AAClB,WAAQ,KAAK,SAAS,MAAM,mBAAmB,GAAI;AAC/C,qBAAe;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,cAAQ;AAAA,IACZ;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,MAAM;AAC3B,UAAM,KAAK,CAAC,QAAQ;AAChB,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,eAAO;AAAA,MACX,WACS,OAAO,WAAW,OAAO,KAAK;AACnC,eAAO;AACP,aAAK,KAAK;AACV,eAAO,GAAG,GAAG;AAAA,MACjB,OACK;AACD,eAAO;AACP,aAAK,KAAK;AACV,eAAO,GAAG,GAAG;AAAA,MACjB;AAAA,IACJ;AACA,WAAO,GAAG,EAAE;AAAA,EAChB;AACA,WAAS,WAAW,MAAM;AACtB,eAAW,IAAI;AACf,UAAM,SAAS;AAAA,MAAI;AAAA,MAAM;AAAA;AAAA,IAAyB;AAClD,eAAW,IAAI;AACf,WAAO;AAAA,EACX;AAEA,WAAS,uBAAuB,MAAMD,UAAS;AAC3C,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ,IAAI;AAAA,MACR,KAAK;AACD,YAAIA,SAAQ,aAAa,GAAG;AACxB,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,QAChF;AACA,aAAK,KAAK;AACV,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAA8B;AAAA;AAAA,QAA8B;AACtF,mBAAW,IAAI;AACf,QAAAA,SAAQ;AACR,eAAO;AAAA,MACX,KAAK;AACD,YAAIA,SAAQ,YAAY,KACpBA,SAAQ,gBAAgB,GAA8B;AACtD,oBAAU,kBAAkB,mBAAmB,gBAAgB,GAAG,CAAC;AAAA,QACvE;AACA,aAAK,KAAK;AACV,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAA+B;AAAA;AAAA,QAA+B;AACxF,QAAAA,SAAQ;AACR,QAAAA,SAAQ,YAAY,KAAK,WAAW,IAAI;AACxC,YAAIA,SAAQ,YAAYA,SAAQ,cAAc,GAAG;AAC7C,UAAAA,SAAQ,WAAW;AAAA,QACvB;AACA,eAAO;AAAA,MACX,KAAK;AACD,YAAIA,SAAQ,YAAY,GAAG;AACvB,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,QAChF;AACA,gBAAQ,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAC/D,QAAAA,SAAQ,YAAY;AACpB,eAAO;AAAA,MACX,SAAS;AACL,YAAI,uBAAuB;AAC3B,YAAI,sBAAsB;AAC1B,YAAI,eAAe;AACnB,YAAI,cAAc,IAAI,GAAG;AACrB,cAAIA,SAAQ,YAAY,GAAG;AACvB,sBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,UAChF;AACA,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAIA,SAAQ,YAAY,MACnBA,SAAQ,gBAAgB,KACrBA,SAAQ,gBAAgB,KACxBA,SAAQ,gBAAgB,IAA6B;AACzD,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAC5E,UAAAA,SAAQ,YAAY;AACpB,iBAAO,UAAU,MAAMA,QAAO;AAAA,QAClC;AACA,YAAK,uBAAuB,uBAAuB,MAAMA,QAAO,GAAI;AAChE,kBAAQ,SAASA,UAAS,GAA0B,oBAAoB,IAAI,CAAC;AAC7E,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAK,sBAAsB,sBAAsB,MAAMA,QAAO,GAAI;AAC9D,kBAAQ,SAASA,UAAS,GAAyB,mBAAmB,IAAI,CAAC;AAC3E,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAK,eAAe,eAAe,MAAMA,QAAO,GAAI;AAChD,kBAAQ,SAASA,UAAS,GAA4B,YAAY,IAAI,CAAC;AACvE,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,cAAc;AAEhE,kBAAQ,SAASA,UAAS,IAAkC,sBAAsB,IAAI,CAAC;AACvF,oBAAU,kBAAkB,8BAA8B,gBAAgB,GAAG,GAAG,MAAM,KAAK;AAC3F,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAEA,WAAS,kBAAkB,MAAMA,UAAS;AACtC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK,YAAY;AAC5B,SAAK,gBAAgB,KACjB,gBAAgB,KAChB,gBAAgB,MAChB,gBAAgB,QACf,OAAO,WAAW,OAAO,UAAU;AACpC,gBAAU,kBAAkB,uBAAuB,gBAAgB,GAAG,CAAC;AAAA,IAC3E;AACA,YAAQ,IAAI;AAAA,MACR,KAAK;AACD,aAAK,KAAK;AACV,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAAgC;AAAA;AAAA,QAAgC;AAC1F,QAAAA,SAAQ,WAAW;AACnB,eAAO;AAAA,MACX,KAAK;AACD,mBAAW,IAAI;AACf,aAAK,KAAK;AACV,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAA8B;AAAA;AAAA,QAA8B;AAAA,MACzF,KAAK;AACD,mBAAW,IAAI;AACf,aAAK,KAAK;AACV,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAAqC;AAAA;AAAA,QAAoC;AAAA,MACtG;AACI,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,MAAMA,QAAO,KAC9B,uBAAuB,MAAMA,QAAO,GAAG;AACvC,qBAAW,IAAI;AACf,iBAAO,kBAAkB,MAAMA,QAAO;AAAA,QAC1C;AACA,YAAI,sBAAsB,MAAMA,QAAO,GAAG;AACtC,qBAAW,IAAI;AACf,iBAAO,SAASA,UAAS,IAAoC,mBAAmB,IAAI,CAAC;AAAA,QACzF;AACA,YAAI,mBAAmB,MAAMA,QAAO,GAAG;AACnC,qBAAW,IAAI;AACf,cAAI,OAAO,KAAgC;AAEvC,mBAAO,uBAAuB,MAAMA,QAAO,KAAK;AAAA,UACpD,OACK;AACD,mBAAO,SAASA,UAAS,IAA+B,gBAAgB,IAAI,CAAC;AAAA,UACjF;AAAA,QACJ;AACA,YAAI,gBAAgB,GAAgC;AAChD,oBAAU,kBAAkB,uBAAuB,gBAAgB,GAAG,CAAC;AAAA,QAC3E;AACA,QAAAA,SAAQ,YAAY;AACpB,QAAAA,SAAQ,WAAW;AACnB,eAAO,UAAU,MAAMA,QAAO;AAAA,IACtC;AAAA,EACJ;AAEA,WAAS,UAAU,MAAMA,UAAS;AAC9B,QAAI,QAAQ;AAAA,MAAE,MAAM;AAAA;AAAA,IAAwB;AAC5C,QAAIA,SAAQ,YAAY,GAAG;AACvB,aAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IACvE;AACA,QAAIA,SAAQ,UAAU;AAClB,aAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IAClE;AACA,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ,IAAI;AAAA,MACR,KAAK;AACD,eAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,MACvE,KAAK;AACD,kBAAU,kBAAkB,0BAA0B,gBAAgB,GAAG,CAAC;AAC1E,aAAK,KAAK;AACV,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAA+B;AAAA;AAAA,QAA+B;AAAA,MAC3F,KAAK;AACD,eAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,MAClE,SAAS;AACL,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,cAAM,EAAE,UAAU,SAAS,IAAI,kBAAkB,IAAI;AACrD,YAAI,UAAU;AACV,iBAAO,WACD,SAASA,UAAS,GAAyB,SAAS,IAAI,CAAC,IACzD,SAASA,UAAS,GAA2B,WAAW,IAAI,CAAC;AAAA,QACvE;AACA,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO,SAASA,UAAS,GAAyB,SAAS,IAAI,CAAC;AAAA,QACpE;AACA;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,YAAY;AACjB,UAAM,EAAE,aAAa,QAAQ,UAAU,OAAO,IAAI;AAClD,aAAS,WAAW;AACpB,aAAS,aAAa;AACtB,aAAS,eAAe;AACxB,aAAS,aAAa;AACtB,aAAS,SAAS,cAAc;AAChC,aAAS,WAAW,gBAAgB;AACpC,QAAI,MAAM,YAAY,MAAM,KAAK;AAC7B,aAAO;AAAA,QAAS;AAAA,QAAU;AAAA;AAAA,MAAuB;AAAA,IACrD;AACA,WAAO,UAAU,OAAO,QAAQ;AAAA,EACpC;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB;AAEvB,IAAM,gBAAgB;AACtB,SAAS,mBAAmB,OAAO,YAAY,YAAY;AACvD,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IAEX,KAAK;AAED,aAAO;AAAA,IACX,SAAS;AACL,YAAM,YAAY,SAAS,cAAc,YAAY,EAAE;AACvD,UAAI,aAAa,SAAU,aAAa,OAAQ;AAC5C,eAAO,OAAO,cAAc,SAAS;AAAA,MACzC;AAGA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,UAAU,CAAC,GAAG;AAChC,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,EAAE,SAAS,OAAO,IAAI;AAC5B,WAAS,UAAU,UAAUF,OAAM,OAAO,WAAW,MAAM;AACvD,UAAM,MAAM,SAAS,gBAAgB;AACrC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,WAAW,eAAe,OAAO,GAAG,IAAI;AACpD,YAAM,MAAM,mBAAmBA,OAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,cAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACA,WAAS,SAAS,UAAUA,OAAM,OAAO,WAAW,MAAM;AACtD,UAAM,MAAM,SAAS,gBAAgB;AACrC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,QAAQ;AACR,YAAM,MAAM,WAAW,eAAe,OAAO,GAAG,IAAI;AACpD,aAAO,kBAAkBA,OAAM,KAAK,IAAI,CAAC;AAAA,IAC7C;AAAA,EACJ;AACA,WAAS,UAAU,MAAM,QAAQ,KAAK;AAClC,UAAM,OAAO,EAAE,KAAK;AACpB,QAAI,UAAU;AACV,WAAK,QAAQ;AACb,WAAK,MAAM;AACX,WAAK,MAAM,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IACtC;AACA,WAAO;AAAA,EACX;AACA,WAAS,QAAQ,MAAM,QAAQ,KAAK,MAAM;AACtC,QAAI,MAAM;AACN,WAAK,OAAO;AAAA,IAChB;AACA,QAAI,UAAU;AACV,WAAK,MAAM;AACX,UAAI,KAAK,KAAK;AACV,aAAK,IAAI,MAAM;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAAwB,QAAQ,QAAQ,QAAQ,QAAQ;AAC/E,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAwB,QAAQ,GAAG;AAC1D,SAAK,QAAQ,SAAS,OAAO,EAAE;AAC/B,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,WAAW,WAAW,KAAK,QAAQ;AACxC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAyB,QAAQ,GAAG;AAC3D,SAAK,MAAM;AACX,QAAI,WAAW,MAAM;AACjB,WAAK,SAAS;AAAA,IAClB;AACA,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,aAAa,WAAW,OAAO;AACpC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAA2B,QAAQ,GAAG;AAC7D,SAAK,QAAQ,MAAM,QAAQ,eAAe,kBAAkB;AAC5D,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB,WAAW;AACpC,UAAM,QAAQ,UAAU,UAAU;AAClC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAkC,QAAQ,GAAG;AACpE,QAAI,MAAM,SAAS,IAAoC;AAEnD,gBAAU,WAAW,kBAAkB,kCAAkC,QAAQ,cAAc,CAAC;AAChG,WAAK,QAAQ;AACb,cAAQ,MAAM,QAAQ,GAAG;AACzB,aAAO;AAAA,QACH,kBAAkB;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,MAAM,SAAS,MAAM;AACrB,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IACvH;AACA,SAAK,QAAQ,MAAM,SAAS;AAC5B,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,MACH;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,eAAe,WAAW,OAAO;AACtC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAA6B,QAAQ,QAAQ,QAAQ,QAAQ;AACpF,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,YAAY,WAAW;AAC5B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,aAAa,UAAU,GAA0B,QAAQ,QAAQ,QAAQ,QAAQ;AACvF,QAAI,QAAQ,UAAU,UAAU;AAChC,QAAI,MAAM,SAAS,GAA8B;AAC7C,YAAM,SAAS,oBAAoB,SAAS;AAC5C,iBAAW,WAAW,OAAO;AAC7B,cAAQ,OAAO,oBAAoB,UAAU,UAAU;AAAA,IAC3D;AAEA,QAAI,MAAM,SAAS,IAAqC;AACpD,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IACvH;AACA,YAAQ,UAAU,UAAU;AAE5B,QAAI,MAAM,SAAS,GAA8B;AAC7C,cAAQ,UAAU,UAAU;AAAA,IAChC;AACA,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,eAAe,WAAW,MAAM,SAAS,EAAE;AAC5D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,WAAW,WAAW,MAAM,SAAS,EAAE;AACxD;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,UAAU,WAAW,MAAM,SAAS,EAAE;AACvD;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,aAAa,WAAW,MAAM,SAAS,EAAE;AAC1D;AAAA,MACJ,SAAS;AAEL,kBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,CAAC;AAC3F,cAAM,cAAc,UAAU,QAAQ;AACtC,cAAM,qBAAqB,UAAU,GAA6B,YAAY,QAAQ,YAAY,QAAQ;AAC1G,2BAAmB,QAAQ;AAC3B,gBAAQ,oBAAoB,YAAY,QAAQ,YAAY,QAAQ;AACpE,mBAAW,MAAM;AACjB,gBAAQ,YAAY,YAAY,QAAQ,YAAY,QAAQ;AAC5D,eAAO;AAAA,UACH,kBAAkB;AAAA,UAClB,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,YAAY,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AAC1E,WAAO;AAAA,MACH,MAAM;AAAA,IACV;AAAA,EACJ;AACA,WAAS,aAAa,WAAW;AAC7B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,cAAc,QAAQ,gBAAgB,IACtC,UAAU,cAAc,IACxB,QAAQ;AACd,UAAM,WAAW,QAAQ,gBAAgB,IACnC,QAAQ,SACR,QAAQ;AACd,UAAM,OAAO,UAAU,GAA2B,aAAa,QAAQ;AACvE,SAAK,QAAQ,CAAC;AACd,QAAI,YAAY;AAChB,QAAI,SAAS;AACb,OAAG;AACC,YAAM,QAAQ,aAAa,UAAU,UAAU;AAC/C,kBAAY;AACZ,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,QACJ,KAAK;AACD,mBAAS;AACT;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,WAAW,WAAW,MAAM,SAAS,IAAI,CAAC,CAAC,MAAM,CAAC;AAClE,cAAI,QAAQ;AACR,qBAAS,WAAW,iBAAiB,mBAAmB,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AACvG,qBAAS;AAAA,UACb;AACA;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,aAAa,WAAW,MAAM,SAAS,EAAE,CAAC;AAC1D;AAAA,QACJ,KAAK,GAAgC;AACjC,gBAAM,SAAS,YAAY,SAAS;AACpC,eAAK,MAAM,KAAK,OAAO,IAAI;AAC3B,sBAAY,OAAO,oBAAoB;AACvC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,SAAS,QAAQ,gBAAgB,MAC7B,QAAQ,gBAAgB;AAE5B,UAAM,YAAY,QAAQ,gBAAgB,IACpC,QAAQ,aACR,UAAU,cAAc;AAC9B,UAAM,SAAS,QAAQ,gBAAgB,IACjC,QAAQ,aACR,UAAU,gBAAgB;AAChC,YAAQ,MAAM,WAAW,MAAM;AAC/B,WAAO;AAAA,EACX;AACA,WAAS,YAAY,WAAW,QAAQ,KAAK,SAAS;AAClD,UAAM,UAAU,UAAU,QAAQ;AAClC,QAAI,kBAAkB,QAAQ,MAAM,WAAW;AAC/C,UAAM,OAAO,UAAU,GAA0B,QAAQ,GAAG;AAC5D,SAAK,QAAQ,CAAC;AACd,SAAK,MAAM,KAAK,OAAO;AACvB,OAAG;AACC,YAAM,MAAM,aAAa,SAAS;AAClC,UAAI,CAAC,iBAAiB;AAClB,0BAAkB,IAAI,MAAM,WAAW;AAAA,MAC3C;AACA,WAAK,MAAM,KAAK,GAAG;AAAA,IACvB,SAAS,QAAQ,gBAAgB;AACjC,QAAI,iBAAiB;AACjB,gBAAU,WAAW,kBAAkB,8BAA8B,KAAK,CAAC;AAAA,IAC/E;AACA,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,cAAc,WAAW;AAC9B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,UAAM,UAAU,aAAa,SAAS;AACtC,QAAI,QAAQ,gBAAgB,IAAyB;AACjD,aAAO;AAAA,IACX,OACK;AACD,aAAO,YAAY,WAAW,QAAQ,UAAU,OAAO;AAAA,IAC3D;AAAA,EACJ;AACA,WAASI,OAAM,QAAQ;AACnB,UAAM,YAAY,gBAAgB,QAAQP,QAAO,CAAC,GAAG,OAAO,CAAC;AAC7D,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAA4B,QAAQ,QAAQ,QAAQ,QAAQ;AACnF,QAAI,YAAY,KAAK,KAAK;AACtB,WAAK,IAAI,SAAS;AAAA,IACtB;AACA,SAAK,OAAO,cAAc,SAAS;AACnC,QAAI,QAAQ,YAAY;AACpB,WAAK,WAAW,QAAQ,WAAW,MAAM;AAAA,IAC7C;AAEA,QAAI,QAAQ,gBAAgB,IAAyB;AACjD,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,IAC7H;AACA,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,SAAO,EAAE,OAAAO,OAAM;AACnB;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI,MAAM,SAAS,IAAyB;AACxC,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,MAAM,SAAS,IAAI,QAAQ,WAAW,KAAK;AACzD,SAAO,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM;AACvD;AAEA,SAAS,kBAAkB,KAAK,UAAU,CAAC,GACzC;AACE,QAAM,WAAW;AAAA,IACb;AAAA,IACA,SAAS,oBAAI,IAAI;AAAA,EACrB;AACA,QAAM,UAAU,MAAM;AACtB,QAAM,SAAS,CAAC,SAAS;AACrB,aAAS,QAAQ,IAAI,IAAI;AACzB,WAAO;AAAA,EACX;AACA,SAAO,EAAE,SAAS,OAAO;AAC7B;AACA,SAAS,cAAc,OAAO,aAAa;AACvC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,iBAAa,MAAM,CAAC,GAAG,WAAW;AAAA,EACtC;AACJ;AACA,SAAS,aAAa,MAAM,aAAa;AAErC,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC,kBAAY;AAAA,QAAO;AAAA;AAAA,MAAmC;AACtD;AAAA,IACJ,KAAK;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC;AAAA,IACJ,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,mBAAa,OAAO,KAAK,WAAW;AACpC,kBAAY;AAAA,QAAO;AAAA;AAAA,MAAmC;AACtD,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA+B;AAClD;AAAA,IACJ;AAAA,IACA,KAAK;AACD,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA6C;AAChE,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA+B;AAClD;AAAA,IACJ,KAAK;AACD,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA6C;AAChE,kBAAY;AAAA,QAAO;AAAA;AAAA,MAAiC;AACpD;AAAA,EACR;AAEJ;AAEA,SAAS,UAAU,KAAK,UAAU,CAAC,GACjC;AACE,QAAM,cAAc,kBAAkB,GAAG;AACzC,cAAY;AAAA,IAAO;AAAA;AAAA,EAAyC;AAE5D,MAAI,QAAQ,aAAa,IAAI,MAAM,WAAW;AAE9C,QAAM,UAAU,YAAY,QAAQ;AACpC,MAAI,UAAU,MAAM,KAAK,QAAQ,OAAO;AAC5C;AAEA,SAAS,SAAS,KAAK;AACnB,QAAM,OAAO,IAAI;AACjB,MAAI,KAAK,SAAS,GAA2B;AACzC,wBAAoB,IAAI;AAAA,EAC5B,OACK;AACD,SAAK,MAAM,QAAQ,OAAK,oBAAoB,CAAC,CAAC;AAAA,EAClD;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,SAAS;AAClC,MAAI,QAAQ,MAAM,WAAW,GAAG;AAC5B,UAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,QAAI,KAAK,SAAS,KAA0B,KAAK,SAAS,GAA2B;AACjF,cAAQ,SAAS,KAAK;AACtB,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OACK;AACD,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,UAAI,EAAE,KAAK,SAAS,KAA0B,KAAK,SAAS,IAA4B;AACpF;AAAA,MACJ;AACA,UAAI,KAAK,SAAS,MAAM;AACpB;AAAA,MACJ;AACA,aAAO,KAAK,KAAK,KAAK;AAAA,IAC1B;AACA,QAAI,OAAO,WAAW,QAAQ,MAAM,QAAQ;AACxC,cAAQ,SAASL,MAAK,MAAM;AAC5B,eAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC3C,cAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,YAAI,KAAK,SAAS,KAA0B,KAAK,SAAS,GAA2B;AACjF,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB;AAEvB,SAAS,OAAO,MAAM;AAClB,OAAK,IAAI,KAAK;AACd,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK,GAA4B;AAC7B,YAAM,WAAW;AACjB,aAAO,SAAS,IAAI;AACpB,eAAS,IAAI,SAAS;AACtB,aAAO,SAAS;AAChB;AAAA,IACJ;AAAA,IACA,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,YAAM,QAAQ,OAAO;AACrB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAO,MAAM,CAAC,CAAC;AAAA,MACnB;AACA,aAAO,IAAI;AACX,aAAO,OAAO;AACd;AAAA,IACJ;AAAA,IACA,KAAK,GAA2B;AAC5B,YAAM,UAAU;AAChB,YAAM,QAAQ,QAAQ;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAO,MAAM,CAAC,CAAC;AAAA,MACnB;AACA,cAAQ,IAAI;AACZ,aAAO,QAAQ;AACf,UAAI,QAAQ,QAAQ;AAChB,gBAAQ,IAAI,QAAQ;AACpB,eAAO,QAAQ;AAAA,MACnB;AACA;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,GAA6B;AAC9B,YAAM,YAAY;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,IAAI,UAAU;AACxB,eAAO,UAAU;AAAA,MACrB;AACA;AAAA,IACJ;AAAA,IACA,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,aAAO,OAAO,GAAG;AACjB,aAAO,IAAI,OAAO;AAClB,aAAO,OAAO;AACd,UAAI,OAAO,UAAU;AACjB,eAAO,OAAO,QAAQ;AACtB,eAAO,IAAI,OAAO;AAClB,eAAO,OAAO;AAAA,MAClB;AACA;AAAA,IACJ;AAAA,IACA,KAAK,GAAwB;AACzB,YAAM,OAAO;AACb,WAAK,IAAI,KAAK;AACd,aAAO,KAAK;AACZ;AAAA,IACJ;AAAA,IACA,KAAK,GAAyB;AAC1B,YAAM,QAAQ;AACd,YAAM,IAAI,MAAM;AAChB,aAAO,MAAM;AACb;AAAA,IACJ;AAAA,IACA,SACI;AACI,YAAM,mBAAmB,kBAAkB,8BAA8B,MAAM;AAAA,QAC3E,QAAQ;AAAA,QACR,MAAM,CAAC,KAAK,IAAI;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACR;AACA,SAAO,KAAK;AAChB;AAKA,IAAM,eAAe;AACrB,SAAS,oBAAoB,KAAK,SAAS;AACvC,QAAM,EAAE,WAAW,UAAU,eAAe,YAAY,YAAY,IAAI;AACxE,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,WAAW;AAAA,IACb;AAAA,IACA,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACA,MAAI,YAAY,IAAI,KAAK;AACrB,aAAS,SAAS,IAAI,IAAI;AAAA,EAC9B;AACA,QAAM,UAAU,MAAM;AACtB,WAAS,KAAKC,OAAM,MAAM;AACtB,aAAS,QAAQA;AAAA,EACrB;AACA,WAAS,SAAS,GAAG,gBAAgB,MAAM;AACvC,UAAM,iBAAiB,gBAAgB,gBAAgB;AACvD,SAAK,cAAc,iBAAiB,KAAK,OAAO,CAAC,IAAI,cAAc;AAAA,EACvE;AACA,WAAS,OAAO,cAAc,MAAM;AAChC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EACjC;AACA,WAAS,SAAS,cAAc,MAAM;AAClC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EACjC;AACA,WAAS,UAAU;AACf,aAAS,SAAS,WAAW;AAAA,EACjC;AACA,QAAM,SAAS,CAAC,QAAQ,IAAI,GAAG;AAC/B,QAAM,aAAa,MAAM,SAAS;AAClC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,OAAO,IAAI;AACnB,YAAU,KAAK,GAAG;AAAA,IAAO;AAAA;AAAA,EAAmC,CAAC,GAAG;AAChE,eAAa,WAAW,KAAK,GAAG;AAChC,MAAI,KAAK,UAAU;AACf,cAAU,KAAK,IAAI;AACnB,iBAAa,WAAW,KAAK,QAAQ;AACrC,cAAU,KAAK,SAAS;AAAA,EAC5B,OACK;AACD,cAAU,KAAK,oBAAoB;AAAA,EACvC;AACA,YAAU,KAAK,GAAG;AACtB;AACA,SAAS,oBAAoB,WAAW,MAAM;AAC1C,QAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,YAAU,KAAK,GAAG;AAAA,IAAO;AAAA;AAAA,EAAyC,CAAC,IAAI;AACvE,YAAU,OAAO,WAAW,CAAC;AAC7B,QAAM,SAAS,KAAK,MAAM;AAC1B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,iBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,QAAI,MAAM,SAAS,GAAG;AAClB;AAAA,IACJ;AACA,cAAU,KAAK,IAAI;AAAA,EACvB;AACA,YAAU,SAAS,WAAW,CAAC;AAC/B,YAAU,KAAK,IAAI;AACvB;AACA,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,MAAI,KAAK,MAAM,SAAS,GAAG;AACvB,cAAU,KAAK,GAAG;AAAA,MAAO;AAAA;AAAA,IAAmC,CAAC,IAAI;AACjE,cAAU,OAAO,WAAW,CAAC;AAC7B,UAAM,SAAS,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,mBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,UAAI,MAAM,SAAS,GAAG;AAClB;AAAA,MACJ;AACA,gBAAU,KAAK,IAAI;AAAA,IACvB;AACA,cAAU,SAAS,WAAW,CAAC;AAC/B,cAAU,KAAK,IAAI;AAAA,EACvB;AACJ;AACA,SAAS,iBAAiB,WAAW,MAAM;AACvC,MAAI,KAAK,MAAM;AACX,iBAAa,WAAW,KAAK,IAAI;AAAA,EACrC,OACK;AACD,cAAU,KAAK,MAAM;AAAA,EACzB;AACJ;AACA,SAAS,aAAa,WAAW,MAAM;AACnC,QAAM,EAAE,OAAO,IAAI;AACnB,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,uBAAiB,WAAW,IAAI;AAChC;AAAA,IACJ,KAAK;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,IACJ,KAAK;AACD,0BAAoB,WAAW,IAAI;AACnC;AAAA,IACJ,KAAK;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAA6C,CAAC,IAAI;AAAA,QAAO;AAAA;AAAA,MAA+B,CAAC,IAAI,KAAK,KAAK,MAAM,IAAI;AAC1I;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAA6C,CAAC,IAAI;AAAA,QAAO;AAAA;AAAA,MAAiC,CAAC,IAAI,KAAK,UAAU,KAAK,GAAG,CAAC,MAAM,IAAI;AAC1J;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,SACI;AACI,YAAM,mBAAmB,kBAAkB,6BAA6B,MAAM;AAAA,QAC1E,QAAQ;AAAA,QACR,MAAM,CAAC,KAAK,IAAI;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACR;AACJ;AAEA,IAAM,WAAW,CAAC,KAAK,UAAU,CAAC,MAC7B;AACD,QAAM,OAAOF,UAAS,QAAQ,IAAI,IAAI,QAAQ,OAAO;AACrD,QAAM,WAAWA,UAAS,QAAQ,QAAQ,IACpC,QAAQ,WACR;AACN,QAAM,YAAY,CAAC,CAAC,QAAQ;AAE5B,QAAM,gBAAgB,QAAQ,iBAAiB,OACzC,QAAQ,gBACR,SAAS,UACL,MACA;AACV,QAAM,aAAa,QAAQ,aAAa,QAAQ,aAAa,SAAS;AACtE,QAAM,UAAU,IAAI,WAAW,CAAC;AAChC,QAAM,YAAY,oBAAoB,KAAK;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,YAAU,KAAK,SAAS,WAAW,6BAA6B,YAAY;AAC5E,YAAU,OAAO,UAAU;AAC3B,MAAI,QAAQ,SAAS,GAAG;AACpB,cAAU,KAAK,WAAWC,MAAK,QAAQ,IAAI,OAAK,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU;AAC/E,cAAU,QAAQ;AAAA,EACtB;AACA,YAAU,KAAK,SAAS;AACxB,eAAa,WAAW,GAAG;AAC3B,YAAU,SAAS,UAAU;AAC7B,YAAU,KAAK,GAAG;AAClB,SAAO,IAAI;AACX,QAAM,EAAE,MAAAC,OAAM,IAAI,IAAI,UAAU,QAAQ;AACxC,SAAO;AAAA,IACH;AAAA,IACA,MAAAA;AAAA,IACA,KAAK,MAAM,IAAI,OAAO,IAAI;AAAA;AAAA,EAC9B;AACJ;AAEA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACvC,QAAM,kBAAkBH,QAAO,CAAC,GAAG,OAAO;AAC1C,QAAM,MAAM,CAAC,CAAC,gBAAgB;AAC9B,QAAM,eAAe,CAAC,CAAC,gBAAgB;AACvC,QAAM,iBAAiB,gBAAgB,YAAY,OAAO,OAAO,gBAAgB;AAEjF,QAAM,SAAS,aAAa,eAAe;AAC3C,QAAM,MAAM,OAAO,MAAM,MAAM;AAC/B,MAAI,CAAC,KAAK;AAEN,cAAU,KAAK,eAAe;AAE9B,WAAO,SAAS,KAAK,eAAe;AAAA,EACxC,OACK;AAED,sBAAkB,SAAS,GAAG;AAE9B,oBAAgB,OAAO,GAAG;AAE1B,WAAO,EAAE,KAAK,MAAM,GAAG;AAAA,EAC3B;AACJ;;;ACzkDA,SAAS,mBAAmB;AACxB,MAAI,OAAO,8BAA8B,WAAW;AAChD,kBAAc,EAAE,4BAA4B;AAAA,EAChD;AACA,MAAI,OAAO,gCAAgC,WAAW;AAClD,kBAAc,EAAE,8BAA8B;AAAA,EAClD;AACA,MAAI,OAAO,sCAAsC,WAAW;AACxD,kBAAc,EAAE,oCAAoC;AAAA,EACxD;AACJ;AAEA,SAAS,aAAa,KAAK;AACvB,SAAQ,SAAS,GAAG,KAChB,YAAY,GAAG,MAAM,MACpB,OAAO,KAAK,GAAG,KAAK,OAAO,KAAK,MAAM;AAC/C;AACA,IAAM,aAAa,CAAC,KAAK,MAAM;AAC/B,SAAS,YAAY,MAAM;AACvB,SAAO,aAAa,MAAM,UAAU;AACxC;AACA,IAAM,cAAc,CAAC,KAAK,OAAO;AACjC,SAAS,aAAa,MAAM;AACxB,SAAO,aAAa,MAAM,aAAa,CAAC,CAAC;AAC7C;AACA,IAAM,eAAe,CAAC,KAAK,QAAQ;AACnC,SAAS,cAAc,MAAM;AACzB,SAAO,aAAa,MAAM,YAAY;AAC1C;AACA,IAAM,cAAc,CAAC,KAAK,OAAO;AACjC,SAAS,aAAa,MAAM;AACxB,SAAO,aAAa,MAAM,aAAa,CAAC,CAAC;AAC7C;AACA,IAAM,aAAa,CAAC,KAAK,MAAM;AAC/B,SAAS,YAAY,MAAM;AACvB,SAAO,aAAa,MAAM,UAAU;AACxC;AACA,IAAM,cAAc,CAAC,KAAK,OAAO;AACjC,SAAS,eAAe,MAAM,MAAM;AAChC,QAAM,WAAW,aAAa,MAAM,WAAW;AAC/C,MAAI,YAAY,MAAM;AAClB,WAAO;AAAA,EACX,OACK;AACD,UAAM,wBAAwB,IAAI;AAAA,EACtC;AACJ;AACA,IAAM,iBAAiB,CAAC,KAAK,UAAU;AACvC,SAAS,sBAAsB,MAAM;AACjC,SAAO,aAAa,MAAM,cAAc;AAC5C;AACA,IAAM,YAAY,CAAC,KAAK,KAAK;AAC7B,SAAS,iBAAiB,MAAM;AAC5B,QAAM,WAAW,aAAa,MAAM,SAAS;AAC7C,MAAI,UAAU;AACV,WAAO;AAAA,EACX,OACK;AACD,UAAM;AAAA,MAAwB;AAAA;AAAA,IAAwB;AAAA,EAC1D;AACJ;AACA,SAAS,aAAa,MAAM,OAAO,cAAc;AAC7C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAM,OAAO,MAAM,CAAC;AAEpB,QAAI,OAAO,MAAM,IAAI,KAAK,KAAK,IAAI,KAAK,MAAM;AAE1C,aAAO,KAAK,IAAI;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,sBAAsB;AAAA,EACxB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AAAA,EACH,GAAG;AACP;AACA,SAAS,wBAAwB,MAAM;AACnC,SAAO,IAAI,MAAM,wBAAwB,IAAI,EAAE;AACnD;AAEA,IAAM,mBAAoB,CAAC;AAC3B;AAAA,EAAiB;AAAA;AAA0B,IAAI;AAAA,EAC3C;AAAA,IAAC;AAAA;AAAA,EAAiC,GAAG;AAAA,IAAC;AAAA;AAAA,EAA0B;AAAA,EAChE;AAAA,IAAC;AAAA;AAAA,EAA6B,GAAG;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAG;AAAA,IAAC;AAAA;AAAA,EAA0B;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAmC,GAAG;AAAA,IAAC;AAAA;AAAA,EAAyB;AACrE;AACA;AAAA,EAAiB;AAAA;AAAsB,IAAI;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAAiC,GAAG;AAAA,IAAC;AAAA;AAAA,EAAsB;AAAA,EAC5D;AAAA,IAAC;AAAA;AAAA,EAA2B,GAAG;AAAA,IAAC;AAAA;AAAA,EAA2B;AAAA,EAC3D;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAG;AAAA,IAAC;AAAA;AAAA,EAA0B;AAAA,EACnE;AAAA,IAAC;AAAA;AAAA,EAAmC,GAAG;AAAA,IAAC;AAAA;AAAA,EAAyB;AACrE;AACA;AAAA,EAAiB;AAAA;AAA2B,IAAI;AAAA,EAC5C;AAAA,IAAC;AAAA;AAAA,EAAiC,GAAG;AAAA,IAAC;AAAA;AAAA,EAA2B;AAAA,EACjE;AAAA,IAAC;AAAA;AAAA,EAA6B,GAAG;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAA4B,GAAG;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AACpF;AACA;AAAA,EAAiB;AAAA;AAAuB,IAAI;AAAA,EACxC;AAAA,IAAC;AAAA;AAAA,EAA6B,GAAG;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAA4B,GAAG;AAAA,IAAC;AAAA,IAAyB;AAAA;AAAA,EAAsB;AAAA,EAChF;AAAA,IAAC;AAAA;AAAA,EAAiC,GAAG;AAAA,IAAC;AAAA,IAAwB;AAAA;AAAA,EAAoB;AAAA,EAClF;AAAA,IAAC;AAAA;AAAA,EAA2B,GAAG;AAAA,IAAC;AAAA,IAA6B;AAAA;AAAA,EAAoB;AAAA,EACjF;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAG;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAoB;AAAA,EACzF;AAAA,IAAC;AAAA;AAAA,EAAmC,GAAG;AAAA,IAAC;AAAA,IAA2B;AAAA;AAAA,EAAoB;AAC3F;AACA;AAAA,EAAiB;AAAA;AAA0B,IAAI;AAAA,EAC3C;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAG;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAAA,EAC/F;AAAA,IAAC;AAAA;AAAA,EAAqC,GAAG;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAAA,EAChG;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAG;AAAA,IACpC;AAAA,IACA;AAAA;AAAA,EACJ;AAAA,EACA;AAAA,IAAC;AAAA;AAAA,EAAqC,GAAG;AAAA,IAAC;AAAA,IAAwB;AAAA;AAAA,EAA6B;AAAA,EAC/F;AAAA,IAAC;AAAA;AAAA,EAAmC,GAAG;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAA4B,GAAG;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAsB;AACvF;AACA;AAAA,EAAiB;AAAA;AAA8B,IAAI;AAAA,EAC/C;AAAA,IAAC;AAAA;AAAA,EAAoC,GAAG;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAsB;AAAA,EAC3F;AAAA,IAAC;AAAA;AAAA,EAAmC,GAAG;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAA4B,GAAG;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAC3F;AACA;AAAA,EAAiB;AAAA;AAA8B,IAAI;AAAA,EAC/C;AAAA,IAAC;AAAA;AAAA,EAAqC,GAAG;AAAA,IAAC;AAAA,IAA4B;AAAA;AAAA,EAAsB;AAAA,EAC5F;AAAA,IAAC;AAAA;AAAA,EAAmC,GAAG;AAAA,EACvC;AAAA,IAAC;AAAA;AAAA,EAA4B,GAAG;AAAA,IAAC;AAAA,IAAgC;AAAA;AAAA,EAAsB;AAC3F;AAIA,IAAM,iBAAiB;AACvB,SAAS,UAAU,KAAK;AACpB,SAAO,eAAe,KAAK,GAAG;AAClC;AAIA,SAAS,YAAY,KAAK;AACtB,QAAM,IAAI,IAAI,WAAW,CAAC;AAC1B,QAAM,IAAI,IAAI,WAAW,IAAI,SAAS,CAAC;AACvC,SAAO,MAAM,MAAM,MAAM,MAAQ,MAAM,MAAQ,IAAI,MAAM,GAAG,EAAE,IAAI;AACtE;AAIA,SAAS,gBAAgB,IAAI;AACzB,MAAI,OAAO,UAAa,OAAO,MAAM;AACjC,WAAO;AAAA,EACX;AACA,QAAMQ,QAAO,GAAG,WAAW,CAAC;AAC5B,UAAQA,OAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAMA,SAAS,cAAc,MAAM;AACzB,QAAM,UAAU,KAAK,KAAK;AAE1B,MAAI,KAAK,OAAO,CAAC,MAAM,OAAO,MAAM,SAAS,IAAI,CAAC,GAAG;AACjD,WAAO;AAAA,EACX;AACA,SAAO,UAAU,OAAO,IAClB,YAAY,OAAO,IACnB,MAAmC;AAC7C;AAIA,SAAS,MAAM,MAAM;AACjB,QAAM,OAAO,CAAC;AACd,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI,eAAe;AACnB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,CAAC;AACjB;AAAA,IAAQ;AAAA;AAAA,EAAsB,IAAI,MAAM;AACpC,QAAI,QAAQ,QAAW;AACnB,YAAM;AAAA,IACV,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACA;AAAA,IAAQ;AAAA;AAAA,EAAoB,IAAI,MAAM;AAClC,QAAI,QAAQ,QAAW;AACnB,WAAK,KAAK,GAAG;AACb,YAAM;AAAA,IACV;AAAA,EACJ;AACA;AAAA,IAAQ;AAAA;AAAA,EAAkC,IAAI,MAAM;AAChD;AAAA,MAAQ;AAAA;AAAA,IAAsB,EAAE;AAChC;AAAA,EACJ;AACA;AAAA,IAAQ;AAAA;AAAA,EAA6B,IAAI,MAAM;AAC3C,QAAI,eAAe,GAAG;AAClB;AACA,aAAO;AACP;AAAA,QAAQ;AAAA;AAAA,MAAsB,EAAE;AAAA,IACpC,OACK;AACD,qBAAe;AACf,UAAI,QAAQ,QAAW;AACnB,eAAO;AAAA,MACX;AACA,YAAM,cAAc,GAAG;AACvB,UAAI,QAAQ,OAAO;AACf,eAAO;AAAA,MACX,OACK;AACD;AAAA,UAAQ;AAAA;AAAA,QAAoB,EAAE;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,qBAAqB;AAC1B,UAAM,WAAW,KAAK,QAAQ,CAAC;AAC/B,QAAK,SAAS,KACV,aAAa,OACZ,SAAS,KACN,aAAa,KAAwC;AACzD;AACA,gBAAU,OAAO;AACjB;AAAA,QAAQ;AAAA;AAAA,MAAsB,EAAE;AAChC,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO,SAAS,MAAM;AAClB;AACA,QAAI,KAAK,KAAK;AACd,QAAI,MAAM,QAAQ,mBAAmB,GAAG;AACpC;AAAA,IACJ;AACA,WAAO,gBAAgB,CAAC;AACxB,cAAU,iBAAiB,IAAI;AAC/B,iBAAa,QAAQ,IAAI,KAAK;AAAA,MAAQ;AAAA;AAAA,IAA4B,KAAK;AAEvE,QAAI,eAAe,GAAsB;AACrC;AAAA,IACJ;AACA,WAAO,WAAW,CAAC;AACnB,QAAI,WAAW,CAAC,MAAM,QAAW;AAC7B,eAAS,QAAQ,WAAW,CAAC,CAAC;AAC9B,UAAI,QAAQ;AACR,kBAAU;AACV,YAAI,OAAO,MAAM,OAAO;AACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,SAAS,GAA2B;AACpC,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAEA,IAAM,QAAQ,oBAAI,IAAI;AActB,SAAS,oBAAoB,KAAK,MAAM;AACpC,SAAO,SAAS,GAAG,IAAI,IAAI,IAAI,IAAI;AACvC;AAcA,SAAS,aAAa,KAAK,MAAM;AAE7B,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,WAAO;AAAA,EACX;AAEA,MAAI,MAAM,MAAM,IAAI,IAAI;AACxB,MAAI,CAAC,KAAK;AACN,UAAM,MAAM,IAAI;AAChB,QAAI,KAAK;AACL,YAAM,IAAI,MAAM,GAAG;AAAA,IACvB;AAAA,EACJ;AAEA,MAAI,CAAC,KAAK;AACN,WAAO;AAAA,EACX;AAEA,QAAM,MAAM,IAAI;AAChB,MAAI,OAAO;AACX,MAAI,IAAI;AACR,SAAO,IAAI,KAAK;AACZ,UAAM,MAAM,IAAI,CAAC;AAMjB,QAAI,oBAAoB,SAAS,GAAG,KAAK,aAAa,IAAI,GAAG;AACzD,aAAO;AAAA,IACX;AACA,UAAM,MAAM,KAAK,GAAG;AACpB,QAAI,QAAQ,QAAW;AACnB,aAAO;AAAA,IACX;AACA,QAAI,WAAW,IAAI,GAAG;AAClB,aAAO;AAAA,IACX;AACA,WAAO;AACP;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,mBAAmB,CAAC,QAAQ;AAClC,IAAM,kBAAkB,CAAC,QAAQ;AACjC,IAAM,4BAA4B;AAClC,IAAM,oBAAoB,CAAC,WAAW,OAAO,WAAW,IAAI,KAAK,KAAK,MAAM;AAC5E,IAAM,sBAAsB;AAC5B,SAAS,cAAc,QAAQ,eAAe;AAC1C,WAAS,KAAK,IAAI,MAAM;AACxB,MAAI,kBAAkB,GAAG;AAErB,WAAO,SACD,SAAS,IACL,IACA,IACJ;AAAA,EACV;AACA,SAAO,SAAS,KAAK,IAAI,QAAQ,CAAC,IAAI;AAC1C;AACA,SAAS,eAAe,SAAS;AAE7B,QAAM,QAAQ,SAAS,QAAQ,WAAW,IACpC,QAAQ,cACR;AAEN,SAAO,QAAQ,UAAU,SAAS,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,MAAM,CAAC,KAC5E,SAAS,QAAQ,MAAM,KAAK,IACxB,QAAQ,MAAM,QACd,SAAS,QAAQ,MAAM,CAAC,IACpB,QAAQ,MAAM,IACd,QACR;AACV;AACA,SAAS,eAAe,aAAa,OAAO;AACxC,MAAI,CAAC,MAAM,OAAO;AACd,UAAM,QAAQ;AAAA,EAClB;AACA,MAAI,CAAC,MAAM,GAAG;AACV,UAAM,IAAI;AAAA,EACd;AACJ;AACA,SAAS,qBAAqB,UAAU,CAAC,GAAG;AACxC,QAAM,SAAS,QAAQ;AACvB,QAAM,cAAc,eAAe,OAAO;AAC1C,QAAM,aAAa,SAAS,QAAQ,WAAW,KAC3C,SAAS,MAAM,KACf,WAAW,QAAQ,YAAY,MAAM,CAAC,IACpC,QAAQ,YAAY,MAAM,IAC1B;AACN,QAAM,gBAAgB,SAAS,QAAQ,WAAW,KAC9C,SAAS,MAAM,KACf,WAAW,QAAQ,YAAY,MAAM,CAAC,IACpC,gBACA;AACN,QAAM,SAAS,CAAC,aAAa;AACzB,WAAO,SAAS,WAAW,aAAa,SAAS,QAAQ,aAAa,CAAC;AAAA,EAC3E;AACA,QAAM,QAAQ,QAAQ,QAAQ,CAAC;AAC/B,QAAM,OAAO,CAAC,UAAU,MAAM,KAAK;AAEnC,QAAM,SAAS,QAAQ,SAAS,OAAO;AACvC,WAAS,QAAQ,WAAW,KAAK,eAAe,aAAa,MAAM;AACnE,QAAM,QAAQ,CAAC,QAAQ,OAAO,GAAG;AACjC,WAAS,QAAQ,KAAK;AAElB,UAAM,MAAM,WAAW,QAAQ,QAAQ,IACjC,QAAQ,SAAS,GAAG,IACpB,SAAS,QAAQ,QAAQ,IACrB,QAAQ,SAAS,GAAG,IACpB;AACV,WAAO,CAAC,MACF,QAAQ,SACJ,QAAQ,OAAO,QAAQ,GAAG,IAC1B,kBACJ;AAAA,EACV;AACA,QAAM,YAAY,CAAC,SAAS,QAAQ,YAC9B,QAAQ,UAAU,IAAI,IACtB;AACN,QAAM,YAAY,cAAc,QAAQ,SAAS,KAAK,WAAW,QAAQ,UAAU,SAAS,IACtF,QAAQ,UAAU,YAClB;AACN,QAAM,cAAc,cAAc,QAAQ,SAAS,KAC/C,WAAW,QAAQ,UAAU,WAAW,IACtC,QAAQ,UAAU,cAClB;AACN,QAAM,OAAO,cAAc,QAAQ,SAAS,KAAK,SAAS,QAAQ,UAAU,IAAI,IAC1E,QAAQ,UAAU,OAClB;AACN,QAAM,SAAS,CAAC,QAAQ,SAAS;AAC7B,UAAM,CAAC,MAAM,IAAI,IAAI;AACrB,QAAIC,QAAO;AACX,QAAI,WAAW;AACf,QAAI,KAAK,WAAW,GAAG;AACnB,UAAI,SAAS,IAAI,GAAG;AAChB,mBAAW,KAAK,YAAY;AAC5B,QAAAA,QAAO,KAAK,QAAQA;AAAA,MACxB,WACS,SAAS,IAAI,GAAG;AACrB,mBAAW,QAAQ;AAAA,MACvB;AAAA,IACJ,WACS,KAAK,WAAW,GAAG;AACxB,UAAI,SAAS,IAAI,GAAG;AAChB,mBAAW,QAAQ;AAAA,MACvB;AACA,UAAI,SAAS,IAAI,GAAG;AAChB,QAAAA,QAAO,QAAQA;AAAA,MACnB;AAAA,IACJ;AACA,UAAM,MAAM,QAAQ,GAAG,EAAE,GAAG;AAC5B,UAAM;AAAA;AAAA,MAENA,UAAS,WAAW,QAAQ,GAAG,KAAK,WAC9B,IAAI,CAAC,IACL;AAAA;AACN,WAAO,WAAW,UAAU,QAAQ,EAAE,KAAKA,KAAI,IAAI;AAAA,EACvD;AACA,QAAM,MAAM;AAAA,IACR;AAAA,MAAC;AAAA;AAAA,IAA+B,GAAG;AAAA,IACnC;AAAA,MAAC;AAAA;AAAA,IAAiC,GAAG;AAAA,IACrC;AAAA,MAAC;AAAA;AAAA,IAAmC,GAAG;AAAA,IACvC;AAAA,MAAC;AAAA;AAAA,IAAmC,GAAG;AAAA,IACvC;AAAA,MAAC;AAAA;AAAA,IAAqC,GAAG;AAAA,IACzC;AAAA,MAAC;AAAA;AAAA,IAA+B,GAAG;AAAA,IACnC;AAAA,MAAC;AAAA;AAAA,IAA6C,GAAG;AAAA,IACjD;AAAA,MAAC;AAAA;AAAA,IAAyC,GAAG;AAAA,IAC7C;AAAA,MAAC;AAAA;AAAA,IAAmC,GAAG,OAAO,OAAO,GAAG,OAAO,MAAM;AAAA,EACzE;AACA,SAAO;AACX;AAEA,IAAI,WAAW;AACf,SAAS,gBAAgB,MAAM;AAC3B,aAAW;AACf;AAIA,SAAS,iBAAiB,MAAM,SAAS,MAAM;AAE3C,cACI,SAAS,KAAK,aAAiD;AAAA,IAC3D,WAAW,KAAK,IAAI;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACT;AACA,IAAM,oBAAmC;AAAA,EAAmB;AAAA;AAAiE;AAC7H,SAAS,mBAAmB,MAAM;AAC9B,SAAO,CAAC,aAAa,YAAY,SAAS,KAAK,MAAM,QAAQ;AACjE;AAEA,IAAM,SAAS,iBAAiB;AAChC,IAAM,QAAQ,YAAY,MAAM;AAChC,IAAM,gBAAgB;AAAA,EAClB,eAAe;AAAA;AAAA,EACf,uBAAuB,MAAM;AAAA;AAAA,EAC7B,sBAAsB,MAAM;AAAA;AAAA,EAC5B,2BAA2B,MAAM;AAAA;AAAA,EACjC,oBAAoB,MAAM;AAAA;AAAA,EAC1B,yBAAyB,MAAM;AAAA;AAAA,EAC/B,sCAAsC,MAAM;AAAA;AAAA,EAC5C,kBAAkB,MAAM;AAAA;AAC5B;AAEA,IAAMC,gBAAe;AAAA,EACjB,CAAC,cAAc,aAAa,GAAG;AAAA,EAC/B,CAAC,cAAc,qBAAqB,GAAG;AAAA,EACvC,CAAC,cAAc,oBAAoB,GAAG;AAAA,EACtC,CAAC,cAAc,yBAAyB,GAAG;AAAA,EAC3C,CAAC,cAAc,kBAAkB,GAAG;AAAA,EACpC,CAAC,cAAc,uBAAuB,GAAG;AAAA,EACzC,CAAC,cAAc,oCAAoC,GAAG;AAC1D;AACA,SAAS,eAAeC,UAAS,MAAM;AACnC,SAAO,OAASD,cAAaC,KAAI,GAAG,GAAG,IAAI;AAC/C;AAEA,IAAM,OAAO,kBAAkB;AAC/B,IAAM,MAAM,YAAY,IAAI;AAC5B,IAAM,iBAAiB;AAAA,EACnB,kBAAkB;AAAA;AAAA,EAClB,uBAAuB,IAAI;AAAA;AAAA,EAC3B,2BAA2B,IAAI;AAAA;AAAA,EAC/B,gCAAgC,IAAI;AAAA;AAAA,EACpC,kCAAkC,IAAI;AAAA;AAAA,EACtC,mCAAmC,IAAI;AAAA;AAAA,EACvC,yBAAyB,IAAI;AAAA;AAAA,EAC7B,kBAAkB,IAAI;AAAA;AAC1B;AACA,SAAS,gBAAgBA,OAAM;AAC3B,SAAO,mBAAmBA,OAAM,MAAO,OAAyC,EAAE,UAAUC,eAAc,IAAI,MAAS;AAC3H;AAEA,IAAMA,iBAAgB;AAAA,EAClB,CAAC,eAAe,gBAAgB,GAAG;AAAA,EACnC,CAAC,eAAe,qBAAqB,GAAG;AAAA,EAExC,CAAC,eAAe,yBAAyB,GAAG;AAAA,EAC5C,CAAC,eAAe,8BAA8B,GAAG;AAAA,EACjD,CAAC,eAAe,gCAAgC,GAAG;AAAA,EACnD,CAAC,eAAe,iCAAiC,GAAG;AAAA,EACpD,CAAC,eAAe,uBAAuB,GAAG;AAC9C;AAGA,SAAS,UAAU,SAAS,SAAS;AACjC,SAAO,QAAQ,UAAU,OACnB,cAAc,QAAQ,MAAM,IAC5B,cAAc,QAAQ,MAAM;AACtC;AACA,IAAI;AAEJ,SAAS,cAAc,QAAQ;AAC3B,MAAI,SAAS,MAAM,GAAG;AAClB,WAAO;AAAA,EACX,OACK;AACD,QAAI,WAAW,MAAM,GAAG;AACpB,UAAI,OAAO,gBAAgB,kBAAkB,MAAM;AAC/C,eAAO;AAAA,MACX,WACS,OAAO,YAAY,SAAS,YAAY;AAC7C,cAAM,UAAU,OAAO;AACvB,YAAI,UAAU,OAAO,GAAG;AACpB,gBAAM,gBAAgB,eAAe,gCAAgC;AAAA,QACzE;AACA,eAAQ,iBAAiB;AAAA,MAC7B,OACK;AACD,cAAM,gBAAgB,eAAe,iCAAiC;AAAA,MAC1E;AAAA,IACJ,OACK;AACD,YAAM,gBAAgB,eAAe,uBAAuB;AAAA,IAChE;AAAA,EACJ;AACJ;AAiBA,SAAS,mBAAmB,KAAK,UAAU,OACzC;AAEE,SAAO,CAAC,GAAG,oBAAI,IAAI;AAAA,IACX;AAAA,IACA,GAAI,QAAQ,QAAQ,IACd,WACA,SAAS,QAAQ,IACb,OAAO,KAAK,QAAQ,IACpB,SAAS,QAAQ,IACb,CAAC,QAAQ,IACT,CAAC,KAAK;AAAA,EACxB,CAAC,CAAC;AACV;AAiBA,SAAS,wBAAwB,KAAK,UAAU,OAAO;AACnD,QAAM,cAAc,SAAS,KAAK,IAAI,QAAQ;AAC9C,QAAM,UAAU;AAChB,MAAI,CAAC,QAAQ,oBAAoB;AAC7B,YAAQ,qBAAqB,oBAAI,IAAI;AAAA,EACzC;AACA,MAAI,QAAQ,QAAQ,mBAAmB,IAAI,WAAW;AACtD,MAAI,CAAC,OAAO;AACR,YAAQ,CAAC;AAET,QAAI,QAAQ,CAAC,KAAK;AAElB,WAAO,QAAQ,KAAK,GAAG;AACnB,cAAQ,mBAAmB,OAAO,OAAO,QAAQ;AAAA,IACrD;AAGA,UAAM,WAAW,QAAQ,QAAQ,KAAK,CAAC,cAAc,QAAQ,IACvD,WACA,SAAS,SAAS,IACd,SAAS,SAAS,IAClB;AAEV,YAAQ,SAAS,QAAQ,IAAI,CAAC,QAAQ,IAAI;AAC1C,QAAI,QAAQ,KAAK,GAAG;AAChB,yBAAmB,OAAO,OAAO,KAAK;AAAA,IAC1C;AACA,YAAQ,mBAAmB,IAAI,aAAa,KAAK;AAAA,EACrD;AACA,SAAO;AACX;AACA,SAAS,mBAAmB,OAAO,OAAO,QAAQ;AAC9C,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,UAAU,UAAU,MAAM,GAAG,KAAK;AACxD,UAAM,SAAS,MAAM,CAAC;AACtB,QAAI,SAAS,MAAM,GAAG;AAClB,eAAS,oBAAoB,OAAO,MAAM,CAAC,GAAG,MAAM;AAAA,IACxD;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,OAAO,QAAQ,QAAQ;AAChD,MAAI;AACJ,QAAM,SAAS,OAAO,MAAM,GAAG;AAC/B,KAAG;AACC,UAAM,SAAS,OAAO,KAAK,GAAG;AAC9B,aAAS,kBAAkB,OAAO,QAAQ,MAAM;AAChD,WAAO,OAAO,IAAI,CAAC;AAAA,EACvB,SAAS,OAAO,UAAU,WAAW;AACrC,SAAO;AACX;AACA,SAAS,kBAAkB,OAAO,QAAQ,QAAQ;AAC9C,MAAI,SAAS;AACb,MAAI,CAAC,MAAM,SAAS,MAAM,GAAG;AACzB,aAAS;AACT,QAAI,QAAQ;AACR,eAAS,OAAO,OAAO,SAAS,CAAC,MAAM;AACvC,YAAM,SAAS,OAAO,QAAQ,MAAM,EAAE;AACtC,YAAM,KAAK,MAAM;AACjB,WAAK,QAAQ,MAAM,KAAK,cAAc,MAAM,MACxC,OAAO,MAAM,GACf;AAEE,iBAAS,OAAO,MAAM;AAAA,MAC1B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAOA,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,iBAAiB;AACvB,IAAM,wBAAwB;AAC9B,IAAM,aAAa,CAAC,QAAQ,GAAG,IAAI,OAAO,CAAC,EAAE,kBAAkB,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC;AAChF,SAAS,4BAA4B;AACjC,SAAO;AAAA,IACH,OAAO,CAAC,KAAK,SAAS;AAElB,aAAO,SAAS,UAAU,SAAS,GAAG,IAChC,IAAI,YAAY,IAChB,SAAS,WAAW,SAAS,GAAG,KAAK,iBAAiB,MAClD,IAAI,SAAS,YAAY,IACzB;AAAA,IACd;AAAA,IACA,OAAO,CAAC,KAAK,SAAS;AAElB,aAAO,SAAS,UAAU,SAAS,GAAG,IAChC,IAAI,YAAY,IAChB,SAAS,WAAW,SAAS,GAAG,KAAK,iBAAiB,MAClD,IAAI,SAAS,YAAY,IACzB;AAAA,IACd;AAAA,IACA,YAAY,CAAC,KAAK,SAAS;AAEvB,aAAQ,SAAS,UAAU,SAAS,GAAG,IACjC,WAAW,GAAG,IACd,SAAS,WAAW,SAAS,GAAG,KAAK,iBAAiB,MAClD,WAAW,IAAI,QAAQ,IACvB;AAAA,IACd;AAAA,EACJ;AACJ;AACA,IAAI;AACJ,SAAS,wBAAwB,UAAU;AACvC,cAAY;AAChB;AACA,IAAI;AAQJ,SAAS,wBAAwB,UAAU;AACvC,cAAY;AAChB;AACA,IAAI;AAQJ,SAAS,yBAAyB,YAAY;AAC1C,gBAAc;AAClB;AAEA,IAAI,kBAAmB;AAEvB,IAAM,oBAAoB,CAAC,SAAS;AAChC,oBAAkB;AACtB;AAEA,IAAM,oBAAoB,MAAM;AAChC,IAAI,mBAAmB;AACvB,IAAM,qBAAqB,CAAC,YAAY;AACpC,qBAAmB;AACvB;AACA,IAAM,qBAAqB,MAAM;AAEjC,IAAI,OAAO;AACX,SAAS,kBAAkB,UAAU,CAAC,GAAG;AAErC,QAAM,SAAS,WAAW,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAC7D,QAAM,UAAU,SAAS,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC9D,QAAM,SAAS,SAAS,QAAQ,MAAM,KAAK,WAAW,QAAQ,MAAM,IAC9D,QAAQ,SACR;AACN,QAAM,UAAU,WAAW,MAAM,IAAI,iBAAiB;AACtD,QAAM,iBAAiB,QAAQ,QAAQ,cAAc,KACjD,cAAc,QAAQ,cAAc,KACpC,SAAS,QAAQ,cAAc,KAC/B,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,QAAM,WAAW,cAAc,QAAQ,QAAQ,IACzC,QAAQ,WACR,gBAAgB,OAAO;AAC7B,QAAM,kBAAkB,cAAc,QAAQ,eAAe,IACnD,QAAQ,kBACR,gBAAgB,OAAO;AAEjC,QAAM,gBAAgB,cAAc,QAAQ,aAAa,IAC/C,QAAQ,gBACR,gBAAgB,OAAO;AAEjC,QAAM,YAAY,OAAO,OAAO,GAAG,QAAQ,WAAW,0BAA0B,CAAC;AACjF,QAAM,cAAc,QAAQ,eAAe,OAAO;AAClD,QAAM,UAAU,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAChE,QAAM,cAAc,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC5E,QAAQ,cACR;AACN,QAAM,eAAe,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC/E,QAAQ,eACR;AACN,QAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,QAAM,cAAc,CAAC,CAAC,QAAQ;AAC9B,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,QAAM,YAAY,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY;AACzE,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AACN,QAAM,kBAAkB,CAAC,CAAC,QAAQ;AAClC,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,MAGI,WAAW,QAAQ,eAAe,GAAG;AACrC,aAAS,eAAe,cAAc,oCAAoC,CAAC;AAAA,EAC/E;AACA,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR,aAAa;AACnB,QAAM,mBAAmB,WAAW,QAAQ,gBAAgB,IACtD,QAAQ,mBACR,eAAe;AACrB,QAAM,kBAAkB,SAAS,QAAQ,eAAe,IAClD,QAAQ,kBACR;AAEN,QAAM,kBAAkB;AACxB,QAAM,uBAAuB,SAAS,gBAAgB,oBAAoB,IAChE,gBAAgB,uBAChB,oBAAI,IAAI;AAElB,QAAM,qBAAqB,SAAS,gBAAgB,kBAAkB,IAC5D,gBAAgB,qBAChB,oBAAI,IAAI;AAElB,QAAM,SAAS,SAAS,gBAAgB,MAAM,IAAI,gBAAgB,SAAS,CAAC;AAC5E;AACA,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA;AACI,YAAQ,kBAAkB;AAC1B,YAAQ,gBAAgB;AACxB,YAAQ,uBAAuB;AAC/B,YAAQ,qBAAqB;AAAA,EACjC;AAEA,MAAK,MAAwC;AACzC,YAAQ,cACJ,gBAAgB,eAAe,OACzB,gBAAgB,cAChB;AAAA,EACd;AAEA,MAAK,MAAqE;AACtE,qBAAiB,SAAS,SAAS,MAAM;AAAA,EAC7C;AACA,SAAO;AACX;AACA,IAAM,kBAAkB,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,OAAO,EAAE;AAE1D,SAAS,wBAAwB,UAAU,KAAK;AAC5C,SAAO,oBAAoB,SAAS,SAAS,KAAK,GAAG,IAAI;AAC7D;AAEA,SAAS,uBAAuB,SAAS,KAAK;AAC1C,SAAO,mBAAmB,SAAS,QAAQ,KAAK,GAAG,IAAI;AAC3D;AAEA,SAAS,cAAc,SAAS,KAAK,QAAQ,aAAa,MAAM;AAC5D,QAAM,EAAE,SAAS,OAAO,IAAI;AAE5B,MAAK,MAAwC;AACzC,UAAM,UAAU,QAAQ;AACxB,QAAI,SAAS;AACT,cAAQ,KAAK,WAAmD;AAAA,QAC5D;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,MAC3B,CAAC;AAAA,IACL;AAAA,EACJ;AACA,MAAI,YAAY,MAAM;AAClB,UAAM,MAAM,QAAQ,SAAS,QAAQ,KAAK,IAAI;AAC9C,WAAO,SAAS,GAAG,IAAI,MAAM;AAAA,EACjC,OACK;AACD,QAA+C,uBAAuB,aAAa,GAAG,GAAG;AACrF,aAAO,eAAe,cAAc,eAAe,EAAE,KAAK,OAAO,CAAC,CAAC;AAAA,IACvE;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,qBAAqB,KAAK,QAAQ,UAAU;AACjD,QAAM,UAAU;AAChB,UAAQ,qBAAqB,oBAAI,IAAI;AACrC,MAAI,iBAAiB,KAAK,UAAU,MAAM;AAC9C;AAEA,SAAS,mBAAmB,QAAQ,eAAe;AAC/C,MAAI,WAAW;AACX,WAAO;AACX,SAAO,OAAO,MAAM,GAAG,EAAE,CAAC,MAAM,cAAc,MAAM,GAAG,EAAE,CAAC;AAC9D;AAEA,SAAS,mBAAmB,cAAc,SAAS;AAC/C,QAAM,QAAQ,QAAQ,QAAQ,YAAY;AAC1C,MAAI,UAAU,IAAI;AACd,WAAO;AAAA,EACX;AACA,WAAS,IAAI,QAAQ,GAAG,IAAI,QAAQ,QAAQ,KAAK;AAC7C,QAAI,mBAAmB,cAAc,QAAQ,CAAC,CAAC,GAAG;AAC9C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAGA,SAASC,QAAO,KAAK;AACjB,QAAM,MAAM,CAAC,QAAQ,YAAY,KAAK,GAAG;AACzC,SAAO;AACX;AACA,SAAS,YAAY,KAAK,KAAK;AAC3B,QAAM,OAAO,YAAY,GAAG;AAC5B,MAAI,QAAQ,MAAM;AACd,UAAM;AAAA,MAAwB;AAAA;AAAA,IAA0B;AAAA,EAC5D;AACA,QAAM,OAAO,YAAY,IAAI;AAC7B,MAAI,SAAS,GAA0B;AACnC,UAAM,SAAS;AACf,UAAM,QAAQ,aAAa,MAAM;AACjC,WAAO,IAAI,OAAO,MAAM,OAAO,CAAC,UAAU,MAAM;AAAA,MAC5C,GAAG;AAAA,MACH,mBAAmB,KAAK,CAAC;AAAA,IAC7B,GAAG,CAAC,CAAC,CAAC;AAAA,EACV,OACK;AACD,WAAO,mBAAmB,KAAK,IAAI;AAAA,EACvC;AACJ;AACA,SAAS,mBAAmB,KAAK,MAAM;AACnC,QAAM,UAAU,cAAc,IAAI;AAClC,MAAI,WAAW,MAAM;AACjB,WAAO,IAAI,SAAS,SACd,UACA,IAAI,UAAU,CAAC,OAAO,CAAC;AAAA,EACjC,OACK;AACD,UAAM,WAAW,aAAa,IAAI,EAAE,OAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,kBAAkB,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAC9F,WAAO,IAAI,UAAU,QAAQ;AAAA,EACjC;AACJ;AACA,SAAS,kBAAkB,KAAK,MAAM;AAClC,QAAM,OAAO,YAAY,IAAI;AAC7B,UAAQ,MAAM;AAAA,IACV,KAAK,GAAwB;AACzB,aAAO,eAAe,MAAM,IAAI;AAAA,IACpC;AAAA,IACA,KAAK,GAA2B;AAC5B,aAAO,eAAe,MAAM,IAAI;AAAA,IACpC;AAAA,IACA,KAAK,GAAyB;AAC1B,YAAM,QAAQ;AACd,UAAI,OAAO,OAAO,GAAG,KAAK,MAAM,GAAG;AAC/B,eAAO,IAAI,YAAY,IAAI,MAAM,MAAM,CAAC,CAAC;AAAA,MAC7C;AACA,UAAI,OAAO,OAAO,KAAK,KAAK,MAAM,KAAK;AACnC,eAAO,IAAI,YAAY,IAAI,MAAM,MAAM,GAAG,CAAC;AAAA,MAC/C;AACA,YAAM,wBAAwB,IAAI;AAAA,IACtC;AAAA,IACA,KAAK,GAAwB;AACzB,YAAM,OAAO;AACb,UAAI,OAAO,MAAM,GAAG,KAAK,SAAS,KAAK,CAAC,GAAG;AACvC,eAAO,IAAI,YAAY,IAAI,KAAK,KAAK,CAAC,CAAC;AAAA,MAC3C;AACA,UAAI,OAAO,MAAM,OAAO,KAAK,SAAS,KAAK,KAAK,GAAG;AAC/C,eAAO,IAAI,YAAY,IAAI,KAAK,KAAK,KAAK,CAAC;AAAA,MAC/C;AACA,YAAM,wBAAwB,IAAI;AAAA,IACtC;AAAA,IACA,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,YAAM,WAAW,sBAAsB,MAAM;AAC7C,YAAM,MAAM,iBAAiB,MAAM;AACnC,aAAO,IAAI,OAAO,kBAAkB,KAAK,GAAG,GAAG,WAAW,kBAAkB,KAAK,QAAQ,IAAI,QAAW,IAAI,IAAI;AAAA,IACpH;AAAA,IACA,KAAK,GAA6B;AAC9B,aAAO,eAAe,MAAM,IAAI;AAAA,IACpC;AAAA,IACA,KAAK,GAAkC;AACnC,aAAO,eAAe,MAAM,IAAI;AAAA,IACpC;AAAA,IACA;AACI,YAAM,IAAI,MAAM,0CAA0C,IAAI,EAAE;AAAA,EACxE;AACJ;AAEA,IAAM,eAAe;AACrB,SAAS,iBAAiB,QAAQ,iBAAiB;AAC/C,MAAI,mBAAmB,cAAc,MAAM,GAAG;AAC1C,SAAK,OAAS,cAAc,EAAE,OAAO,CAAC,CAAC;AAAA,EAC3C;AACJ;AACA,IAAM,oBAAoB,CAAC,YAAY;AACvC,IAAI,eAAe,OAAO;AAC1B,SAAS,cAAc,OAAO;AAC1B,MAAI,MAAM,SAAS,iBAAiB,mBAAmB;AACnD,SAAK;AAAA;AAAA,qCAGqC,MAAM,OAAO,GAAG;AAAA,EAC9D;AACJ;AAIA,SAASC,aAAY,SAAS,UAAU,CAAC,GAAG;AAExC,MAAI,cAAc;AAClB,QAAM,UAAU,QAAQ,WAAW;AACnC,UAAQ,UAAU,CAAC,QAAQ;AACvB,kBAAc;AACd,YAAQ,GAAG;AAAA,EACf;AAEA,SAAO,EAAE,GAAG,YAAc,SAAS,OAAO,GAAG,YAAY;AAC7D;AAEA,IAAM,oBAAoB,CAAC,SAAS,YAAY;AAC5C,MAAI,CAAC,SAAS,OAAO,GAAG;AACpB,UAAM,gBAAgB,eAAe,8BAA8B;AAAA,EACvE;AAEA,MAAK,MAAwC;AACzC,YAAQ,SAAS;AAAA,EACrB;AACA;AAEI,UAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AACN,IAA2C,iBAAiB,SAAS,eAAe;AAEpF,UAAM,aAAa,QAAQ,cAAc;AACzC,UAAM,WAAW,WAAW,OAAO;AACnC,UAAM,SAAS,aAAa,QAAQ;AACpC,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AAEA,UAAM,EAAE,MAAAC,OAAM,YAAY,IAAID,aAAY,SAAS,OAAO;AAE1D,UAAM,MAAM,IAAI,SAAS,UAAUC,KAAI,EAAE,EAAE;AAE3C,WAAO,CAAC,cACD,aAAa,QAAQ,IAAI,MAC1B;AAAA,EACV;AACJ;AACA,SAAS,QAAQ,SAAS,SAAS;AAE/B,MAAK,MAAwC;AACzC,YAAQ,SAAS;AAAA,EACrB;AACA,MAAM,+BAA+B,CAAC,qCAClC,SAAS,OAAO,GAAG;AAEnB,UAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR;AACN,IAA2C,iBAAiB,SAAS,eAAe;AAEpF,UAAM,aAAa,QAAQ,cAAc;AACzC,UAAM,WAAW,WAAW,OAAO;AACnC,UAAM,SAAS,aAAa,QAAQ;AACpC,QAAI,QAAQ;AACR,aAAO;AAAA,IACX;AAEA,UAAM,EAAE,KAAK,YAAY,IAAID,aAAY,SAAS;AAAA,MAC9C,GAAG;AAAA,MACH,UAAW;AAAA,MACX,KAAK;AAAA,IACT,CAAC;AAED,UAAM,MAAME,QAAO,GAAG;AAEtB,WAAO,CAAC,cACD,aAAa,QAAQ,IAAI,MAC1B;AAAA,EACV,OACK;AACD,QAA+C,CAAC,aAAa,OAAO,GAAG;AACnE,WAAK,yCAAyC,QAAQ,GAAG,wCAAwC;AACjG,aAAQ,MAAM;AAAA,IAClB;AAEA,UAAM,WAAW,QAAQ;AACzB,QAAI,UAAU;AACV,YAAM,SAAS,aAAa,QAAQ;AACpC,UAAI,QAAQ;AACR,eAAO;AAAA,MACX;AAEA,aAAQ,aAAa,QAAQ,IACzBA,QAAO,OAAO;AAAA,IACtB,OACK;AACD,aAAOA,QAAO,OAAO;AAAA,IACzB;AAAA,EACJ;AACJ;AAEA,IAAM,wBAAwB,MAAM;AACpC,IAAM,oBAAoB,CAAC,QAAQ,WAAW,GAAG;AAEjD,SAAS,UAAU,YAAY,MAAM;AACjC,QAAM,EAAE,gBAAgB,iBAAiB,aAAa,iBAAiB,gBAAgB,SAAS,IAAI;AACpG,QAAM,CAAC,KAAK,OAAO,IAAI,mBAAmB,GAAG,IAAI;AACjD,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,kBAAkB,UAAU,QAAQ,eAAe,IACnD,QAAQ,kBACR,QAAQ;AACd,QAAM,kBAAkB,CAAC,CAAC,QAAQ;AAElC,QAAM,kBAAkB,SAAS,QAAQ,OAAO,KAAK,UAAU,QAAQ,OAAO,IACxE,CAAC,UAAU,QAAQ,OAAO,IACtB,QAAQ,UACP,CAAC,kBAAkB,MAAM,MAAM,MACpC,iBACK,CAAC,kBAAkB,MAAM,MAAM,MAChC;AACV,QAAM,mBAAmB,kBAAkB,oBAAoB;AAC/D,QAAM,SAAS,UAAU,SAAS,OAAO;AAEzC,qBAAmB,aAAa,OAAO;AAGvC,MAAI,CAAC,aAAa,cAAc,OAAO,IAAI,CAAC,kBACtC,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,WAAW,IACpF;AAAA,IACE;AAAA,IACA;AAAA,IACA,SAAS,MAAM,KAAK,OAAO;AAAA,EAC/B;AAMJ,MAAIA,UAAS;AAEb,MAAI,eAAe;AACnB,MAAI,CAAC,mBACD,EAAE,SAASA,OAAM,KACb,aAAaA,OAAM,KACnB,kBAAkBA,OAAM,IAAI;AAChC,QAAI,kBAAkB;AAClB,MAAAA,UAAS;AACT,qBAAeA;AAAA,IACnB;AAAA,EACJ;AAEA,MAAI,CAAC,oBACA,EAAE,SAASA,OAAM,KACd,aAAaA,OAAM,KACnB,kBAAkBA,OAAM,MACxB,CAAC,SAAS,YAAY,IAAI;AAC9B,WAAO,cAAc,eAAe;AAAA,EACxC;AAEA,MAA+C,SAASA,OAAM,KAAK,QAAQ,mBAAmB,MAAM;AAChG,SAAK,yLAGgC,GAAG,IAAI;AAC5C,WAAO;AAAA,EACX;AAEA,MAAI,WAAW;AACf,QAAM,UAAU,MAAM;AAClB,eAAW;AAAA,EACf;AAEA,QAAM,MAAM,CAAC,kBAAkBA,OAAM,IAC/B,qBAAqB,SAAS,KAAK,cAAcA,SAAQ,cAAc,OAAO,IAC9EA;AAEN,MAAI,UAAU;AACV,WAAOA;AAAA,EACX;AAEA,QAAM,aAAa,yBAAyB,SAAS,cAAc,SAAS,OAAO;AACnF,QAAM,aAAa,qBAAqB,UAAU;AAClD,QAAM,WAAW,gBAAgB,SAAS,KAAK,UAAU;AAEzD,MAAI,MAAM,kBACJ,gBAAgB,UAAU,GAAG,IAC7B;AAEN,MAAI,mBAAmB,SAAS,GAAG,GAAG;AAClC,UAAM,uBAAuB,GAAG;AAAA,EACpC;AAEA,MAAK,MAAqE;AAEtE,UAAM,WAAW;AAAA,MACb,WAAW,KAAK,IAAI;AAAA,MACpB,KAAK,SAAS,GAAG,IACX,MACA,kBAAkBA,OAAM,IACpBA,QAAO,MACP;AAAA,MACV,QAAQ,iBAAiB,kBAAkBA,OAAM,IAC3CA,QAAO,SACP;AAAA,MACN,QAAQ,SAASA,OAAM,IACjBA,UACA,kBAAkBA,OAAM,IACpBA,QAAO,SACP;AAAA,MACV,SAAS;AAAA,IACb;AACA,aAAS,OAAO,OAAO,CAAC,GAAG,QAAQ,QAAQ,kBAAkB,KAAK,CAAC,CAAC;AACpE,sBAAkB,QAAQ;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,aAAa,SAAS;AAC3B,MAAI,QAAQ,QAAQ,IAAI,GAAG;AACvB,YAAQ,OAAO,QAAQ,KAAK,IAAI,UAAQ,SAAS,IAAI,IAAI,WAAW,IAAI,IAAI,IAAI;AAAA,EACpF,WACS,SAAS,QAAQ,KAAK,GAAG;AAC9B,WAAO,KAAK,QAAQ,KAAK,EAAE,QAAQ,SAAO;AACtC,UAAI,SAAS,QAAQ,MAAM,GAAG,CAAC,GAAG;AAC9B,gBAAQ,MAAM,GAAG,IAAI,WAAW,QAAQ,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,qBAAqB,SAAS,KAAK,QAAQ,gBAAgB,cAAc,aAAa;AAC3F,QAAM,EAAE,UAAU,QAAQ,iBAAiBC,eAAc,iBAAiB,IAAI;AAC9E,QAAM,UAAU,iBAAiB,SAAS,gBAAgB,MAAM;AAChE,MAAI,UAAU,OAAO;AACrB,MAAI;AACJ,MAAID,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,mBAAe,KAAK,QAAQ,CAAC;AAC7B,QACI,WAAW,gBACX,CAAC,mBAAmB,QAAQ,YAAY,KACxC,wBAAwB,cAAc,GAAG,GAAG;AAC5C,aAAO,eAAe,cAAc,uBAAuB;AAAA,QACvD;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAEA,QAA+C,WAAW,cAAc;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAAoD;AAAA,UAC7D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAC3B,CAAC;AAAA,MACL;AAAA,IACJ;AACA,cACI,SAAS,YAAY,KAAK,OAAO;AAErC,QAAI,QAAQ;AACZ,QAAI;AACJ,QAAI;AACJ,QAA+C,WAAW;AACtD,cAAQ,OAAO,YAAY,IAAI;AAC/B,iBAAW;AACX,eAAS;AACT,cAAQ,KAAK,QAAQ;AAAA,IACzB;AACA,SAAKA,UAASC,cAAa,SAAS,GAAG,OAAO,MAAM;AAEhD,MAAAD,UAAS,QAAQ,GAAG;AAAA,IACxB;AAEA,QAA+C,WAAW;AACtD,YAAM,MAAM,OAAO,YAAY,IAAI;AACnC,YAAM,UAAU,QAAQ;AACxB,UAAI,WAAW,SAASA,SAAQ;AAC5B,gBAAQ,KAAK,mBAAmE;AAAA,UAC5E,MAAM;AAAA,UACN;AAAA,UACA,SAASA;AAAA,UACT,MAAM,MAAM;AAAA,UACZ,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAC3B,CAAC;AAAA,MACL;AACA,UAAI,YAAY,UAAU,QAAQ,SAAS;AACvC,aAAK,MAAM;AACX,gBAAQ,2BAA2B,UAAU,MAAM;AAAA,MACvD;AAAA,IACJ;AACA,QAAI,SAASA,OAAM,KAAK,aAAaA,OAAM,KAAK,kBAAkBA,OAAM,GAAG;AACvE;AAAA,IACJ;AACA,QAAI,CAAC,mBAAmB,cAAc,OAAO,GAAG;AAC5C,YAAM,aAAa;AAAA,QAAc;AAAA;AAAA,QACjC;AAAA,QAAK;AAAA,QAAc;AAAA,QAAa;AAAA,MAAI;AACpC,UAAI,eAAe,KAAK;AACpB,QAAAA,UAAS;AAAA,MACb;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAO,CAACA,SAAQ,cAAc,OAAO;AACzC;AACA,SAAS,qBAAqB,SAAS,KAAK,cAAcA,SAAQ,cAAc,SAAS;AACrF,QAAM,EAAE,iBAAiB,gBAAgB,IAAI;AAC7C,MAAI,kBAAkBA,OAAM,GAAG;AAC3B,UAAME,OAAMF;AACZ,IAAAE,KAAI,SAASA,KAAI,UAAU;AAC3B,IAAAA,KAAI,MAAMA,KAAI,OAAO;AACrB,WAAOA;AAAA,EACX;AACA,MAAI,mBAAmB,MAAM;AACzB,UAAMA,OAAO,MAAMF;AACnB,IAAAE,KAAI,SAAS;AACb,IAAAA,KAAI,MAAM;AACV,WAAOA;AAAA,EACX;AAEA,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAA+C,WAAW;AACtD,YAAQ,OAAO,YAAY,IAAI;AAC/B,eAAW;AACX,aAAS;AACT,YAAQ,KAAK,QAAQ;AAAA,EACzB;AACA,QAAM,MAAM,gBAAgBF,SAAQ,kBAAkB,SAAS,cAAc,cAAcA,SAAQ,iBAAiB,OAAO,CAAC;AAE5H,MAA+C,WAAW;AACtD,UAAM,MAAM,OAAO,YAAY,IAAI;AACnC,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,OAAO;AAClB,cAAQ,KAAK,uBAA2E;AAAA,QACpF,MAAM;AAAA,QACN,SAASA;AAAA,QACT,MAAM,MAAM;AAAA,QACZ,SAAS,GAAG,WAAW,IAAI,GAAG;AAAA,MAClC,CAAC;AAAA,IACL;AACA,QAAI,YAAY,UAAU,QAAQ,SAAS;AACvC,WAAK,MAAM;AACX,cAAQ,+BAA+B,UAAU,MAAM;AAAA,IAC3D;AAAA,EACJ;AACA,MAAI,SAAS;AACb,MAAI,MAAM;AACV,MAAI,SAASA;AACb,SAAO;AACX;AACA,SAAS,gBAAgB,SAAS,KAAK,QAAQ;AAE3C,MAAI,QAAQ;AACZ,MAAI;AACJ,MAAI;AACJ,MAA+C,WAAW;AACtD,YAAQ,OAAO,YAAY,IAAI;AAC/B,eAAW;AACX,aAAS;AACT,YAAQ,KAAK,QAAQ;AAAA,EACzB;AACA,QAAM,WAAW,IAAI,MAAM;AAE3B,MAA+C,WAAW;AACtD,UAAM,MAAM,OAAO,YAAY,IAAI;AACnC,UAAM,UAAU,QAAQ;AACxB,QAAI,WAAW,OAAO;AAClB,cAAQ,KAAK,sBAAyE;AAAA,QAClF,MAAM;AAAA,QACN,OAAO;AAAA,QACP,MAAM,MAAM;AAAA,QACZ,SAAS,GAAG,WAAW,IAAI,IAAI,GAAG;AAAA,MACtC,CAAC;AAAA,IACL;AACA,QAAI,YAAY,UAAU,QAAQ,SAAS;AACvC,WAAK,MAAM;AACX,cAAQ,8BAA8B,UAAU,MAAM;AAAA,IAC1D;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,sBAAsB,MAAM;AACjC,QAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,QAAM,UAAU,OAAO;AACvB,MAAI,CAAC,SAAS,IAAI,KACd,CAAC,SAAS,IAAI,KACd,CAAC,kBAAkB,IAAI,KACvB,CAAC,aAAa,IAAI,GAAG;AACrB,UAAM,gBAAgB,eAAe,gBAAgB;AAAA,EACzD;AAEA,QAAM,MAAM,SAAS,IAAI,IACnB,OAAO,IAAI,IACX,kBAAkB,IAAI,IAClB,OACA;AACV,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,SAAS,IAAI,GAAG;AACrB,YAAQ,UAAU;AAAA,EACtB,WACS,cAAc,IAAI,KAAK,CAAC,cAAc,IAAI,GAAG;AAClD,YAAQ,QAAQ;AAAA,EACpB,WACS,QAAQ,IAAI,GAAG;AACpB,YAAQ,OAAO;AAAA,EACnB;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,SAAS,IAAI,GAAG;AACrB,YAAQ,UAAU;AAAA,EACtB,WACS,cAAc,IAAI,GAAG;AAC1B,WAAO,SAAS,IAAI;AAAA,EACxB;AACA,SAAO,CAAC,KAAK,OAAO;AACxB;AACA,SAAS,kBAAkB,SAAS,QAAQ,KAAK,QAAQ,iBAAiB,SAAS;AAC/E,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,SAAS,CAAC,QAAQ;AACd,iBAAW,QAAQ,GAAG;AACtB,UAAK,MAAwC;AACzC,cAAM,UAAU,sBAAsB,MAAM;AAC5C,cAAM,UAAU,8BAA8B,IAAI,OAAO;AACzD,cAAM,YAAY,IAAI,YAClB,WACA,kBAAkB,SAAS,IAAI,SAAS,MAAM,QAAQ,IAAI,SAAS,IAAI,MAAM;AACjF,cAAM,UAAU,QAAQ;AACxB,YAAI,WAAW,SAAS;AACpB,kBAAQ,KAAK,iBAA+D;AAAA,YACxE,SAAS;AAAA,YACT,OAAO,IAAI;AAAA,YACX,OAAO,IAAI,YAAY,IAAI,SAAS,MAAM;AAAA,YAC1C,KAAK,IAAI,YAAY,IAAI,SAAS,IAAI;AAAA,YACtC,SAAS,GAAG,WAAW,IAAI,GAAG;AAAA,UAClC,CAAC;AAAA,QACL;AACA,gBAAQ,MAAM,YAAY,GAAG,OAAO;AAAA,EAAK,SAAS,KAAK,OAAO;AAAA,MAClE,OACK;AACD,cAAM;AAAA,MACV;AAAA,IACJ;AAAA,IACA,YAAY,CAACG,YAAW,uBAAuB,QAAQ,KAAKA,OAAM;AAAA,EACtE;AACJ;AACA,SAAS,sBAAsB,QAAQ;AACnC,MAAI,SAAS,MAAM,GAAG;AAClB,WAAO;AAAA,EACX,OACK;AACD,QAAI,OAAO,OAAO,OAAO,IAAI,QAAQ;AACjC,aAAO,OAAO,IAAI;AAAA,IACtB;AAAA,EACJ;AACJ;AACA,SAAS,yBAAyB,SAAS,QAAQ,SAAS,SAAS;AACjE,QAAM,EAAE,WAAW,aAAa,iBAAiBF,eAAc,gBAAgB,cAAc,aAAa,gBAAgB,IAAI;AAC9H,QAAM,iBAAiB,CAAC,QAAQ;AAC5B,QAAI,MAAMA,cAAa,SAAS,GAAG;AAEnC,QAAI,OAAO,QAAQ,iBAAiB;AAChC,YAAM,CAAC,EAAE,EAAEG,QAAO,IAAI,qBAAqB,iBAAiB,KAAK,QAAQ,gBAAgB,cAAc,WAAW;AAClH,YAAMH,cAAaG,UAAS,GAAG;AAAA,IACnC;AACA,QAAI,SAAS,GAAG,KAAK,aAAa,GAAG,GAAG;AACpC,UAAI,WAAW;AACf,YAAM,UAAU,MAAM;AAClB,mBAAW;AAAA,MACf;AACA,YAAM,MAAM,qBAAqB,SAAS,KAAK,QAAQ,KAAK,KAAK,OAAO;AACxE,aAAO,CAAC,WACF,MACA;AAAA,IACV,WACS,kBAAkB,GAAG,GAAG;AAC7B,aAAO;AAAA,IACX,OACK;AAED,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,aAAa;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACd;AACA,MAAI,QAAQ,WAAW;AACnB,eAAW,YAAY,QAAQ;AAAA,EACnC;AACA,MAAI,QAAQ,MAAM;AACd,eAAW,OAAO,QAAQ;AAAA,EAC9B;AACA,MAAI,QAAQ,OAAO;AACf,eAAW,QAAQ,QAAQ;AAAA,EAC/B;AACA,MAAI,SAAS,QAAQ,MAAM,GAAG;AAC1B,eAAW,cAAc,QAAQ;AAAA,EACrC;AACA,SAAO;AACX;AAEA,IAAM,cAAc,OAAO,SAAS;AACpC,IAAM,iBAAiB;AAAA,EACnB,gBAAgB,eAAe,OAAO,KAAK,mBAAmB;AAAA,EAC9D,cAAc,eAAe,OAAO,KAAK,iBAAiB;AAC9D;AAGA,SAAS,SAAS,YAAY,MAAM;AAChC,QAAM,EAAE,iBAAiB,aAAa,gBAAgB,QAAQ,iBAAiB,IAAI;AACnF,QAAM,EAAE,qBAAqB,IAAI;AACjC,MAA+C,CAAC,eAAe,gBAAgB;AAC3E,WAAO,eAAe,cAAc,kBAAkB,CAAC;AACvD,WAAO;AAAA,EACX;AACA,QAAM,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,kBAAkB,GAAG,IAAI;AAClE,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,OAAO,CAAC,CAAC,QAAQ;AACvB,QAAM,SAAS,UAAU,SAAS,OAAO;AACzC,QAAM,UAAU;AAAA,IAAiB;AAAA;AAAA,IACjC;AAAA,IAAgB;AAAA,EAAM;AACtB,MAAI,CAAC,SAAS,GAAG,KAAK,QAAQ,IAAI;AAC9B,WAAO,IAAI,KAAK,eAAe,QAAQ,SAAS,EAAE,OAAO,KAAK;AAAA,EAClE;AAEA,MAAI,iBAAiB,CAAC;AACtB,MAAI;AACJ,MAAIJ,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,mBAAe,KAAK,QAAQ,CAAC;AAC7B,QACI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,aAAO,eAAe,cAAc,yBAAyB;AAAA,QACzD;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAEA,QAA+C,WAAW,cAAc;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAAoD;AAAA,UAC7D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAC3B,CAAC;AAAA,MACL;AAAA,IACJ;AACA,qBACI,gBAAgB,YAAY,KAAK,CAAC;AACtC,IAAAA,UAAS,eAAe,GAAG;AAC3B,QAAI,cAAcA,OAAM;AACpB;AACJ,kBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC3D,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,cAAcA,OAAM,KAAK,CAAC,SAAS,YAAY,GAAG;AACnD,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAAI,KAAK,GAAG,YAAY,KAAK,GAAG;AAChC,MAAI,CAAC,cAAc,SAAS,GAAG;AAC3B,SAAK,GAAG,EAAE,KAAK,KAAK,UAAU,SAAS,CAAC;AAAA,EAC5C;AACA,MAAI,YAAY,qBAAqB,IAAI,EAAE;AAC3C,MAAI,CAAC,WAAW;AACZ,gBAAY,IAAI,KAAK,eAAe,cAAc,OAAO,CAAC,GAAGA,SAAQ,SAAS,CAAC;AAC/E,yBAAqB,IAAI,IAAI,SAAS;AAAA,EAC1C;AACA,SAAO,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAC1E;AAEA,IAAM,+BAA+B;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,qBAAqB,MAAM;AAChC,QAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,QAAM,UAAU,OAAO;AACvB,MAAI,YAAY,OAAO;AACvB,MAAI;AACJ,MAAI,SAAS,IAAI,GAAG;AAGhB,UAAM,UAAU,KAAK,MAAM,gCAAgC;AAC3D,QAAI,CAAC,SAAS;AACV,YAAM,gBAAgB,eAAe,yBAAyB;AAAA,IAClE;AAGA,UAAM,WAAW,QAAQ,CAAC,IACpB,QAAQ,CAAC,EAAE,KAAK,EAAE,WAAW,GAAG,IAC5B,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,KACxC,GAAG,QAAQ,CAAC,EAAE,KAAK,CAAC,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,KAC7C,QAAQ,CAAC,EAAE,KAAK;AACtB,YAAQ,IAAI,KAAK,QAAQ;AACzB,QAAI;AAEA,YAAM,YAAY;AAAA,IACtB,SACO,GAAG;AACN,YAAM,gBAAgB,eAAe,yBAAyB;AAAA,IAClE;AAAA,EACJ,WACS,OAAO,IAAI,GAAG;AACnB,QAAI,MAAM,KAAK,QAAQ,CAAC,GAAG;AACvB,YAAM,gBAAgB,eAAe,qBAAqB;AAAA,IAC9D;AACA,YAAQ;AAAA,EACZ,WACS,SAAS,IAAI,GAAG;AACrB,YAAQ;AAAA,EACZ,OACK;AACD,UAAM,gBAAgB,eAAe,gBAAgB;AAAA,EACzD;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,MAAM;AAAA,EAClB,WACS,cAAc,IAAI,GAAG;AAC1B,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC7B,UAAI,6BAA6B,SAAS,GAAG,GAAG;AAC5C,kBAAU,GAAG,IAAI,KAAK,GAAG;AAAA,MAC7B,OACK;AACD,gBAAQ,GAAG,IAAI,KAAK,GAAG;AAAA,MAC3B;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,cAAc,IAAI,GAAG;AAC1B,gBAAY;AAAA,EAChB;AACA,MAAI,cAAc,IAAI,GAAG;AACrB,gBAAY;AAAA,EAChB;AACA,SAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AACxD;AAEA,SAAS,oBAAoB,KAAK,QAAQA,SAAQ;AAC9C,QAAM,UAAU;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AAC5B,QAAI,CAAC,QAAQ,qBAAqB,IAAI,EAAE,GAAG;AACvC;AAAA,IACJ;AACA,YAAQ,qBAAqB,OAAO,EAAE;AAAA,EAC1C;AACJ;AAGA,SAAS,OAAO,YAAY,MAAM;AAC9B,QAAM,EAAE,eAAe,aAAa,gBAAgB,QAAQ,iBAAiB,IAAI;AACjF,QAAM,EAAE,mBAAmB,IAAI;AAC/B,MAA+C,CAAC,eAAe,cAAc;AACzE,WAAO,eAAe,cAAc,oBAAoB,CAAC;AACzD,WAAO;AAAA,EACX;AACA,QAAM,CAAC,KAAK,OAAO,SAAS,SAAS,IAAI,gBAAgB,GAAG,IAAI;AAChE,QAAM,cAAc,UAAU,QAAQ,WAAW,IAC3C,QAAQ,cACR,QAAQ;AACd,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR,QAAQ;AACd,QAAM,OAAO,CAAC,CAAC,QAAQ;AACvB,QAAM,SAAS,UAAU,SAAS,OAAO;AACzC,QAAM,UAAU;AAAA,IAAiB;AAAA;AAAA,IACjC;AAAA,IAAgB;AAAA,EAAM;AACtB,MAAI,CAAC,SAAS,GAAG,KAAK,QAAQ,IAAI;AAC9B,WAAO,IAAI,KAAK,aAAa,QAAQ,SAAS,EAAE,OAAO,KAAK;AAAA,EAChE;AAEA,MAAI,eAAe,CAAC;AACpB,MAAI;AACJ,MAAIA,UAAS;AACb,MAAI,OAAO;AACX,MAAI,KAAK;AACT,QAAM,OAAO;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,mBAAe,KAAK,QAAQ,CAAC;AAC7B,QACI,WAAW,gBACX,wBAAwB,cAAc,GAAG,GAAG;AAC5C,aAAO,eAAe,cAAc,2BAA2B;AAAA,QAC3D;AAAA,QACA,QAAQ;AAAA,MACZ,CAAC,CAAC;AAAA,IACN;AAEA,QAA+C,WAAW,cAAc;AACpE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,gBAAQ,KAAK,YAAoD;AAAA,UAC7D;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,GAAG,IAAI,IAAI,GAAG;AAAA,QAC3B,CAAC;AAAA,MACL;AAAA,IACJ;AACA,mBACI,cAAc,YAAY,KAAK,CAAC;AACpC,IAAAA,UAAS,aAAa,GAAG;AACzB,QAAI,cAAcA,OAAM;AACpB;AACJ,kBAAc,SAAS,KAAK,cAAc,aAAa,IAAI;AAC3D,WAAO;AAAA,EACX;AAEA,MAAI,CAAC,cAAcA,OAAM,KAAK,CAAC,SAAS,YAAY,GAAG;AACnD,WAAO,cAAc,eAAe;AAAA,EACxC;AACA,MAAI,KAAK,GAAG,YAAY,KAAK,GAAG;AAChC,MAAI,CAAC,cAAc,SAAS,GAAG;AAC3B,SAAK,GAAG,EAAE,KAAK,KAAK,UAAU,SAAS,CAAC;AAAA,EAC5C;AACA,MAAI,YAAY,mBAAmB,IAAI,EAAE;AACzC,MAAI,CAAC,WAAW;AACZ,gBAAY,IAAI,KAAK,aAAa,cAAc,OAAO,CAAC,GAAGA,SAAQ,SAAS,CAAC;AAC7E,uBAAmB,IAAI,IAAI,SAAS;AAAA,EACxC;AACA,SAAO,CAAC,OAAO,UAAU,OAAO,KAAK,IAAI,UAAU,cAAc,KAAK;AAC1E;AAEA,IAAM,6BAA6B;AAAA,EAC/B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,SAAS,mBAAmB,MAAM;AAC9B,QAAM,CAAC,MAAM,MAAM,MAAM,IAAI,IAAI;AACjC,QAAM,UAAU,OAAO;AACvB,MAAI,YAAY,OAAO;AACvB,MAAI,CAAC,SAAS,IAAI,GAAG;AACjB,UAAM,gBAAgB,eAAe,gBAAgB;AAAA,EACzD;AACA,QAAM,QAAQ;AACd,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,MAAM;AAAA,EAClB,WACS,cAAc,IAAI,GAAG;AAC1B,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO;AAC7B,UAAI,2BAA2B,SAAS,GAAG,GAAG;AAC1C,kBAAU,GAAG,IAAI,KAAK,GAAG;AAAA,MAC7B,OACK;AACD,gBAAQ,GAAG,IAAI,KAAK,GAAG;AAAA,MAC3B;AAAA,IACJ,CAAC;AAAA,EACL;AACA,MAAI,SAAS,IAAI,GAAG;AAChB,YAAQ,SAAS;AAAA,EACrB,WACS,cAAc,IAAI,GAAG;AAC1B,gBAAY;AAAA,EAChB;AACA,MAAI,cAAc,IAAI,GAAG;AACrB,gBAAY;AAAA,EAChB;AACA,SAAO,CAAC,QAAQ,OAAO,IAAI,OAAO,SAAS,SAAS;AACxD;AAEA,SAAS,kBAAkB,KAAK,QAAQA,SAAQ;AAC5C,QAAM,UAAU;AAChB,aAAW,OAAOA,SAAQ;AACtB,UAAM,KAAK,GAAG,MAAM,KAAK,GAAG;AAC5B,QAAI,CAAC,QAAQ,mBAAmB,IAAI,EAAE,GAAG;AACrC;AAAA,IACJ;AACA,YAAQ,mBAAmB,OAAO,EAAE;AAAA,EACxC;AACJ;AAEA;AACI,mBAAiB;AACrB;;;ACx3DA,IAAMK,WAAU;AAKhB,SAASC,oBAAmB;AACxB,MAAI,OAAO,8BAA8B,WAAW;AAChD,kBAAc,EAAE,4BAA4B;AAAA,EAChD;AACA,MAAI,OAAO,4BAA4B,WAAW;AAC9C,kBAAc,EAAE,0BAA0B;AAAA,EAC9C;AACA,MAAI,OAAO,gCAAgC,WAAW;AAClD,kBAAc,EAAE,8BAA8B;AAAA,EAClD;AACA,MAAI,OAAO,sCAAsC,WAAW;AACxD,kBAAc,EAAE,oCAAoC;AAAA,EACxD;AACA,MAAI,OAAO,8BAA8B,WAAW;AAChD,kBAAc,EAAE,4BAA4B;AAAA,EAChD;AACJ;AAEA,IAAMC,UAAS,cAAc;AAC7B,IAAMC,SAAQ,YAAYD,OAAM;AAChC,IAAM,gBAAgB;AAAA,EAClB,kBAAkBA;AAAA;AAAA,EAClB,wBAAwBC,OAAM;AAAA;AAAA,EAC9B,yBAAyBA,OAAM;AAAA;AAAA,EAC/B,kCAAkCA,OAAM;AAAA;AAAA,EACxC,gCAAgCA,OAAM;AAAA;AAAA,EACtC,kCAAkCA,OAAM;AAAA;AAAA,EACxC,wBAAwBA,OAAM;AAAA;AAAA,EAC9B,oBAAoBA,OAAM;AAAA;AAAA,EAC1B,+BAA+BA,OAAM;AAAA;AAAA,EACrC,6CAA6CA,OAAM;AAAA;AACvD;AACA,IAAMC,gBAAe;AAAA,EACjB,CAAC,cAAc,gBAAgB,GAAG;AAAA,EAClC,CAAC,cAAc,sBAAsB,GAAG;AAAA,EACxC,CAAC,cAAc,uBAAuB,GAAG;AAAA,EACzC,CAAC,cAAc,gCAAgC,GAAG;AAAA,EAClD,CAAC,cAAc,8BAA8B,GAAG;AAAA,EAChD,CAAC,cAAc,gCAAgC,GAAG;AAAA,EAClD,CAAC,cAAc,sBAAsB,GAAG;AAAA,EACxC,CAAC,cAAc,kBAAkB,GAAG;AAAA,EACpC,CAAC,cAAc,6BAA6B,GAAG;AAAA,EAC/C,CAAC,cAAc,2CAA2C,GAAG;AACjE;AACA,SAASC,gBAAeC,UAAS,MAAM;AACnC,SAAO,OAAOF,cAAaE,KAAI,GAAG,GAAG,IAAI;AAC7C;AAEA,IAAMA,QAAO,eAAe;AAC5B,IAAMC,OAAM,YAAYD,KAAI;AAC5B,IAAM,iBAAiB;AAAA;AAAA,EAEnB,wBAAwBA;AAAA;AAAA;AAAA,EAExB,kBAAkBC,KAAI;AAAA;AAAA;AAAA,EAEtB,wBAAwBA,KAAI;AAAA;AAAA,EAC5B,eAAeA,KAAI;AAAA;AAAA,EACnB,8BAA8BA,KAAI;AAAA;AAAA;AAAA,EAElC,gBAAgBA,KAAI;AAAA;AAAA,EACpB,eAAeA,KAAI;AAAA;AAAA;AAAA,EAEnB,kCAAkCA,KAAI;AAAA;AAAA,EACtC,4BAA4BA,KAAI;AAAA;AAAA;AAAA,EAEhC,kBAAkBA,KAAI;AAAA;AAAA;AAAA,EAEtB,gCAAgCA,KAAI;AAAA;AAAA;AAAA,EAEpC,2BAA2BA,KAAI;AAAA;AAAA;AAAA,EAE/B,8CAA8CA,KAAI;AAAA;AAAA;AAAA,EAElD,qCAAqCA,KAAI;AAAA;AAAA;AAAA,EAEzC,kBAAkBA,KAAI;AAAA;AAC1B;AACA,SAAS,gBAAgBD,UAAS,MAAM;AACpC,SAAO,mBAAmBA,OAAM,MAAO,OAAyC,EAAE,UAAUE,gBAAe,KAAK,IAAI,MAAS;AACjI;AACA,IAAMA,iBAAgB;AAAA,EAClB,CAAC,eAAe,sBAAsB,GAAG;AAAA,EACzC,CAAC,eAAe,gBAAgB,GAAG;AAAA,EACnC,CAAC,eAAe,sBAAsB,GAAG;AAAA,EACzC,CAAC,eAAe,aAAa,GAAG;AAAA,EAChC,CAAC,eAAe,gBAAgB,GAAG;AAAA,EACnC,CAAC,eAAe,4BAA4B,GAAG;AAAA,EAC/C,CAAC,eAAe,cAAc,GAAG;AAAA,EACjC,CAAC,eAAe,aAAa,GAAG;AAAA,EAChC,CAAC,eAAe,gCAAgC,GAAG;AAAA,EACnD,CAAC,eAAe,0BAA0B,GAAG;AAAA,EAC7C,CAAC,eAAe,8BAA8B,GAAG;AAAA,EACjD,CAAC,eAAe,yBAAyB,GAAG;AAAA,EAC5C,CAAC,eAAe,4CAA4C,GAAG;AAAA,EAC/D,CAAC,eAAe,mCAAmC,GAAG;AAC1D;AAEA,IAAM,uBACS,WAAW,kBAAkB;AAC5C,IAAM,sBAAqC,WAAW,iBAAiB;AACvE,IAAM,oBAAmC,WAAW,eAAe;AACnE,IAAM,gBAA+B,WAAW,iBAAiB;AACjE,IAAM,iBAAgC,WAAW,kBAAkB;AACnE,IAAM,uBAAuB,WAAW,kBAAkB;AAC1D,WAAW,eAAe;AAC1B,IAAM,yBACS,WAAW,oBAAoB;AAC9C,IAAM,gBAA+B,WAAW,WAAW;AAC3D,IAAM,sBAAuB;AAM7B,SAAS,eAAe,KAAK;AAEzB,MAAI,CAAC,SAAS,GAAG,GAAG;AAChB,WAAO;AAAA,EACX;AACA,MAAI,aAAa,GAAG,GAAG;AACnB,WAAO;AAAA,EACX;AACA,aAAW,OAAO,KAAK;AAEnB,QAAI,CAAC,OAAO,KAAK,GAAG,GAAG;AACnB;AAAA,IACJ;AAEA,QAAI,CAAC,IAAI,SAAS,GAAG,GAAG;AAEpB,UAAI,SAAS,IAAI,GAAG,CAAC,GAAG;AACpB,uBAAe,IAAI,GAAG,CAAC;AAAA,MAC3B;AAAA,IACJ,OAEK;AAED,YAAM,UAAU,IAAI,MAAM,GAAG;AAC7B,YAAM,YAAY,QAAQ,SAAS;AACnC,UAAI,aAAa;AACjB,UAAI,iBAAiB;AACrB,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAChC,YAAI,QAAQ,CAAC,MAAM,aAAa;AAC5B,gBAAM,IAAI,MAAM,eAAe,QAAQ,CAAC,CAAC,EAAE;AAAA,QAC/C;AACA,YAAI,EAAE,QAAQ,CAAC,KAAK,aAAa;AAC7B,qBAAW,QAAQ,CAAC,CAAC,IAAI,OAAO;AAAA,QACpC;AACA,YAAI,CAAC,SAAS,WAAW,QAAQ,CAAC,CAAC,CAAC,GAAG;AACnC,UACI,KAAKH,gBAAe,cAAc,oBAAoB;AAAA,YAClD,KAAK,QAAQ,CAAC;AAAA,UAClB,CAAC,CAAC;AACN,2BAAiB;AACjB;AAAA,QACJ;AACA,qBAAa,WAAW,QAAQ,CAAC,CAAC;AAAA,MACtC;AAEA,UAAI,CAAC,gBAAgB;AACjB,YAAI,CAAC,aAAa,UAAU,GAAG;AAC3B,qBAAW,QAAQ,SAAS,CAAC,IAAI,IAAI,GAAG;AACxC,iBAAO,IAAI,GAAG;AAAA,QAClB,OACK;AAKD,cAAI,CAAC,oBAAoB,SAAS,QAAQ,SAAS,CAAC,GAAG;AACnD,mBAAO,IAAI,GAAG;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,CAAC,aAAa,UAAU,GAAG;AAC3B,cAAM,SAAS,WAAW,QAAQ,SAAS,CAAC;AAC5C,YAAI,SAAS,MAAM,GAAG;AAClB,yBAAe,MAAM;AAAA,QACzB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,kBAAkB,QAAQ,SAAS;AACxC,QAAM,EAAE,UAAU,QAAQ,iBAAiB,SAAS,IAAI;AAExD,QAAM,MAAO,cAAc,QAAQ,IAC7B,WACA,QAAQ,MAAM,IACV,OAAO,IACP,EAAE,CAAC,MAAM,GAAG,OAAO,EAAE;AAE/B,MAAI,QAAQ,MAAM,GAAG;AACjB,WAAO,QAAQ,YAAU;AACrB,UAAI,YAAY,UAAU,cAAc,QAAQ;AAC5C,cAAM,EAAE,QAAAI,SAAQ,SAAS,IAAI;AAC7B,YAAIA,SAAQ;AACR,cAAIA,OAAM,IAAI,IAAIA,OAAM,KAAK,OAAO;AACpC,mBAAS,UAAU,IAAIA,OAAM,CAAC;AAAA,QAClC,OACK;AACD,mBAAS,UAAU,GAAG;AAAA,QAC1B;AAAA,MACJ,OACK;AACD,iBAAS,MAAM,KAAK,SAAS,KAAK,MAAM,MAAM,GAAG,GAAG;AAAA,MACxD;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,MAAI,mBAAmB,QAAQ,UAAU;AACrC,eAAW,OAAO,KAAK;AACnB,UAAI,OAAO,KAAK,GAAG,GAAG;AAClB,uBAAe,IAAI,GAAG,CAAC;AAAA,MAC3B;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,oBAAoB,UAAU;AACnC,SAAO,SAAS;AACpB;AACA,SAAS,oBAAoB,IAAI,SAAS,kBACxC;AACE,MAAI,WAAW,SAAS,QAAQ,QAAQ,IAClC,QAAQ,WACR,OAAO;AACb,MAAI,kBAAkB,kBAAkB;AACpC,eAAW,kBAAkB,GAAG,OAAO,OAAO;AAAA,MAC1C;AAAA,MACA,QAAQ,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACL;AAEA,QAAM,UAAU,OAAO,KAAK,QAAQ;AACpC,MAAI,QAAQ,QAAQ;AAChB,YAAQ,QAAQ,YAAU;AACtB,SAAG,mBAAmB,QAAQ,SAAS,MAAM,CAAC;AAAA,IAClD,CAAC;AAAA,EACL;AACA;AAEI,QAAI,SAAS,QAAQ,eAAe,GAAG;AACnC,YAAMC,WAAU,OAAO,KAAK,QAAQ,eAAe;AACnD,UAAIA,SAAQ,QAAQ;AAChB,QAAAA,SAAQ,QAAQ,YAAU;AACtB,aAAG,oBAAoB,QAAQ,QAAQ,gBAAgB,MAAM,CAAC;AAAA,QAClE,CAAC;AAAA,MACL;AAAA,IACJ;AAEA,QAAI,SAAS,QAAQ,aAAa,GAAG;AACjC,YAAMA,WAAU,OAAO,KAAK,QAAQ,aAAa;AACjD,UAAIA,SAAQ,QAAQ;AAChB,QAAAA,SAAQ,QAAQ,YAAU;AACtB,aAAG,kBAAkB,QAAQ,QAAQ,cAAc,MAAM,CAAC;AAAA,QAC9D,CAAC;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,KAAK;AACzB,SAAO,YAAY,MAAM,MAAM,KAAK,CAAC;AAEzC;AAKA,IAAM,gBAAgB;AACtB,IAAM,oBAAoB,MAAM,CAAC;AACjC,IAAM,oBAAoB,MAAM;AAChC,IAAI,aAAa;AACjB,SAAS,yBAAyB,SAAS;AACvC,SAAQ,CAAC,KAAK,QAAQ,KAAK,SAAS;AAChC,WAAO,QAAQ,QAAQ,KAAK,mBAAmB,KAAK,QAAW,IAAI;AAAA,EACvE;AACJ;AAGA,IAAM,cAAc,MAAM;AACtB,QAAM,WAAW,mBAAmB;AACpC,MAAI,OAAO;AACX,SAAO,aAAa,OAAO,oBAAoB,QAAQ,EAAE,aAAa,KAChE,EAAE,CAAC,aAAa,GAAG,KAAK,IACxB;AACV;AAOA,SAAS,eAAe,UAAU,CAAC,GAAG,eAAe;AACjD,QAAM,EAAE,QAAQ,mBAAmB,IAAI;AACvC,QAAM,YAAY,WAAW;AAC7B,QAAM,WAAW,QAAQ;AACzB,QAAM,OAAO,YAAY,MAAM;AAC/B,QAAM,2BAA2B,CAAC,CAAC,QAAQ;AAC3C,MAAK,MAAwC;AACzC,QAAI,4BAA4B,MAAQ;AACpC,eAASL,gBAAe,cAAc,2CAA2C,CAAC;AAAA,IACtF;AAAA,EACJ;AACA,MAAI,iBAAiB,UAAU,QAAQ,aAAa,IAC9C,QAAQ,gBACR;AACN,QAAM,UAAU;AAAA;AAAA,IAEhB,UAAU,iBACJ,OAAO,OAAO,QACd,SAAS,QAAQ,MAAM,IACnB,QAAQ,SACR;AAAA,EAAc;AACxB,QAAM,kBAAkB;AAAA;AAAA,IAExB,UAAU,iBACJ,OAAO,eAAe,QACtB,SAAS,QAAQ,cAAc,KAC7B,QAAQ,QAAQ,cAAc,KAC9B,cAAc,QAAQ,cAAc,KACpC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,EAAK;AACvB,QAAM,YAAY,KAAK,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AAEhE,QAAM,mBAAmB,KAAK,cAAc,QAAQ,eAAe,IACzD,QAAQ,kBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAGjC,QAAM,iBAAiB,KAAK,cAAc,QAAQ,aAAa,IACrD,QAAQ,gBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAIjC,MAAI,eAAe,SACb,OAAO,cACP,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC1D,QAAQ,cACR;AAEV,MAAI,gBAAgB,SACd,OAAO,eACP,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC5D,QAAQ,eACR;AAEV,MAAI,gBAAgB,SACd,OAAO,eACP,UAAU,QAAQ,YAAY,IAC1B,QAAQ,eACR;AAEV,MAAI,kBAAkB,CAAC,CAAC,QAAQ;AAEhC,MAAI,WAAW,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAC/D,MAAI,kBAAkB,WAAW,QAAQ,OAAO,IAC1C,yBAAyB,QAAQ,OAAO,IACxC;AAEN,MAAI,mBAAmB,WAAW,QAAQ,eAAe,IACnD,QAAQ,kBACR;AAEN,MAAI,mBAAmB,SACjB,OAAO,kBACP,UAAU,QAAQ,eAAe,IAC7B,QAAQ,kBACR;AACV,MAAI,mBAAmB,CAAC,CAAC,QAAQ;AAGjC,QAAM,aAAa,SACb,OAAO,YACP,cAAc,QAAQ,SAAS,IAC3B,QAAQ,YACR,CAAC;AAEX,MAAI,eAAe,QAAQ,eAAgB,UAAU,OAAO;AAG5D,MAAI;AACJ,QAAM,iBAAiB,MAAM;AACzB,iBAAa,mBAAmB,IAAI;AACpC,UAAM,aAAa;AAAA,MACf,SAASL;AAAA,MACT,QAAQ,QAAQ;AAAA,MAChB,gBAAgB,gBAAgB;AAAA,MAChC,UAAU,UAAU;AAAA,MACpB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,SAAS,oBAAoB,OAAO,SAAY;AAAA,MAChD,aAAa;AAAA,MACb,cAAc;AAAA,MACd,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,iBAAiB,qBAAqB,OAAO,SAAY;AAAA,MACzD,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,MACjB,iBAAiB,QAAQ;AAAA,MACzB,iBAAiB,QAAQ;AAAA,MACzB,QAAQ,EAAE,WAAW,MAAM;AAAA,IAC/B;AACA;AACI,iBAAW,kBAAkB,iBAAiB;AAC9C,iBAAW,gBAAgB,eAAe;AAC1C,iBAAW,uBAAuB,cAAc,QAAQ,IAClD,SAAS,uBACT;AACN,iBAAW,qBAAqB,cAAc,QAAQ,IAChD,SAAS,qBACT;AAAA,IACV;AACA,QAAK,MAAwC;AACzC,iBAAW,cAAc,cAAc,QAAQ,IACzC,SAAS,cACT;AAAA,IACV;AACA,UAAM,MAAM,kBAAkB,UAAU;AACxC,iBAAa,mBAAmB,GAAG;AACnC,WAAO;AAAA,EACX;AACA,aAAW,eAAe;AAC1B,uBAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAEnE,WAAS,wBAAwB;AAC7B,WAAO;AAAA,MACC,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACnB;AAAA,EAER;AAEA,QAAM,SAAS,SAAS;AAAA,IACpB,KAAK,MAAM,QAAQ;AAAA,IACnB,KAAK,SAAO;AACR,cAAQ,QAAQ;AAChB,eAAS,SAAS,QAAQ;AAAA,IAC9B;AAAA,EACJ,CAAC;AAED,QAAM,iBAAiB,SAAS;AAAA,IAC5B,KAAK,MAAM,gBAAgB;AAAA,IAC3B,KAAK,SAAO;AACR,sBAAgB,QAAQ;AACxB,eAAS,iBAAiB,gBAAgB;AAC1C,2BAAqB,UAAU,QAAQ,OAAO,GAAG;AAAA,IACrD;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,SAAS,MAAM,UAAU,KAAK;AAE/C,QAAM,kBAAiC,SAAS,MAAM,iBAAiB,KAAK;AAE5E,QAAM,gBAA+B,SAAS,MAAM,eAAe,KAAK;AAExE,WAAS,4BAA4B;AACjC,WAAO,WAAW,gBAAgB,IAAI,mBAAmB;AAAA,EAC7D;AAEA,WAAS,0BAA0B,SAAS;AACxC,uBAAmB;AACnB,aAAS,kBAAkB;AAAA,EAC/B;AAEA,WAAS,oBAAoB;AACzB,WAAO;AAAA,EACX;AAEA,WAAS,kBAAkB,SAAS;AAChC,QAAI,YAAY,MAAM;AAClB,wBAAkB,yBAAyB,OAAO;AAAA,IACtD;AACA,eAAW;AACX,aAAS,UAAU;AAAA,EACvB;AACA,WAAS,2BAA2B,MAAM,KACxC;AACE,WAAO,SAAS,eAAe,CAAC,IAAI;AAAA,EACxC;AACA,QAAM,eAAe,CAAC,IAAI,gBAAgB,UAAU,iBAAiB,cAAc,qBAAqB;AACpG,0BAAsB;AAEtB,QAAI;AACJ,QAAI;AACA,UAAK,MAAqE;AACtE,0BAAkB,YAAY,CAAC;AAAA,MACnC;AACA,UAAI,CAAC,WAAW;AACZ,iBAAS,kBAAkB,SACrB,mBAAmB,IACnB;AAAA,MACV;AACA,YAAM,GAAG,QAAQ;AAAA,IACrB,UACA;AACI,UAAK,MAAqE;AACtE,0BAAkB,IAAI;AAAA,MAC1B;AACA,UAAI,CAAC,WAAW;AACZ,iBAAS,kBAAkB;AAAA,MAC/B;AAAA,IACJ;AACA,QAAK,aAAa;AAAA,IACd,SAAS,GAAG,KACZ,QAAQ,gBACP,aAAa,sBAAsB,CAAC,KACvC;AACE,YAAM,CAAC,KAAK,IAAI,IAAI,eAAe;AACnC,UACI,UACA,SAAS,GAAG,KACZ,2BAA2B,UAAU,IAAI,GAAG;AAC5C,YAAI,kBACC,wBAAwB,eAAe,GAAG,KACvC,uBAAuB,cAAc,GAAG,IAAI;AAChD,eAAKK,gBAAe,cAAc,kBAAkB;AAAA,YAChD;AAAA,YACA,MAAM;AAAA,UACV,CAAC,CAAC;AAAA,QACN;AAEA,YAAK,MAAwC;AACzC,gBAAM,EAAE,aAAa,QAAQ,IAAI;AACjC,cAAI,WAAW,eAAe;AAC1B,oBAAQ,KAAK,YAAoD;AAAA,cAC7D,MAAM;AAAA,cACN;AAAA,cACA,IAAI;AAAA,cACJ,SAAS,GAAG,QAAQ,IAAI,GAAG;AAAA,YAC/B,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ;AACA,aAAO,UAAU,gBACX,gBAAgB,MAAM,IACtB,aAAa,GAAG;AAAA,IAC1B,WACS,iBAAiB,GAAG,GAAG;AAC5B,aAAO;AAAA,IACX,OACK;AAED,YAAM,gBAAgB,eAAe,sBAAsB;AAAA,IAC/D;AAAA,EACJ;AAEA,WAAS,KAAK,MAAM;AAChB,WAAO,aAAa,aAAW,QAAQ,MAAM,WAAW,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,mBAAmB,GAAG,IAAI,GAAG,aAAa,UAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,SAAO,KAAK,SAAO,SAAS,GAAG,CAAC;AAAA,EACvN;AAEA,WAAS,MAAM,MAAM;AACjB,UAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,QAAI,QAAQ,CAAC,SAAS,IAAI,GAAG;AACzB,YAAM,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,WAAO,EAAE,GAAG,CAAC,MAAM,MAAM,OAAO,EAAE,iBAAiB,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EAC3E;AAEA,WAAS,KAAK,MAAM;AAChB,WAAO,aAAa,aAAW,QAAQ,MAAM,UAAU,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,kBAAkB,GAAG,IAAI,GAAG,mBAAmB,UAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,uBAAuB,SAAO,SAAS,GAAG,CAAC;AAAA,EAC5O;AAEA,WAAS,KAAK,MAAM;AAChB,WAAO,aAAa,aAAW,QAAQ,MAAM,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,MAAM,gBAAgB,GAAG,IAAI,GAAG,iBAAiB,UAAQ,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM,uBAAuB,SAAO,SAAS,GAAG,CAAC;AAAA,EACtO;AAEA,WAAS,UAAU,QAAQ;AACvB,WAAO,OAAO,IAAI,SAAO,SAAS,GAAG,KAAK,SAAS,GAAG,KAAK,UAAU,GAAG,IAClE,eAAe,OAAO,GAAG,CAAC,IAC1B,GAAG;AAAA,EACb;AACA,QAAM,cAAc,CAAC,QAAQ;AAC7B,QAAM,YAAY;AAAA,IACd;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACV;AAEA,WAAS,kBAAkB,MAAM;AAC7B,WAAO;AAAA,MAAa,aAAW;AAC3B,YAAI;AACJ,cAAMM,YAAW;AACjB,YAAI;AACA,UAAAA,UAAS,YAAY;AACrB,gBAAM,QAAQ,MAAM,WAAW,MAAM,CAACA,WAAU,GAAG,IAAI,CAAC;AAAA,QAC5D,UACA;AACI,UAAAA,UAAS,YAAY;AAAA,QACzB;AACA,eAAO;AAAA,MACX;AAAA,MAAG,MAAM,mBAAmB,GAAG,IAAI;AAAA,MAAG;AAAA;AAAA,MAEtC,UAAQ,KAAK,oBAAoB,EAAE,GAAG,IAAI;AAAA,MAAG,SAAO,CAAC,eAAe,GAAG,CAAC;AAAA,MAAG,SAAO,QAAQ,GAAG;AAAA,IAAC;AAAA,EAClG;AAEA,WAAS,eAAe,MAAM;AAC1B,WAAO;AAAA,MAAa,aAAW,QAAQ,MAAM,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAAA,MAAG,MAAM,gBAAgB,GAAG,IAAI;AAAA,MAAG;AAAA;AAAA,MAEhH,UAAQ,KAAK,iBAAiB,EAAE,GAAG,IAAI;AAAA,MAAG;AAAA,MAAmB,SAAO,SAAS,GAAG,KAAK,QAAQ,GAAG;AAAA,IAAC;AAAA,EACrG;AAEA,WAAS,iBAAiB,MAAM;AAC5B,WAAO;AAAA,MAAa,aAAW,QAAQ,MAAM,UAAU,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAAA,MAAG,MAAM,kBAAkB,GAAG,IAAI;AAAA,MAAG;AAAA;AAAA,MAEpH,UAAQ,KAAK,mBAAmB,EAAE,GAAG,IAAI;AAAA,MAAG;AAAA,MAAmB,SAAO,SAAS,GAAG,KAAK,QAAQ,GAAG;AAAA,IAAC;AAAA,EACvG;AACA,WAAS,eAAe,OAAO;AAC3B,mBAAe;AACf,aAAS,cAAc;AAAA,EAC3B;AAEA,WAAS,GAAG,KAAKF,SAAQ;AACrB,WAAO,aAAa,MAAM;AACtB,UAAI,CAAC,KAAK;AACN,eAAO;AAAA,MACX;AACA,YAAM,eAAe,SAASA,OAAM,IAAIA,UAAS,QAAQ;AACzD,YAAM,UAAU,iBAAiB,YAAY;AAC7C,YAAM,WAAW,SAAS,gBAAgB,SAAS,GAAG;AACtD,aAAO,CAAC,2BACF,aAAa,QAAQ,KACnB,kBAAkB,QAAQ,KAC1B,SAAS,QAAQ,IACnB,YAAY;AAAA,IACtB,GAAG,MAAM,CAAC,GAAG,GAAG,oBAAoB,UAAQ;AACxC,aAAO,QAAQ,MAAM,KAAK,IAAI,MAAM,CAAC,KAAKA,OAAM,CAAC;AAAA,IACrD,GAAG,mBAAmB,SAAO,UAAU,GAAG,CAAC;AAAA,EAC/C;AACA,WAAS,gBAAgB,KAAK;AAC1B,QAAIG,YAAW;AACf,UAAM,UAAU,wBAAwB,UAAU,gBAAgB,OAAO,QAAQ,KAAK;AACtF,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,YAAM,uBAAuB,UAAU,MAAM,QAAQ,CAAC,CAAC,KAAK,CAAC;AAC7D,YAAM,eAAe,SAAS,gBAAgB,sBAAsB,GAAG;AACvE,UAAI,gBAAgB,MAAM;AACtB,QAAAA,YAAW;AACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAOA;AAAA,EACX;AAEA,WAAS,GAAG,KAAK;AACb,UAAMA,YAAW,gBAAgB,GAAG;AAEpC,WAAOA,aAAY,OACbA,YACA,SACI,OAAO,GAAG,GAAG,KAAK,CAAC,IACnB,CAAC;AAAA,EACf;AAEA,WAAS,iBAAiBH,SAAQ;AAC9B,WAAQ,UAAU,MAAMA,OAAM,KAAK,CAAC;AAAA,EACxC;AAEA,WAAS,iBAAiBA,SAAQ,SAAS;AACvC,QAAI,UAAU;AACV,YAAM,WAAW,EAAE,CAACA,OAAM,GAAG,QAAQ;AACrC,iBAAW,OAAO,UAAU;AACxB,YAAI,OAAO,UAAU,GAAG,GAAG;AACvB,yBAAe,SAAS,GAAG,CAAC;AAAA,QAChC;AAAA,MACJ;AACA,gBAAU,SAASA,OAAM;AAAA,IAC7B;AACA,cAAU,MAAMA,OAAM,IAAI;AAC1B,aAAS,WAAW,UAAU;AAAA,EAClC;AAEA,WAAS,mBAAmBA,SAAQ,SAAS;AACzC,cAAU,MAAMA,OAAM,IAAI,UAAU,MAAMA,OAAM,KAAK,CAAC;AACtD,UAAM,WAAW,EAAE,CAACA,OAAM,GAAG,QAAQ;AACrC,QAAI,UAAU;AACV,iBAAW,OAAO,UAAU;AACxB,YAAI,OAAO,UAAU,GAAG,GAAG;AACvB,yBAAe,SAAS,GAAG,CAAC;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AACA,cAAU,SAASA,OAAM;AACzB,aAAS,SAAS,UAAU,MAAMA,OAAM,CAAC;AACzC,aAAS,WAAW,UAAU;AAAA,EAClC;AAEA,WAAS,kBAAkBA,SAAQ;AAC/B,WAAO,iBAAiB,MAAMA,OAAM,KAAK,CAAC;AAAA,EAC9C;AAEA,WAAS,kBAAkBA,SAAQI,SAAQ;AACvC,qBAAiB,MAAMJ,OAAM,IAAII;AACjC,aAAS,kBAAkB,iBAAiB;AAC5C,wBAAoB,UAAUJ,SAAQI,OAAM;AAAA,EAChD;AAEA,WAAS,oBAAoBJ,SAAQI,SAAQ;AACzC,qBAAiB,MAAMJ,OAAM,IAAI,OAAO,iBAAiB,MAAMA,OAAM,KAAK,CAAC,GAAGI,OAAM;AACpF,aAAS,kBAAkB,iBAAiB;AAC5C,wBAAoB,UAAUJ,SAAQI,OAAM;AAAA,EAChD;AAEA,WAAS,gBAAgBJ,SAAQ;AAC7B,WAAO,eAAe,MAAMA,OAAM,KAAK,CAAC;AAAA,EAC5C;AAEA,WAAS,gBAAgBA,SAAQI,SAAQ;AACrC,mBAAe,MAAMJ,OAAM,IAAII;AAC/B,aAAS,gBAAgB,eAAe;AACxC,sBAAkB,UAAUJ,SAAQI,OAAM;AAAA,EAC9C;AAEA,WAAS,kBAAkBJ,SAAQI,SAAQ;AACvC,mBAAe,MAAMJ,OAAM,IAAI,OAAO,eAAe,MAAMA,OAAM,KAAK,CAAC,GAAGI,OAAM;AAChF,aAAS,gBAAgB,eAAe;AACxC,sBAAkB,UAAUJ,SAAQI,OAAM;AAAA,EAC9C;AAEA;AAEA,MAAI,UAAU,WAAW;AACrB,UAAM,OAAO,QAAQ,CAAC,QAAQ;AAC1B,UAAI,gBAAgB;AAChB,gBAAQ,QAAQ;AAChB,iBAAS,SAAS;AAClB,6BAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,MACvE;AAAA,IACJ,CAAC;AACD,UAAM,OAAO,gBAAgB,CAAC,QAAQ;AAClC,UAAI,gBAAgB;AAChB,wBAAgB,QAAQ;AACxB,iBAAS,iBAAiB;AAC1B,6BAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,MACvE;AAAA,IACJ,CAAC;AAAA,EACL;AAEA,QAAM,WAAW;AAAA,IACb,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA,IAAI,gBAAgB;AAChB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,cAAc,KAAK;AACnB,uBAAiB;AACjB,UAAI,OAAO,QAAQ;AACf,gBAAQ,QAAQ,OAAO,OAAO;AAC9B,wBAAgB,QAAQ,OAAO,eAAe;AAC9C,6BAAqB,UAAU,QAAQ,OAAO,gBAAgB,KAAK;AAAA,MACvE;AAAA,IACJ;AAAA,IACA,IAAI,mBAAmB;AACnB,aAAO,OAAO,KAAK,UAAU,KAAK,EAAE,KAAK;AAAA,IAC7C;AAAA,IACA;AAAA,IACA,IAAI,YAAY;AACZ,aAAO;AAAA,IACX;AAAA,IACA,IAAI,cAAc;AACd,aAAO,gBAAgB,CAAC;AAAA,IAC5B;AAAA,IACA,IAAI,WAAW;AACX,aAAO;AAAA,IACX;AAAA,IACA,IAAI,cAAc;AACd,aAAO;AAAA,IACX;AAAA,IACA,IAAI,YAAY,KAAK;AACjB,qBAAe;AACf,eAAS,cAAc;AAAA,IAC3B;AAAA,IACA,IAAI,eAAe;AACf,aAAO;AAAA,IACX;AAAA,IACA,IAAI,aAAa,KAAK;AAClB,sBAAgB;AAChB,eAAS,eAAe;AAAA,IAC5B;AAAA,IACA,IAAI,eAAe;AACf,aAAO;AAAA,IACX;AAAA,IACA,IAAI,aAAa,KAAK;AAClB,sBAAgB;AAAA,IACpB;AAAA,IACA,IAAI,iBAAiB;AACjB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,eAAe,KAAK;AACpB,wBAAkB;AAClB,eAAS,iBAAiB;AAAA,IAC9B;AAAA,IACA,IAAI,kBAAkB;AAClB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,gBAAgB,KAAK;AACrB,yBAAmB;AACnB,eAAS,kBAAkB;AAAA,IAC/B;AAAA,IACA,IAAI,kBAAkB;AAClB,aAAO;AAAA,IACX;AAAA,IACA,IAAI,gBAAgB,KAAK;AACrB,yBAAmB;AACnB,eAAS,kBAAkB;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,CAAC,oBAAoB,GAAG;AAAA,EAC5B;AACA;AACI,aAAS,kBAAkB;AAC3B,aAAS,gBAAgB;AACzB,aAAS,KAAK;AACd,aAAS,KAAK;AACd,aAAS,KAAK;AACd,aAAS,IAAI;AACb,aAAS,IAAI;AACb,aAAS,oBAAoB;AAC7B,aAAS,oBAAoB;AAC7B,aAAS,sBAAsB;AAC/B,aAAS,kBAAkB;AAC3B,aAAS,kBAAkB;AAC3B,aAAS,oBAAoB;AAC7B,aAAS,sBAAsB,IAAI;AACnC,aAAS,oBAAoB,IAAI;AACjC,aAAS,mBAAmB,IAAI;AAChC,aAAS,iBAAiB,IAAI;AAAA,EAClC;AAEA,MAAK,MAAwC;AACzC,aAAS,aAAa,IAAI,CAAC,YAAY;AACnC,eAAS,cAAc;AAAA,IAC3B;AACA,aAAS,cAAc,IAAI,MAAM;AAC7B,eAAS,cAAc;AAAA,IAC3B;AAAA,EACJ;AACA,SAAO;AACX;AASA,SAAS,uBAAuB,SAAS;AACrC,QAAM,SAAS,SAAS,QAAQ,MAAM,IAAI,QAAQ,SAAS;AAC3D,QAAM,iBAAiB,SAAS,QAAQ,cAAc,KAClD,QAAQ,QAAQ,cAAc,KAC9B,cAAc,QAAQ,cAAc,KACpC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR;AACN,QAAM,UAAU,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAChE,QAAM,cAAc,UAAU,QAAQ,qBAAqB,KACvD,SAAS,QAAQ,qBAAqB,IACpC,CAAC,QAAQ,wBACT;AACN,QAAM,eAAe,UAAU,QAAQ,kBAAkB,KACrD,SAAS,QAAQ,kBAAkB,IACjC,CAAC,QAAQ,qBACT;AACN,QAAM,eAAe,UAAU,QAAQ,YAAY,IAC7C,QAAQ,eACR;AACN,QAAM,iBAAiB,CAAC,CAAC,QAAQ;AACjC,QAAM,YAAY,cAAc,QAAQ,SAAS,IAAI,QAAQ,YAAY,CAAC;AAC1E,QAAM,qBAAqB,QAAQ;AACnC,QAAM,kBAAkB,WAAW,QAAQ,eAAe,IACpD,QAAQ,kBACR;AACN,QAAM,kBAAkB,SAAS,QAAQ,iBAAiB,IACpD,QAAQ,sBAAsB,QAC9B;AACN,QAAM,kBAAkB,CAAC,CAAC,QAAQ;AAClC,QAAM,gBAAgB,UAAU,QAAQ,IAAI,IAAI,QAAQ,OAAO;AAC/D,MAA+C,QAAQ,WAAW;AAC9D,SAAKR,gBAAe,cAAc,uBAAuB,CAAC;AAAA,EAC9D;AACA,MAA+C,QAAQ,0BAA0B;AAC7E,SAAKA,gBAAe,cAAc,gCAAgC,CAAC;AAAA,EACvE;AACA,MAAI,WAAW,QAAQ;AACvB,MAAI,cAAc,QAAQ,cAAc,GAAG;AACvC,UAAM,iBAAiB,QAAQ;AAC/B,UAAM,UAAU,OAAO,KAAK,cAAc;AAC1C,eAAW,QAAQ,OAAO,CAACO,WAAUH,YAAW;AAC5C,YAAM,UAAUG,UAASH,OAAM,MAAMG,UAASH,OAAM,IAAI,CAAC;AACzD,aAAO,SAAS,eAAeA,OAAM,CAAC;AACtC,aAAOG;AAAA,IACX,GAAI,YAAY,CAAC,CAAE;AAAA,EACvB;AACA,QAAM,EAAE,QAAQ,QAAQ,mBAAmB,IAAI;AAC/C,QAAM,kBAAkB,QAAQ;AAChC,QAAM,gBAAgB,QAAQ;AAC9B,QAAM,WAAW,QAAQ;AACzB,QAAM,2BAA2B,QAC5B;AACL,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,iBAAiB,QAAQ;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAOA,SAAS,cAAc,UAAU,CAAC,GAAG,eAAe;AAChD;AACI,UAAM,WAAW,eAAe,uBAAuB,OAAO,CAAC;AAC/D,UAAM,EAAE,WAAW,IAAI;AAEvB,UAAM,UAAU;AAAA;AAAA,MAEZ,IAAI,SAAS;AAAA;AAAA,MAEb,IAAI,SAAS;AACT,eAAO,SAAS,OAAO;AAAA,MAC3B;AAAA,MACA,IAAI,OAAO,KAAK;AACZ,iBAAS,OAAO,QAAQ;AAAA,MAC5B;AAAA;AAAA,MAEA,IAAI,iBAAiB;AACjB,eAAO,SAAS,eAAe;AAAA,MACnC;AAAA,MACA,IAAI,eAAe,KAAK;AACpB,iBAAS,eAAe,QAAQ;AAAA,MACpC;AAAA;AAAA,MAEA,IAAI,WAAW;AACX,eAAO,SAAS,SAAS;AAAA,MAC7B;AAAA;AAAA,MAEA,IAAI,kBAAkB;AAClB,eAAO,SAAS,gBAAgB;AAAA,MACpC;AAAA;AAAA,MAEA,IAAI,gBAAgB;AAChB,eAAO,SAAS,cAAc;AAAA,MAClC;AAAA;AAAA,MAEA,IAAI,mBAAmB;AACnB,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA,MAEA,IAAI,YAAY;AACZ,QAA2C,KAAKP,gBAAe,cAAc,uBAAuB,CAAC;AAErG,eAAO;AAAA,UACH,cAAc;AACV,mBAAO,CAAC;AAAA,UACZ;AAAA,QACJ;AAAA,MACJ;AAAA,MACA,IAAI,UAAU,KAAK;AACf,QAA2C,KAAKA,gBAAe,cAAc,uBAAuB,CAAC;AAAA,MACzG;AAAA;AAAA,MAEA,IAAI,UAAU;AACV,eAAO,SAAS,kBAAkB;AAAA,MACtC;AAAA,MACA,IAAI,QAAQ,SAAS;AACjB,iBAAS,kBAAkB,OAAO;AAAA,MACtC;AAAA;AAAA,MAEA,IAAI,wBAAwB;AACxB,eAAO,UAAU,SAAS,WAAW,IAC/B,CAAC,SAAS,cACV,SAAS;AAAA,MACnB;AAAA,MACA,IAAI,sBAAsB,KAAK;AAC3B,iBAAS,cAAc,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,MACnD;AAAA;AAAA,MAEA,IAAI,qBAAqB;AACrB,eAAO,UAAU,SAAS,YAAY,IAChC,CAAC,SAAS,eACV,SAAS;AAAA,MACnB;AAAA,MACA,IAAI,mBAAmB,KAAK;AACxB,iBAAS,eAAe,UAAU,GAAG,IAAI,CAAC,MAAM;AAAA,MACpD;AAAA;AAAA,MAEA,IAAI,YAAY;AACZ,eAAO,SAAS;AAAA,MACpB;AAAA;AAAA,MAEA,IAAI,yBAAyB;AACzB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,IAAI,uBAAuB,KAAK;AAC5B,iBAAS,iBAAiB;AAAA,MAC9B;AAAA;AAAA,MAEA,IAAI,kBAAkB;AAClB,eAAO,SAAS,0BAA0B;AAAA,MAC9C;AAAA,MACA,IAAI,gBAAgB,SAAS;AACzB,iBAAS,0BAA0B,OAAO;AAAA,MAC9C;AAAA;AAAA,MAEA,IAAI,OAAO;AACP,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,IAAI,KAAK,KAAK;AACV,iBAAS,gBAAgB;AAAA,MAC7B;AAAA;AAAA,MAEA,IAAI,oBAAoB;AACpB,eAAO,SAAS,kBAAkB,SAAS;AAAA,MAC/C;AAAA,MACA,IAAI,kBAAkB,KAAK;AACvB,iBAAS,kBAAkB,QAAQ;AAAA,MACvC;AAAA;AAAA,MAEA,IAAI,sBAAsB;AACtB,eAAO,SAAS;AAAA,MACpB;AAAA,MACA,IAAI,oBAAoB,KAAK;AACzB,iBAAS,kBAAkB;AAAA,MAC/B;AAAA;AAAA,MAEA,IAAI,2BAA2B;AAC3B,QACI,KAAKA,gBAAe,cAAc,gCAAgC,CAAC;AACvE,eAAO;AAAA,MACX;AAAA,MACA,IAAI,yBAAyB,KAAK;AAC9B,QACI,KAAKA,gBAAe,cAAc,gCAAgC,CAAC;AAAA,MAC3E;AAAA;AAAA,MAEA,IAAI,qBAAqB;AACrB,eAAO,SAAS,eAAe,CAAC;AAAA,MACpC;AAAA;AAAA,MAEA,YAAY;AAAA;AAAA,MAEZ,KAAK,MAAM;AACP,cAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,cAAMS,WAAU,CAAC;AACjB,YAAI,OAAO;AACX,YAAI,QAAQ;AACZ,YAAI,CAAC,SAAS,IAAI,GAAG;AACjB,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,cAAM,MAAM;AACZ,YAAI,SAAS,IAAI,GAAG;AAChB,UAAAA,SAAQ,SAAS;AAAA,QACrB,WACS,QAAQ,IAAI,GAAG;AACpB,iBAAO;AAAA,QACX,WACS,cAAc,IAAI,GAAG;AAC1B,kBAAQ;AAAA,QACZ;AACA,YAAI,QAAQ,IAAI,GAAG;AACf,iBAAO;AAAA,QACX,WACS,cAAc,IAAI,GAAG;AAC1B,kBAAQ;AAAA,QACZ;AAEA,eAAO,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,UACvC;AAAA,UACC,QAAQ,SAAS,CAAC;AAAA,UACnBA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA,MACA,MAAM,MAAM;AACR,eAAO,QAAQ,MAAM,SAAS,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,MACzD;AAAA;AAAA,MAEA,MAAM,MAAM;AACR,cAAM,CAAC,MAAM,MAAM,IAAI,IAAI;AAC3B,cAAMA,WAAU,EAAE,QAAQ,EAAE;AAC5B,YAAI,OAAO;AACX,YAAI,QAAQ;AACZ,YAAI,CAAC,SAAS,IAAI,GAAG;AACjB,gBAAM,gBAAgB,eAAe,gBAAgB;AAAA,QACzD;AACA,cAAM,MAAM;AACZ,YAAI,SAAS,IAAI,GAAG;AAChB,UAAAA,SAAQ,SAAS;AAAA,QACrB,WACS,SAAS,IAAI,GAAG;AACrB,UAAAA,SAAQ,SAAS;AAAA,QACrB,WACS,QAAQ,IAAI,GAAG;AACpB,iBAAO;AAAA,QACX,WACS,cAAc,IAAI,GAAG;AAC1B,kBAAQ;AAAA,QACZ;AACA,YAAI,SAAS,IAAI,GAAG;AAChB,UAAAA,SAAQ,SAAS;AAAA,QACrB,WACS,QAAQ,IAAI,GAAG;AACpB,iBAAO;AAAA,QACX,WACS,cAAc,IAAI,GAAG;AAC1B,kBAAQ;AAAA,QACZ;AAEA,eAAO,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,UACvC;AAAA,UACC,QAAQ,SAAS,CAAC;AAAA,UACnBA;AAAA,QACJ,CAAC;AAAA,MACL;AAAA;AAAA,MAEA,GAAG,KAAK,QAAQ;AACZ,eAAO,SAAS,GAAG,KAAK,MAAM;AAAA,MAClC;AAAA;AAAA,MAEA,GAAG,KAAK;AACJ,eAAO,SAAS,GAAG,GAAG;AAAA,MAC1B;AAAA;AAAA,MAEA,iBAAiB,QAAQ;AACrB,eAAO,SAAS,iBAAiB,MAAM;AAAA,MAC3C;AAAA;AAAA,MAEA,iBAAiB,QAAQ,SAAS;AAC9B,iBAAS,iBAAiB,QAAQ,OAAO;AAAA,MAC7C;AAAA;AAAA,MAEA,mBAAmB,QAAQ,SAAS;AAChC,iBAAS,mBAAmB,QAAQ,OAAO;AAAA,MAC/C;AAAA;AAAA,MAEA,KAAK,MAAM;AACP,eAAO,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,MACxD;AAAA;AAAA,MAEA,kBAAkB,QAAQ;AACtB,eAAO,SAAS,kBAAkB,MAAM;AAAA,MAC5C;AAAA;AAAA,MAEA,kBAAkB,QAAQD,SAAQ;AAC9B,iBAAS,kBAAkB,QAAQA,OAAM;AAAA,MAC7C;AAAA;AAAA,MAEA,oBAAoB,QAAQA,SAAQ;AAChC,iBAAS,oBAAoB,QAAQA,OAAM;AAAA,MAC/C;AAAA;AAAA,MAEA,KAAK,MAAM;AACP,eAAO,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC;AAAA,MACxD;AAAA;AAAA,MAEA,gBAAgB,QAAQ;AACpB,eAAO,SAAS,gBAAgB,MAAM;AAAA,MAC1C;AAAA;AAAA,MAEA,gBAAgB,QAAQA,SAAQ;AAC5B,iBAAS,gBAAgB,QAAQA,OAAM;AAAA,MAC3C;AAAA;AAAA,MAEA,kBAAkB,QAAQA,SAAQ;AAC9B,iBAAS,kBAAkB,QAAQA,OAAM;AAAA,MAC7C;AAAA;AAAA;AAAA,MAGA,eAAe,QAAQ,eAAe;AAClC,QACI,KAAKR,gBAAe,cAAc,8BAA8B,CAAC;AACrE,eAAO;AAAA,MACX;AAAA,IACJ;AACA,YAAQ,aAAa;AAErB,QAAK,MAAwC;AACzC,cAAQ,kBAAkB,CAAC,YAAY;AACnC,cAAM,aAAa;AACnB,mBAAW,aAAa,KAAK,WAAW,aAAa,EAAE,OAAO;AAAA,MAClE;AACA,cAAQ,mBAAmB,MAAM;AAC7B,cAAM,aAAa;AACnB,mBAAW,cAAc,KAAK,WAAW,cAAc,EAAE;AAAA,MAC7D;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAGA,IAAM,kBAAkB;AAAA,EACpB,KAAK;AAAA,IACD,MAAM,CAAC,QAAQ,MAAM;AAAA,EACzB;AAAA,EACA,QAAQ;AAAA,IACJ,MAAM;AAAA,EACV;AAAA,EACA,OAAO;AAAA,IACH,MAAM;AAAA;AAAA,IAEN,WAAW,CAAC,QAAiC,QAAQ,YAAY,QAAQ;AAAA,IACzE,SAAS;AAAA;AAAA,EACb;AAAA,EACA,MAAM;AAAA,IACF,MAAM;AAAA,EACV;AACJ;AAEA,SAAS,kBAET,EAAE,MAAM,GACR,MAAM;AACF,MAAI,KAAK,WAAW,KAAK,KAAK,CAAC,MAAM,WAAW;AAE5C,UAAM,MAAM,MAAM,UAAU,MAAM,QAAQ,IAAI,CAAC;AAE/C,WAAO,IAAI,OAAO,CAAC,MAAM,YAAY;AACjC,aAAO;AAAA,QACH,GAAG;AAAA;AAAA,QAEH,GAAI,QAAQ,SAAS,WAAW,QAAQ,WAAW,CAAC,OAAO;AAAA,MAE/D;AAAA,IACJ,GAAG,CAAC,CAAC;AAAA,EACT,OACK;AAED,WAAO,KAAK,OAAO,CAAC,KAAK,QAAQ;AAC7B,YAAM,OAAO,MAAM,GAAG;AACtB,UAAI,MAAM;AACN,YAAI,GAAG,IAAI,KAAK;AAAA,MACpB;AACA,aAAO;AAAA,IACX,GAAG,OAAO,CAAC;AAAA,EACf;AACJ;AAEA,SAAS,mBAAmB,KAAK;AAC7B,SAAO;AACX;AAEA,IAAM,kBAAgC,gBAAgB;AAAA;AAAA,EAElD,MAAM;AAAA,EACN,OAAO,OAAO;AAAA,IACV,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA;AAAA,MAErB,WAAW,CAAC,QAAQ,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG;AAAA,IACnD;AAAA,EACJ,GAAG,eAAe;AAAA;AAAA;AAAA,EAGlB,MAAM,OAAO,SAAS;AAClB,UAAM,EAAE,OAAO,MAAM,IAAI;AAEzB,UAAM,OAAO,MAAM,QACf,QAAQ;AAAA,MACJ,UAAU,MAAM;AAAA,MAChB,gBAAgB;AAAA,IACpB,CAAC;AACL,WAAO,MAAM;AACT,YAAM,OAAO,OAAO,KAAK,KAAK,EAAE,OAAO,SAAO,QAAQ,GAAG;AACzD,YAAM,UAAU,OAAO;AACvB,UAAI,MAAM,QAAQ;AACd,gBAAQ,SAAS,MAAM;AAAA,MAC3B;AACA,UAAI,MAAM,WAAW,QAAW;AAC5B,gBAAQ,SAAS,SAAS,MAAM,MAAM,IAAI,CAAC,MAAM,SAAS,MAAM;AAAA,MACpE;AACA,YAAM,MAAM,kBAAkB,SAAS,IAAI;AAE3C,YAAM,WAAW,KAAK,oBAAoB,EAAE,MAAM,SAAS,KAAK,OAAO;AACvE,YAAM,gBAAgB,OAAO,OAAO,GAAG,KAAK;AAC5C,YAAM,MAAM,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,IAC/C,MAAM,MACN,mBAAmB;AACzB,aAAO,EAAE,KAAK,eAAe,QAAQ;AAAA,IACzC;AAAA,EACJ;AACJ,CAAC;AAsDD,IAAM,cAAc;AACpB,IAAM,QAAQ;AAEd,SAAS,QAAQ,QAAQ;AACrB,SAAO,QAAQ,MAAM,KAAK,CAAC,SAAS,OAAO,CAAC,CAAC;AACjD;AACA,SAAS,gBAAgB,OAAO,SAAS,UAAU,eAAe;AAC9D,QAAM,EAAE,OAAO,MAAM,IAAI;AACzB,SAAO,MAAM;AACT,UAAM,UAAU,EAAE,MAAM,KAAK;AAC7B,QAAI,YAAY,OAAO;AACvB,QAAI,MAAM,QAAQ;AACd,cAAQ,SAAS,MAAM;AAAA,IAC3B;AACA,QAAI,SAAS,MAAM,MAAM,GAAG;AACxB,cAAQ,MAAM,MAAM;AAAA,IACxB,WACS,SAAS,MAAM,MAAM,GAAG;AAE7B,UAAI,SAAS,MAAM,OAAO,GAAG,GAAG;AAE5B,gBAAQ,MAAM,MAAM,OAAO;AAAA,MAC/B;AAEA,kBAAY,OAAO,KAAK,MAAM,MAAM,EAAE,OAAO,CAACS,UAAS,SAAS;AAC5D,eAAO,SAAS,SAAS,IAAI,IACvB,OAAO,OAAO,GAAGA,UAAS,EAAE,CAAC,IAAI,GAAG,MAAM,OAAO,IAAI,EAAE,CAAC,IACxDA;AAAA,MACV,GAAG,OAAO,CAAC;AAAA,IACf;AACA,UAAM,QAAQ,cAAc,GAAG,CAAC,MAAM,OAAO,SAAS,SAAS,CAAC;AAChE,QAAI,WAAW,CAAC,QAAQ,GAAG;AAC3B,QAAI,QAAQ,KAAK,GAAG;AAChB,iBAAW,MAAM,IAAI,CAAC,MAAM,UAAU;AAClC,cAAM,OAAO,MAAM,KAAK,IAAI;AAC5B,cAAM,OAAO,OACP,KAAK,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,OAAO,OAAO,MAAM,CAAC,IAC9C,CAAC,KAAK,KAAK;AACjB,YAAI,QAAQ,IAAI,GAAG;AACf,eAAK,CAAC,EAAE,MAAM,GAAG,KAAK,IAAI,IAAI,KAAK;AAAA,QACvC;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL,WACS,SAAS,KAAK,GAAG;AACtB,iBAAW,CAAC,KAAK;AAAA,IACrB;AACA,UAAM,gBAAgB,OAAO,OAAO,GAAG,KAAK;AAC5C,UAAM,MAAM,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,GAAG,IAC/C,MAAM,MACN,mBAAmB;AACzB,WAAO,EAAE,KAAK,eAAe,QAAQ;AAAA,EACzC;AACJ;AAEA,IAAM,mBAAiC,gBAAgB;AAAA;AAAA,EAEnD,MAAM;AAAA,EACN,OAAO,OAAO;AAAA,IACV,OAAO;AAAA,MACH,MAAM;AAAA,MACN,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,EACJ,GAAG,eAAe;AAAA;AAAA;AAAA,EAGlB,MAAM,OAAO,SAAS;AAClB,UAAM,OAAO,MAAM,QACf,QAAQ;AAAA,MACJ,UAAU,MAAM;AAAA,MAChB,gBAAgB;AAAA,IACpB,CAAC;AACL,WAAO,gBAAgB,OAAO,SAAS,4BAA4B,IAAI;AAAA;AAAA,MAEvE,KAAK,iBAAiB,EAAE,GAAG,IAAI;AAAA,KAAC;AAAA,EACpC;AACJ,CAAC;AAsBD,IAAM,eAAe;AACrB,IAAM,QAAQ;AAEd,IAAM,qBAAoC,gBAAgB;AAAA;AAAA,EAEtD,MAAM;AAAA,EACN,OAAO,OAAO;AAAA,IACV,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,IAAI;AAAA,MACnB,UAAU;AAAA,IACd;AAAA,IACA,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,IACzB;AAAA,EACJ,GAAG,eAAe;AAAA;AAAA;AAAA,EAGlB,MAAM,OAAO,SAAS;AAClB,UAAM,OAAO,MAAM,QACf,QAAQ;AAAA,MACJ,UAAU,MAAM;AAAA,MAChB,gBAAgB;AAAA,IACpB,CAAC;AACL,WAAO,gBAAgB,OAAO,SAAS,8BAA8B,IAAI;AAAA;AAAA,MAEzE,KAAK,mBAAmB,EAAE,GAAG,IAAI;AAAA,KAAC;AAAA,EACtC;AACJ,CAAC;AAkBD,IAAM,iBAAiB;AACvB,IAAM,QAAQ;AAEd,SAAS,cAAc,MAAM,UAAU;AACnC,QAAM,eAAe;AACrB,MAAI,KAAK,SAAS,eAAe;AAC7B,WAAQ,aAAa,cAAc,QAAQ,KAAK,KAAK;AAAA,EACzD,OACK;AACD,UAAM,UAAU,aAAa,cAAc,QAAQ;AACnD,WAAO,WAAW,OACZ,QAAQ,aACR,KAAK,OAAO;AAAA,EACtB;AACJ;AACA,SAAS,YAAY,MAAM;AACvB,QAAM,WAAW,CAAC,YAAY;AAC1B,UAAM,EAAE,UAAU,WAAW,MAAM,IAAI;AAEvC,QAAI,CAAC,YAAY,CAAC,SAAS,GAAG;AAC1B,YAAM,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,UAAM,WAAW,cAAc,MAAM,SAAS,CAAC;AAC/C,QAA+C,UAAU,UAAU;AAC/D,WAAKT,gBAAe,cAAc,sBAAsB,CAAC;AAAA,IAC7D;AACA,UAAM,cAAc,WAAW,KAAK;AACpC,WAAO;AAAA,MACH,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,WAAW,WAAW,CAAC,CAAC;AAAA,MAChE;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,WAAW,CAAC,IAAI,YAAY;AAC9B,UAAM,CAAC,aAAa,QAAQ,IAAI,SAAS,OAAO;AAChD,QAAI,aAAa,KAAK,WAAW,UAAU;AAEvC,SAAG,gBAAgB,MAAM,SAAS,QAAQ,MAAM;AAC5C,gBAAQ,YAAY,QAAQ,SAAS,aAAa;AAAA,MACtD,CAAC;AAAA,IACL;AACA,OAAG,aAAa;AAChB,OAAG,cAAc;AAAA,EACrB;AACA,QAAM,aAAa,CAAC,OAAO;AACvB,QAAI,aAAa,GAAG,eAAe;AAC/B,SAAG,cAAc;AACjB,SAAG,gBAAgB;AACnB,aAAO,GAAG;AAAA,IACd;AACA,QAAI,GAAG,YAAY;AACf,SAAG,aAAa;AAChB,aAAO,GAAG;AAAA,IACd;AAAA,EACJ;AACA,QAAM,SAAS,CAAC,IAAI,EAAE,MAAM,MAAM;AAC9B,QAAI,GAAG,YAAY;AACf,YAAM,WAAW,GAAG;AACpB,YAAM,cAAc,WAAW,KAAK;AACpC,SAAG,cAAc,QAAQ,MAAM,SAAS,GAAG,UAAU;AAAA,QACjD,GAAG,WAAW,WAAW;AAAA,MAC7B,CAAC;AAAA,IACL;AAAA,EACJ;AACA,QAAM,cAAc,CAAC,YAAY;AAC7B,UAAM,CAAC,WAAW,IAAI,SAAS,OAAO;AACtC,WAAO,EAAE,YAAY;AAAA,EACzB;AACA,SAAO;AAAA,IACH,SAAS;AAAA,IACT,WAAW;AAAA,IACX,cAAc;AAAA,IACd;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,MAAI,SAAS,KAAK,GAAG;AACjB,WAAO,EAAE,MAAM,MAAM;AAAA,EACzB,WACS,cAAc,KAAK,GAAG;AAC3B,QAAI,EAAE,UAAU,QAAQ;AACpB,YAAM,gBAAgB,eAAe,gBAAgB,MAAM;AAAA,IAC/D;AACA,WAAO;AAAA,EACX,OACK;AACD,UAAM,gBAAgB,eAAe,aAAa;AAAA,EACtD;AACJ;AACA,SAAS,WAAW,OAAO;AACvB,QAAM,EAAE,MAAM,QAAQ,MAAM,QAAQ,OAAO,IAAI;AAC/C,QAAM,UAAU,CAAC;AACjB,QAAM,QAAQ,QAAQ,CAAC;AACvB,MAAI,SAAS,MAAM,GAAG;AAClB,YAAQ,SAAS;AAAA,EACrB;AACA,MAAI,SAAS,MAAM,GAAG;AAClB,YAAQ,SAAS;AAAA,EACrB;AACA,MAAI,SAAS,MAAM,GAAG;AAClB,YAAQ,SAAS;AAAA,EACrB;AACA,SAAO,CAAC,MAAM,OAAO,OAAO;AAChC;AAEA,SAAS,MAAM,KAAK,SAAS,SAAS;AAClC,QAAM,gBAAgB,cAAc,QAAQ,CAAC,CAAC,IACxC,QAAQ,CAAC,IACT,CAAC;AACP,QAAM,uBAAuB,CAAC,CAAC,cAAc;AAC7C,QAAM,gBAAgB,UAAU,cAAc,aAAa,IACrD,cAAc,gBACd;AACN,MAA+C,iBAAiB,sBAAsB;AAClF,SAAKA,gBAAe,cAAc,kCAAkC;AAAA,MAChE,MAAM,YAAY;AAAA,IACtB,CAAC,CAAC;AAAA,EACN;AACA,MAAI,eAAe;AACf,KAAC,CAAC,uBAAuB,YAAY,OAAO,QAAQ,OAAO,EAAE,QAAQ,UAAQ,IAAI,UAAU,MAAM,WAAW,CAAC;AAC7G,KAAC,aAAa,MAAM,OAAO,EAAE,QAAQ,UAAQ,IAAI,UAAU,MAAM,YAAY,CAAC;AAC9E,KAAC,eAAe,MAAM,OAAO,EAAE,QAAQ,UAAQ,IAAI,UAAU,MAAM,cAAc,CAAC;AAAA,EACtF;AAEA;AACI,QAAI,UAAU,KAAK,YAAY,IAAI,CAAC;AAAA,EACxC;AACJ;AAEA,IAAM,oBAAoB;AAAA,EACtB;AAAA,IAAC;AAAA;AAAA,EAA0D,GAAG;AAAA,EAC9D;AAAA,IAAC;AAAA;AAAA,EAAmE,GAAG;AAAA,EACvE;AAAA,IAAC;AAAA;AAAA,EAAiD,GAAG;AACzD;AACA,IAAM,0BAA0B;AAAA,EAC5B;AAAA,IAAC;AAAA;AAAA,EAAmE,GAAG;AAC3E;AACA,IAAM,4BAA4B;AAAA,EAC9B;AAAA,IAAC;AAAA;AAAA,EAAiD,GAAG;AACzD;AAEA,IAAM,2BAA2B;AACjC,IAAI;AACJ,eAAe,eAAe,KAAK,MAAM;AACrC,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,QAAI;AACA,0BAAoB;AAAA,QAChB,IAAI;AAAA,QACJ,OAAO;AAAA,UAAkB;AAAA;AAAA,QAA0D;AAAA,QACnF,aAAa;AAAA,QACb,UAAU;AAAA,QACV,MAAM;AAAA,QACN,qBAAqB,CAAC,wBAAwB;AAAA,QAC9C;AAAA;AAAA,MACJ,GAAG,SAAO;AACN,sBAAc;AACd,YAAI,GAAG,mBAAmB,CAAC,EAAE,mBAAmB,SAAS,MAAM;AAC3D,kCAAwB,mBAAmB,UAAU,IAAI;AAAA,QAC7D,CAAC;AACD,YAAI,GAAG,iBAAiB,CAAC,EAAE,mBAAmB,aAAa,MAAM;AAC7D,cAAI,kBAAkB,MAAM,MACxB,kBAAkB,MAAM,GAAG,gBAC3B,cAAc;AACd,gBAAI,KAAK,SAAS,UAAU;AAExB,kBAAI,kBAAkB,MAAM,GAAG,iBAC3B,KAAK,OAAO,YAAY;AACxB,gCAAgB,cAAc,kBAAkB,MAAM,GAAG,YAAY;AAAA,cACzE;AAAA,YACJ,OACK;AACD,8BAAgB,cAAc,kBAAkB,MAAM,GAAG,YAAY;AAAA,YACzE;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,YAAI,aAAa;AAAA,UACb,IAAI;AAAA,UACJ,OAAO;AAAA,YAAkB;AAAA;AAAA,UAAmE;AAAA,UAC5F,MAAM;AAAA,UACN,uBAAuB;AAAA,YAAwB;AAAA;AAAA,UAAmE;AAAA,QACtH,CAAC;AACD,YAAI,GAAG,iBAAiB,aAAW;AAC/B,cAAI,QAAQ,QAAQ,OAChB,QAAQ,gBAAgB,+BAAqE;AAC7F,0BAAc,SAAS,IAAI;AAAA,UAC/B;AAAA,QACJ,CAAC;AACD,cAAM,QAAQ,oBAAI,IAAI;AACtB,YAAI,GAAG,kBAAkB,OAAO,YAAY;AACxC,cAAI,QAAQ,QAAQ,OAChB,QAAQ,gBAAgB,+BAAqE;AAC7F,gBAAI,mBAAmB;AACvB,yBAAa,SAAS,IAAI;AAC1B,gBAAI,QAAQ,WAAW,UAAU;AAC7B,kBAAI,CAAC,MAAM,IAAI,QAAQ,GAAG,GAAG;AACzB,sBAAM,CAAC,IAAI,IAAI,MAAM,IAAI,sBAAsB,QAAQ,GAAG;AAC1D,sBAAM,IAAI,QAAQ,KAAK,IAAI;AAAA,cAC/B;AACA,kBAAI,iBAAiB,MAAM,IAAI,QAAQ,GAAG,CAAC;AAAA,YAC/C,OACK;AACD,oBAAM,WAAW,qBAAqB,QAAQ,QAAQ,IAAI;AAC1D,0BAAY,IAAI,iBAAiB,QAAQ;AAAA,YAC7C;AAAA,UACJ;AAAA,QACJ,CAAC;AACD,YAAI,GAAG,mBAAmB,aAAW;AACjC,cAAI,QAAQ,QAAQ,OAChB,QAAQ,gBAAgB,+BAAqE;AAC7F,sBAAU,SAAS,IAAI;AAAA,UAC3B;AAAA,QACJ,CAAC;AACD,YAAI,iBAAiB;AAAA,UACjB,IAAI;AAAA,UACJ,OAAO;AAAA,YAAkB;AAAA;AAAA,UAAiD;AAAA,UAC1E,OAAO;AAAA,YAA0B;AAAA;AAAA,UAAiD;AAAA,QACtF,CAAC;AACD,gBAAQ,IAAI;AAAA,MAChB,CAAC;AAAA,IACL,SACO,GAAG;AACN,cAAQ,MAAM,CAAC;AACf,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,CAAC;AACL;AAEA,SAAS,kBAAkB,UAAU;AACjC,SAAQ,SAAS,KAAK,QAClB,SAAS,KAAK,eACd,SAAS,KAAK,UACd;AACR;AACA,SAAS,wBAAwB,UACjC,UAAU,MAAM;AAEZ,QAAMU,UAAS,KAAK,SAAS,gBACvB,KAAK,SACL,KAAK,OAAO;AAClB,MAAI,YAAY,SAAS,MAAM,MAAM,SAAS,MAAM,GAAG,cAAc;AAEjE,QAAI,SAAS,MAAM,GAAG,iBAAiBA,SAAQ;AAC3C,YAAM,MAAM;AAAA,QACR,OAAO,SAAS,kBAAkB,QAAQ,CAAC;AAAA,QAC3C,WAAW;AAAA,QACX,iBAAiB;AAAA,MACrB;AACA,eAAS,KAAK,KAAK,GAAG;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,SAAS,gBAAgB,cAAc,UAAU;AAC7C,QAAM,OAAO;AACb,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS,OAAO;AAAA,EAC3B,CAAC;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS;AAAA,EACpB,CAAC;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS,eAAe;AAAA,EACnC,CAAC;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,SAAS;AAAA,EACpB,CAAC;AACD,eAAa,MAAM,KAAK;AAAA,IACpB;AAAA,IACA,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO,sBAAsB,SAAS,SAAS,KAAK;AAAA,EACxD,CAAC;AACD;AACI,iBAAa,MAAM,KAAK;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,gBAAgB;AAAA,IACpC,CAAC;AACD,iBAAa,MAAM,KAAK;AAAA,MACpB;AAAA,MACA,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,cAAc;AAAA,IAClC,CAAC;AAAA,EACL;AACJ;AAEA,SAAS,sBAAsB,UAAU;AACrC,QAAM,QAAQ,CAAC;AACf,SAAO,KAAK,QAAQ,EAAE,QAAQ,CAAC,QAAQ;AACnC,UAAM,IAAI,SAAS,GAAG;AACtB,QAAI,WAAW,CAAC,KAAK,YAAY,GAAG;AAChC,YAAM,GAAG,IAAI,0BAA0B,CAAC;AAAA,IAC5C,WACS,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,QAAQ;AAC/C,YAAM,GAAG,IAAI,EAAE,IAAI;AAAA,IACvB,WACS,SAAS,CAAC,GAAG;AAClB,YAAM,GAAG,IAAI,sBAAsB,CAAC;AAAA,IACxC,OACK;AACD,YAAM,GAAG,IAAI;AAAA,IACjB;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAM,MAAM;AAAA,EACR,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACT;AACA,SAAS,OAAO,GAAG;AACf,SAAO,EAAE,QAAQ,WAAW,UAAU;AAC1C;AACA,SAAS,WAAW,GAAG;AACnB,SAAO,IAAI,CAAC,KAAK;AACrB;AAEA,SAAS,0BAA0B,MAAM;AACrC,QAAM,YAAY,KAAK,SAAS,KAAK,OAAO,KAAK,MAAM,CAAC,OAAO;AAC/D,SAAO;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,kBAAkB,SAAS;AAAA,IACxC;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,SAAS,MAAM;AAClC,UAAQ,UAAU,KAAK;AAAA,IACnB,IAAI;AAAA,IACJ,OAAO;AAAA,EACX,CAAC;AAED,QAAMA,UAAS,KAAK,SAAS,gBACvB,KAAK,SACL,KAAK,OAAO;AAClB,aAAW,CAAC,aAAa,QAAQ,KAAK,KAAK,aAAa;AAEpD,UAAM,WAAW,KAAK,SAAS,gBACzB,WACA,SAAS;AACf,QAAIA,YAAW,UAAU;AACrB;AAAA,IACJ;AACA,YAAQ,UAAU,KAAK;AAAA,MACnB,IAAI,SAAS,GAAG,SAAS;AAAA,MACzB,OAAO,GAAG,kBAAkB,WAAW,CAAC;AAAA,IAC5C,CAAC;AAAA,EACL;AACJ;AACA,SAAS,qBAAqB,QAAQ,MAAM;AACxC,MAAI,WAAW;AACf,MAAI,WAAW,UAAU;AACrB,eAAW,CAAC,WAAW,QAAQ,KAAK,KAAK,YAAY,QAAQ,GAAG;AAC5D,UAAI,SAAS,GAAG,SAAS,MAAM,QAAQ;AACnC,mBAAW;AACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,cAAc,QAAQ,MAAM;AACjC,MAAI,WAAW,UAAU;AACrB,WAAO,KAAK,SAAS,gBACf,KAAK,SACL,KAAK,OAAO;AAAA,EACtB,OACK;AACD,UAAM,WAAW,MAAM,KAAK,KAAK,YAAY,OAAO,CAAC,EAAE,KAAK,UAAQ,KAAK,GAAG,SAAS,MAAM,MAAM;AACjG,QAAI,UAAU;AACV,aAAO,KAAK,SAAS,gBACf,WACA,SAAS;AAAA,IACnB,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,SAAS,MAE7B;AACE,QAAM,WAAW,cAAc,QAAQ,QAAQ,IAAI;AACnD,MAAI,UAAU;AAGV,YAAQ,QAAQ,sBAAsB,QAAQ;AAAA,EAClD;AACA,SAAO;AACX;AACA,SAAS,sBAAsB,UAAU;AACrC,QAAM,QAAQ,CAAC;AACf,QAAM,aAAa;AACnB,QAAM,eAAe;AAAA,IACjB;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,OAAO;AAAA,IAC3B;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS,eAAe;AAAA,IACnC;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS;AAAA,IACpB;AAAA,IACA;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,SAAS;AAAA,IACpB;AAAA,EACJ;AACA,QAAM,UAAU,IAAI;AACpB,QAAM,qBAAqB;AAC3B,QAAM,uBAAuB;AAAA,IACzB;AAAA,MACI,MAAM;AAAA,MACN,KAAK;AAAA,MACL,UAAU;AAAA,MACV,OAAO,sBAAsB,SAAS,SAAS,KAAK;AAAA,IACxD;AAAA,EACJ;AACA,QAAM,kBAAkB,IAAI;AAC5B;AACI,UAAM,sBAAsB;AAC5B,UAAM,wBAAwB;AAAA,MAC1B;AAAA,QACI,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO,SAAS,gBAAgB;AAAA,MACpC;AAAA,IACJ;AACA,UAAM,mBAAmB,IAAI;AAC7B,UAAM,oBAAoB;AAC1B,UAAM,sBAAsB;AAAA,MACxB;AAAA,QACI,MAAM;AAAA,QACN,KAAK;AAAA,QACL,UAAU;AAAA,QACV,OAAO,SAAS,cAAc;AAAA,MAClC;AAAA,IACJ;AACA,UAAM,iBAAiB,IAAI;AAAA,EAC/B;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,OAAO,SAAS;AACtC,MAAI,aAAa;AACb,QAAI;AACJ,QAAI,WAAW,aAAa,SAAS;AACjC,gBAAU,QAAQ;AAClB,aAAO,QAAQ;AAAA,IACnB;AACA,gBAAY,iBAAiB;AAAA,MACzB,SAAS;AAAA,MACT,OAAO;AAAA,QACH,OAAO;AAAA,QACP;AAAA,QACA,MAAM,KAAK,IAAI;AAAA,QACf,MAAM,CAAC;AAAA,QACP,MAAM,WAAW,CAAC;AAAA,QAClB,SAAS,UAAU,kBACb,UACA,UAAU,cACR,UAAU,YACR,YACA;AAAA,MACd;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AACA,SAAS,UAAU,SAAS,MAAM;AAC9B,QAAM,WAAW,cAAc,QAAQ,QAAQ,IAAI;AACnD,MAAI,UAAU;AACV,UAAM,CAAC,KAAK,IAAI,QAAQ;AACxB,QAAI,UAAU,YAAY,SAAS,QAAQ,MAAM,KAAK,GAAG;AACrD,eAAS,OAAO,QAAQ,QAAQ,MAAM;AAAA,IAC1C,WACS,UAAU,qBACd,SAAS,QAAQ,MAAM,KAAK,KACzB,QAAQ,QAAQ,MAAM,KAAK,KAC3B,SAAS,QAAQ,MAAM,KAAK,IAAI;AACpC,eAAS,eAAe,QAAQ,QAAQ,MAAM;AAAA,IAClD,WACS,UAAU,mBAAmB,UAAU,QAAQ,MAAM,KAAK,GAAG;AAClE,eAAS,gBAAgB,QAAQ,MAAM;AAAA,IAC3C;AAAA,EACJ;AACJ;AAMA,SAAS,YAAY,SAAS,UAAU,MAAM;AAC1C,SAAO;AAAA,IACH,eAAe;AACX,YAAM,WAAW,mBAAmB;AAEpC,UAAI,CAAC,UAAU;AACX,cAAM,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AACA,YAAM,UAAU,KAAK;AACrB,UAAI,QAAQ,MAAM;AACd,cAAM,cAAc,QAAQ;AAC5B,YAAI,QAAQ,QAAQ;AAChB,sBAAY,SAAS,QAAQ;AAAA,QACjC;AACA,oBAAY,SAAS;AACrB,YAAI,SAAS,KAAK,OAAO;AAErB,eAAK,QAAQ,cAAc,SAAS,WAAW;AAAA,QACnD,OACK;AACD,sBAAY,qBAAqB;AACjC,sBAAY,aAAa,KAAK;AAE9B,eAAK,QAAQ,cAAc,WAAW;AAEtC,gBAAM,WAAW,KAAK;AACtB,cAAI,SAAS,YAAY;AACrB,qBAAS,aAAa,SAAS,WAAW,KAAK,KAAK;AAAA,UACxD;AAAA,QACJ;AAAA,MACJ,WACS,QAAQ,QAAQ;AACrB,YAAI,SAAS,KAAK,OAAO;AAErB,eAAK,QAAQ,cAAc,SAAS,OAAO;AAAA,QAC/C,OACK;AAED,eAAK,QAAQ,cAAc;AAAA,YACvB,QAAQ,QAAQ;AAAA,YAChB,oBAAoB;AAAA,YACpB,YAAY,KAAK;AAAA,YACjB,QAAQ;AAAA,UACZ,CAAC;AAED,gBAAM,WAAW,KAAK;AACtB,cAAI,SAAS,YAAY;AACrB,qBAAS,aAAa,SAAS,WAAW,KAAK,KAAK;AAAA,UACxD;AAAA,QACJ;AAAA,MACJ,OACK;AAED,aAAK,QAAQ;AAAA,MACjB;AACA,UAAI,QAAQ,cAAc;AACtB,4BAAoB,UAAU,SAAS,OAAO;AAAA,MAClD;AAEA,WAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,WAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,WAAK,MAAM,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,IAAI;AAC7C,WAAK,MAAM,CAAC,KAAK,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM;AACrD,WAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,WAAK,KAAK,IAAI,SAAS,KAAK,MAAM,EAAE,GAAG,IAAI;AAC3C,WAAK,MAAM,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;AACrC,WAAK,cAAc,UAAU,KAAK,KAAK;AAAA,IAC3C;AAAA,IACA,UAAU;AAEN,UAEI,KAAK,OACL,KAAK,OAAO;AACZ,cAAM,WAAW,KAAK;AACtB,aAAK,IAAI,eAAe,SAAS;AACjC,cAAM,UAAW,KAAK,cAClB,cAAc;AAClB,iBAAS,mBAAmB,SAAS,gBAAgB,OAAO;AAC5D,gBAAQ,GAAG,KAAK,gBAAgB;AAAA,MACpC;AAAA,IACJ;AAAA,IACA,YAAY;AACR,YAAM,WAAW,mBAAmB;AAEpC,UAAI,CAAC,UAAU;AACX,cAAM,gBAAgB,eAAe,gBAAgB;AAAA,MACzD;AACA,YAAM,WAAW,KAAK;AAEtB,UAEI,KAAK,OACL,KAAK,IAAI,cAAc;AACvB,YAAI,KAAK,aAAa;AAClB,eAAK,YAAY,IAAI,KAAK,gBAAgB;AAC1C,iBAAO,KAAK;AAAA,QAChB;AACA,YAAI,KAAK,OAAO;AACZ,mBAAS,oBAAoB,SAAS,iBAAiB;AACvD,iBAAO,KAAK,IAAI;AAAA,QACpB;AAAA,MACJ;AACA,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,aAAO,KAAK;AACZ,UAAI,SAAS,YAAY;AACrB,iBAAS,WAAW;AACpB,eAAO,SAAS;AAChB,eAAO,SAAS;AAAA,MACpB;AACA,WAAK,iBAAiB,QAAQ;AAC9B,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,GAAG,SAAS;AAC/B,IAAE,SAAS,QAAQ,UAAU,EAAE;AAC/B,IAAE,iBAAiB,QAAQ,kBAAkB,EAAE;AAC/C,IAAE,UAAU,QAAQ,WAAW,EAAE;AACjC,IAAE,wBACE,QAAQ,yBAAyB,EAAE;AACvC,IAAE,qBAAqB,QAAQ,sBAAsB,EAAE;AACvD,IAAE,yBACE,QAAQ,0BAA0B,EAAE;AACxC,IAAE,kBAAkB,QAAQ,mBAAmB,EAAE;AACjD,IAAE,oBAAoB,QAAQ,qBAAqB,EAAE;AACrD,IAAE,sBAAsB,QAAQ,uBAAuB,EAAE;AACzD,IAAE,OAAO,QAAQ,QAAQ,EAAE;AAC3B,IAAE,WAAW,oBAAoB,EAAE,QAAQ,sBAAsB,EAAE,kBAAkB;AACrF,QAAM,WAAW,kBAAkB,EAAE,QAAQ;AAAA,IACzC,UAAU,QAAQ;AAAA,IAClB,QAAQ,QAAQ;AAAA,EACpB,CAAC;AACD,SAAO,KAAK,QAAQ,EAAE,QAAQ,YAAU,EAAE,mBAAmB,QAAQ,SAAS,MAAM,CAAC,CAAC;AACtF,MAAI,QAAQ,iBAAiB;AACzB,WAAO,KAAK,QAAQ,eAAe,EAAE,QAAQ,YAAU,EAAE,oBAAoB,QAAQ,QAAQ,gBAAgB,MAAM,CAAC,CAAC;AAAA,EACzH;AACA,MAAI,QAAQ,eAAe;AACvB,WAAO,KAAK,QAAQ,aAAa,EAAE,QAAQ,YAAU,EAAE,kBAAkB,QAAQ,QAAQ,cAAc,MAAM,CAAC,CAAC;AAAA,EACnH;AACA,SAAO;AACX;AAWA,IAAM,mBACS,WAAW,iBAAiB;AAE3C,SAAS,WAAW,UAAU,CAAC,GAAG,eAAe;AAE7C,QAAM,eAAe,2BAA2B,UAAU,QAAQ,MAAM,IAC9D,QAAQ,SACR;AAEV,QAAM,oBAAoB,UAAU,QAAQ,eAAe,IACrD,QAAQ,kBACR;AAEN,QAAM,qBAAqB,2BAA2B,eAC5C,CAAC,CAAC,QAAQ,mBACV;AACV,QAAM,cAAc,oBAAI,IAAI;AAC5B,QAAM,CAAC,aAAa,QAAQ,IAAI,aAAa,SAAS,YAAY;AAClE,QAAM,SAAwB,WAAY,OAAyC,aAAa,EAAE;AAClG,MAAK,MAAwC;AACzC,QAAI,gBAAgB,sBAAsB,MAAQ;AAC9C,WAAKV,gBAAe,cAAc,6BAA6B,CAAC;AAAA,IACpE;AAAA,EACJ;AACA,WAAS,cAAc,WAAW;AAC9B,WAAO,YAAY,IAAI,SAAS,KAAK;AAAA,EACzC;AACA,WAAS,cAAc,WAAW,UAAU;AACxC,gBAAY,IAAI,WAAW,QAAQ;AAAA,EACvC;AACA,WAAS,iBAAiB,WAAW;AACjC,gBAAY,OAAO,SAAS;AAAA,EAChC;AACA;AACI,UAAM,OAAO;AAAA;AAAA,MAET,IAAI,OAAO;AACP,eAAO,2BAA2B,eAC5B,WACA;AAAA,MACV;AAAA;AAAA,MAEA,IAAI,mBAAmB;AACnB,eAAO;AAAA,MACX;AAAA;AAAA,MAEA,MAAM,QAAQ,QAAQS,UAAS;AAC3B,YACI,MAAQ;AACR,cAAI,eAAe;AAAA,QACvB;AAEA,YAAI,sBAAsB;AAC1B,YAAI,QAAQ,IAAI,qBAAqB,IAAI;AAEzC,YAAI,cAAcA,SAAQ,CAAC,CAAC,GAAG;AAC3B,gBAAM,OAAOA,SAAQ,CAAC;AACtB,eAAK,mBACD,KAAK;AACT,eAAK,kBACD,KAAK;AAAA,QACb;AAEA,YAAI,uBAAuB;AAC3B,YAAI,CAAC,gBAAgB,mBAAmB;AACpC,iCAAuB,mBAAmB,KAAK,KAAK,MAAM;AAAA,QAC9D;AAEA,YAAI,2BAA2B;AAC3B,gBAAM,KAAK,MAAM,GAAGA,QAAO;AAAA,QAC/B;AAEA,YAAI,2BAA2B,cAAc;AACzC,cAAI,MAAM,YAAY,UAAU,SAAS,YAAY,IAAI,CAAC;AAAA,QAC9D;AAEA,cAAM,aAAa,IAAI;AACvB,YAAI,UAAU,MAAM;AAChB,kCAAwB,qBAAqB;AAC7C,eAAK,QAAQ;AACb,qBAAW;AAAA,QACf;AAEA,YAA0E,MAAQ;AAC9E,gBAAM,MAAM,MAAM,eAAe,KAAK,IAAI;AAC1C,cAAI,CAAC,KAAK;AACN,kBAAM,gBAAgB,eAAe,gCAAgC;AAAA,UACzE;AACA,gBAAM,UAAU,cAAc;AAC9B,cAAI,cAAc;AACd,kBAAM,WAAW;AACjB,qBAAS,mBAAmB,SAAS,gBAAgB,OAAO;AAAA,UAChE,OACK;AAED,kBAAM,YAAY;AAClB,sBAAU,aAAa,KAAK,UAAU,aAAa,EAAE,OAAO;AAAA,UAChE;AACA,kBAAQ,GAAG,KAAK,gBAAgB;AAAA,QACpC;AAAA,MACJ;AAAA;AAAA,MAEA,IAAI,SAAS;AACT,eAAO;AAAA,MACX;AAAA,MACA,UAAU;AACN,oBAAY,KAAK;AAAA,MACrB;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA;AAAA,MAEA;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,QAAQ,UAAU,CAAC,GAAG;AAC3B,QAAM,WAAW,mBAAmB;AACpC,MAAI,YAAY,MAAM;AAClB,UAAM,gBAAgB,eAAe,sBAAsB;AAAA,EAC/D;AACA,MAAI,CAAC,SAAS,QACV,SAAS,WAAW,OAAO,QAC3B,CAAC,SAAS,WAAW,IAAI,qBAAqB;AAC9C,UAAM,gBAAgB,eAAe,aAAa;AAAA,EACtD;AACA,QAAM,OAAO,gBAAgB,QAAQ;AACrC,QAAM,KAAK,kBAAkB,IAAI;AACjC,QAAM,mBAAmB,oBAAoB,QAAQ;AACrD,QAAM,QAAQ,SAAS,SAAS,gBAAgB;AAChD,MAAI,yBAAyB;AAEzB,QAAI,KAAK,SAAS,YAAY,CAAC,QAAQ,gBAAgB;AACnD,UAAI,CAAC,KAAK,kBAAkB;AACxB,cAAM,gBAAgB,eAAe,4BAA4B;AAAA,MACrE;AACA,aAAO,iBAAiB,UAAU,OAAO,IAAI,OAAO;AAAA,IACxD;AAAA,EACJ;AACA,MAAI,UAAU,UAAU;AACpB,wBAAoB,IAAI,SAAS,gBAAgB;AACjD,WAAO;AAAA,EACX;AACA,MAAI,UAAU,UAAU;AAEpB,QAAIE,YAAW,YAAY,MAAM,UAAU,QAAQ,cAAc;AACjE,QAAIA,aAAY,MAAM;AAClB,UAAK,MAAwC;AACzC,aAAKX,gBAAe,cAAc,sBAAsB,CAAC;AAAA,MAC7D;AACA,MAAAW,YAAW;AAAA,IACf;AACA,WAAOA;AAAA,EACX;AACA,QAAM,eAAe;AACrB,MAAI,WAAW,aAAa,cAAc,QAAQ;AAClD,MAAI,YAAY,MAAM;AAClB,UAAM,kBAAkB,OAAO,CAAC,GAAG,OAAO;AAC1C,QAAI,YAAY,kBAAkB;AAC9B,sBAAgB,SAAS,iBAAiB;AAAA,IAC9C;AACA,QAAI,IAAI;AACJ,sBAAgB,SAAS;AAAA,IAC7B;AACA,eAAW,eAAe,eAAe;AACzC,QAAI,aAAa,kBAAkB;AAC/B,eAAS,aAAa,IAClB,aAAa,iBAAiB,QAAQ;AAAA,IAC9C;AACA,mBAAe,cAAc,UAAU,QAAQ;AAC/C,iBAAa,cAAc,UAAU,QAAQ;AAAA,EACjD;AACA,SAAO;AACX;AAkBA,IAAM,gBAAgB,CAAC,SAElB;AACD,MAAI,EAAE,uBAAuB,OAAO;AAChC,UAAM,gBAAgB,eAAe,8BAA8B;AAAA,EACvE;AACA,SAAO;AACX;AACA,SAAS,aAAa,SAAS,YAAY,eACzC;AACE,QAAM,QAAQ,YAAY;AAC1B;AACI,UAAM,MAAM,2BAA2B,aACjC,MAAM,IAAI,MAAM,cAAc,OAAO,CAAC,IACtC,MAAM,IAAI,MAAM,eAAe,OAAO,CAAC;AAC7C,QAAI,OAAO,MAAM;AACb,YAAM,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,WAAO,CAAC,OAAO,GAAG;AAAA,EACtB;AACJ;AACA,SAAS,gBAAgB,UAAU;AAC/B;AACI,UAAM,OAAO,OAAO,CAAC,SAAS,OACxB,SAAS,WAAW,IAAI,sBACxB,gBAAgB;AAEtB,QAAI,CAAC,MAAM;AACP,YAAM,gBAAgB,CAAC,SAAS,OAC1B,eAAe,mBACf,eAAe,0BAA0B;AAAA,IACnD;AACA,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,SAAS,SAAS,kBAAkB;AAEzC,SAAO,cAAc,OAAO,IACrB,YAAY,mBACT,UACA,WACJ,CAAC,QAAQ,WACL,UACA,QAAQ;AACtB;AACA,SAAS,kBAAkB,MAAM;AAE7B,SAAO,KAAK,SAAS,gBACX,KAAK,SACL,KAAK,OAAO;AAE1B;AACA,SAAS,YAAY,MAAM,QAAQ,eAAe,OAAO;AACrD,MAAI,WAAW;AACf,QAAM,OAAO,OAAO;AACpB,MAAI,UAAU,2BAA2B,QAAQ,YAAY;AAC7D,SAAO,WAAW,MAAM;AACpB,UAAM,eAAe;AACrB,QAAI,KAAK,SAAS,eAAe;AAC7B,iBAAW,aAAa,cAAc,OAAO;AAAA,IACjD,OACK;AACD,UAAI,yBAAyB;AACzB,cAAM,UAAU,aAAa,cAAc,OAAO;AAClD,YAAI,WAAW,MAAM;AACjB,qBAAW,QACN;AACL,cAAI,gBACA,YACA,CAAC,SAAS,sBAAsB,GAClC;AACE,uBAAW;AAAA,UACf;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,YAAY,MAAM;AAClB;AAAA,IACJ;AACA,QAAI,SAAS,SAAS;AAClB;AAAA,IACJ;AACA,cAAU,QAAQ;AAAA,EACtB;AACA,SAAO;AACX;AACA,SAAS,2BAA2B,QAAQ,eAAe,OAAO;AAC9D,MAAI,UAAU,MAAM;AAChB,WAAO;AAAA,EACX;AACA;AAEI,WAAO,CAAC,eACF,OAAO,SACP,OAAO,MAAM,OAAO,OAAO;AAAA,EACrC;AACJ;AACA,SAAS,eAAe,MAAM,QAAQ,UAAU;AAC5C,MAAI,UAAU;AACd;AACI,cAAU,MAAM;AAEZ,UAEI,OAAO,MAAM,IAAI;AACjB,eAAO,MAAM,GAAG,eAAe;AAC/B,kBAAU,cAAc;AAExB,cAAM,YAAY;AAClB,kBAAU,aAAa,KAAK,UAAU,aAAa,EAAE,OAAO;AAC5D,gBAAQ,GAAG,KAAK,gBAAgB;AAAA,MACpC;AAAA,IACJ,GAAG,MAAM;AACT,gBAAY,MAAM;AAEd,YAAM,YAAY;AAElB,UAEI,OAAO,MAAM,MACb,OAAO,MAAM,GAAG,cAAc;AAC9B,mBAAW,QAAQ,IAAI,KAAK,gBAAgB;AAC5C,kBAAU,cAAc,KAAK,UAAU,cAAc,EAAE;AACvD,eAAO,OAAO,MAAM,GAAG;AAAA,MAC3B;AACA,WAAK,iBAAiB,MAAM;AAE5B,YAAM,UAAU,UAAU,aAAa;AACvC,UAAI,SAAS;AACT,gBAAQ;AACR,eAAO,UAAU,aAAa;AAAA,MAClC;AAAA,IACJ,GAAG,MAAM;AAAA,EACb;AACJ;AACA,SAAS,iBAAiB,UAAU,OAAO,MAAM,UAAU,CAAC,GAC1D;AACE,QAAM,eAAe,UAAU;AAC/B,QAAM,YAAY,WAAW,IAAI;AACjC,MAAI,gBACA,SAAS,SACT,EAAE,SAAS,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,SAAS;AACnE,UAAM,gBAAgB,eAAe,4CAA4C;AAAA,EACrF;AACA,QAAM,iBAAiB,UAAU,QAAQ,aAAa,IAChD,QAAQ,gBACR,CAAC,SAAS,QAAQ,MAAM;AAC9B,QAAM,UAAU;AAAA;AAAA,IAEhB,CAAC,gBAAgB,iBACX,KAAK,OAAO,QACZ,SAAS,QAAQ,MAAM,IACnB,QAAQ,SACR;AAAA,EAAc;AACxB,QAAM,kBAAkB;AAAA;AAAA,IAExB,CAAC,gBAAgB,iBACX,KAAK,eAAe,QACpB,SAAS,QAAQ,cAAc,KAC7B,QAAQ,QAAQ,cAAc,KAC9B,cAAc,QAAQ,cAAc,KACpC,QAAQ,mBAAmB,QACzB,QAAQ,iBACR,QAAQ;AAAA,EAAK;AACvB,QAAM,YAAY,IAAI,kBAAkB,QAAQ,OAAO,OAAO,CAAC;AAE/D,QAAM,mBAAmB,IAAI,cAAc,QAAQ,eAAe,IAC5D,QAAQ,kBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAE7B,QAAM,iBAAiB,IAAI,cAAc,QAAQ,aAAa,IACxD,QAAQ,gBACR,EAAE,CAAC,QAAQ,KAAK,GAAG,CAAC,EAAE,CAAC;AAE7B,QAAM,eAAe,eACf,KAAK,cACL,UAAU,QAAQ,WAAW,KAAK,SAAS,QAAQ,WAAW,IAC1D,QAAQ,cACR;AAEV,QAAM,gBAAgB,eAChB,KAAK,eACL,UAAU,QAAQ,YAAY,KAAK,SAAS,QAAQ,YAAY,IAC5D,QAAQ,eACR;AAEV,QAAM,gBAAgB,eAChB,KAAK,eACL,UAAU,QAAQ,YAAY,IAC1B,QAAQ,eACR;AAEV,QAAM,kBAAkB,CAAC,CAAC,QAAQ;AAElC,QAAM,WAAW,WAAW,QAAQ,OAAO,IAAI,QAAQ,UAAU;AAEjE,QAAM,mBAAmB,WAAW,QAAQ,eAAe,IACrD,QAAQ,kBACR;AAEN,QAAM,mBAAmB,eACnB,KAAK,kBACL,UAAU,QAAQ,eAAe,IAC7B,QAAQ,kBACR;AACV,QAAM,mBAAmB,CAAC,CAAC,QAAQ;AAEnC,QAAM,aAAa,eACb,KAAK,YACL,cAAc,QAAQ,SAAS,IAC3B,QAAQ,YACR,CAAC;AAEX,QAAM,eAAe,QAAQ,eAAgB,gBAAgB,KAAK;AAElE,WAAS,wBAAwB;AAC7B,WAAO;AAAA,MACH,QAAQ;AAAA,MACR,gBAAgB;AAAA,MAChB,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACnB;AAAA,EACJ;AAEA,QAAM,SAAS,SAAS;AAAA,IACpB,KAAK,MAAM;AACP,aAAO,UAAU,QAAQ,UAAU,MAAM,OAAO,QAAQ,QAAQ;AAAA,IACpE;AAAA,IACA,KAAK,SAAO;AACR,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,OAAO,QAAQ;AAAA,MACnC;AACA,cAAQ,QAAQ;AAAA,IACpB;AAAA,EACJ,CAAC;AAED,QAAM,iBAAiB,SAAS;AAAA,IAC5B,KAAK,MAAM;AACP,aAAO,UAAU,QACX,UAAU,MAAM,eAAe,QAC/B,gBAAgB;AAAA,IAC1B;AAAA,IACA,KAAK,SAAO;AACR,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,eAAe,QAAQ;AAAA,MAC3C;AACA,sBAAgB,QAAQ;AAAA,IAC5B;AAAA,EACJ,CAAC;AAED,QAAM,WAAW,SAAS,MAAM;AAC5B,QAAI,UAAU,OAAO;AAEjB,aAAO,UAAU,MAAM,SAAS;AAAA,IACpC,OACK;AAED,aAAO,UAAU;AAAA,IACrB;AAAA,EACJ,CAAC;AACD,QAAM,kBAAkB,SAAS,MAAM,iBAAiB,KAAK;AAC7D,QAAM,gBAAgB,SAAS,MAAM,eAAe,KAAK;AACzD,WAAS,4BAA4B;AACjC,WAAO,UAAU,QACX,UAAU,MAAM,0BAA0B,IAC1C;AAAA,EACV;AACA,WAAS,0BAA0B,SAAS;AACxC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,0BAA0B,OAAO;AAAA,IACrD;AAAA,EACJ;AACA,WAAS,oBAAoB;AACzB,WAAO,UAAU,QAAQ,UAAU,MAAM,kBAAkB,IAAI;AAAA,EACnE;AACA,WAAS,kBAAkB,SAAS;AAChC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,kBAAkB,OAAO;AAAA,IAC7C;AAAA,EACJ;AACA,WAAS,aAAa,IAAI;AACtB,0BAAsB;AACtB,WAAO,GAAG;AAAA,EACd;AACA,WAAS,KAAK,MAAM;AAChB,WAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,EAC/B;AACA,WAAS,MAAM,MAAM;AACjB,WAAO,UAAU,QACX,QAAQ,MAAM,UAAU,MAAM,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,IACjD;AAAA,EACV;AACA,WAAS,KAAK,MAAM;AAChB,WAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,EAC/B;AACA,WAAS,KAAK,MAAM;AAChB,WAAO,UAAU,QACX,aAAa,MAAM,QAAQ,MAAM,UAAU,MAAM,GAAG,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,IACpE,aAAa,MAAM,EAAE;AAAA,EAC/B;AACA,WAAS,GAAG,KAAK;AACb,WAAO,UAAU,QAAQ,UAAU,MAAM,GAAG,GAAG,IAAI,CAAC;AAAA,EACxD;AACA,WAAS,GAAG,KAAKP,SAAQ;AACrB,WAAO,UAAU,QAAQ,UAAU,MAAM,GAAG,KAAKA,OAAM,IAAI;AAAA,EAC/D;AACA,WAAS,iBAAiBA,SAAQ;AAC9B,WAAO,UAAU,QAAQ,UAAU,MAAM,iBAAiBA,OAAM,IAAI,CAAC;AAAA,EACzE;AACA,WAAS,iBAAiBA,SAAQ,SAAS;AACvC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,iBAAiBA,SAAQ,OAAO;AAChD,gBAAU,MAAMA,OAAM,IAAI;AAAA,IAC9B;AAAA,EACJ;AACA,WAAS,mBAAmBA,SAAQ,SAAS;AACzC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,mBAAmBA,SAAQ,OAAO;AAAA,IACtD;AAAA,EACJ;AACA,WAAS,kBAAkBA,SAAQ;AAC/B,WAAO,UAAU,QAAQ,UAAU,MAAM,kBAAkBA,OAAM,IAAI,CAAC;AAAA,EAC1E;AACA,WAAS,kBAAkBA,SAAQI,SAAQ;AACvC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,kBAAkBJ,SAAQI,OAAM;AAChD,uBAAiB,MAAMJ,OAAM,IAAII;AAAA,IACrC;AAAA,EACJ;AACA,WAAS,oBAAoBJ,SAAQI,SAAQ;AACzC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,oBAAoBJ,SAAQI,OAAM;AAAA,IACtD;AAAA,EACJ;AACA,WAAS,gBAAgBJ,SAAQ;AAC7B,WAAO,UAAU,QAAQ,UAAU,MAAM,gBAAgBA,OAAM,IAAI,CAAC;AAAA,EACxE;AACA,WAAS,gBAAgBA,SAAQI,SAAQ;AACrC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,gBAAgBJ,SAAQI,OAAM;AAC9C,qBAAe,MAAMJ,OAAM,IAAII;AAAA,IACnC;AAAA,EACJ;AACA,WAAS,kBAAkBJ,SAAQI,SAAQ;AACvC,QAAI,UAAU,OAAO;AACjB,gBAAU,MAAM,kBAAkBJ,SAAQI,OAAM;AAAA,IACpD;AAAA,EACJ;AACA,QAAM,UAAU;AAAA,IACZ,IAAI,KAAK;AACL,aAAO,UAAU,QAAQ,UAAU,MAAM,KAAK;AAAA,IAClD;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,IAAI,gBAAgB;AAChB,aAAO,UAAU,QAAQ,UAAU,MAAM,gBAAgB;AAAA,IAC7D;AAAA,IACA,IAAI,cAAc,KAAK;AACnB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,gBAAgB;AAAA,MACpC;AAAA,IACJ;AAAA,IACA,IAAI,mBAAmB;AACnB,aAAO,UAAU,QACX,UAAU,MAAM,mBAChB,OAAO,KAAK,UAAU,KAAK;AAAA,IACrC;AAAA,IACA,IAAI,YAAY;AACZ,aAAQ,UAAU,QAAQ,UAAU,MAAM,YAAY;AAAA,IAC1D;AAAA,IACA,IAAI,cAAc;AACd,aAAQ,UAAU,QAAQ,UAAU,MAAM,cAAc;AAAA,IAC5D;AAAA,IACA,IAAI,WAAW;AACX,aAAO,UAAU,QAAQ,UAAU,MAAM,WAAW;AAAA,IACxD;AAAA,IACA,IAAI,cAAc;AACd,aAAO,UAAU,QAAQ,UAAU,MAAM,cAAc;AAAA,IAC3D;AAAA,IACA,IAAI,YAAY,KAAK;AACjB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,cAAc;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,IAAI,eAAe;AACf,aAAO,UAAU,QAAQ,UAAU,MAAM,eAAe;AAAA,IAC5D;AAAA,IACA,IAAI,aAAa,KAAK;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,cAAc;AAAA,MAClC;AAAA,IACJ;AAAA,IACA,IAAI,eAAe;AACf,aAAO,UAAU,QAAQ,UAAU,MAAM,eAAe;AAAA,IAC5D;AAAA,IACA,IAAI,aAAa,KAAK;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,eAAe;AAAA,MACnC;AAAA,IACJ;AAAA,IACA,IAAI,iBAAiB;AACjB,aAAO,UAAU,QAAQ,UAAU,MAAM,iBAAiB;AAAA,IAC9D;AAAA,IACA,IAAI,eAAe,KAAK;AACpB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,iBAAiB;AAAA,MACrC;AAAA,IACJ;AAAA,IACA,IAAI,kBAAkB;AAClB,aAAO,UAAU,QACX,UAAU,MAAM,kBAChB;AAAA,IACV;AAAA,IACA,IAAI,gBAAgB,KAAK;AACrB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,kBAAkB;AAAA,MACtC;AAAA,IACJ;AAAA,IACA,IAAI,kBAAkB;AAClB,aAAO,UAAU,QACX,UAAU,MAAM,kBAChB;AAAA,IACV;AAAA,IACA,IAAI,gBAAgB,KAAK;AACrB,UAAI,UAAU,OAAO;AACjB,kBAAU,MAAM,kBAAkB;AAAA,MACtC;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACA,WAAS,KAAK,UAAU;AACpB,aAAS,OAAO,QAAQ,QAAQ;AAChC,aAAS,eAAe,QAAQ,gBAAgB;AAChD,WAAO,KAAK,UAAU,KAAK,EAAE,QAAQ,CAAAJ,YAAU;AAC3C,eAAS,mBAAmBA,SAAQ,UAAU,MAAMA,OAAM,CAAC;AAAA,IAC/D,CAAC;AACD,WAAO,KAAK,iBAAiB,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAClD,eAAS,oBAAoBA,SAAQ,iBAAiB,MAAMA,OAAM,CAAC;AAAA,IACvE,CAAC;AACD,WAAO,KAAK,eAAe,KAAK,EAAE,QAAQ,CAAAA,YAAU;AAChD,eAAS,kBAAkBA,SAAQ,eAAe,MAAMA,OAAM,CAAC;AAAA,IACnE,CAAC;AACD,aAAS,kBAAkB;AAC3B,aAAS,iBAAiB;AAC1B,aAAS,eAAe;AACxB,aAAS,eAAe;AACxB,aAAS,cAAc;AACvB,aAAS,kBAAkB;AAAA,EAC/B;AACA,gBAAc,MAAM;AAChB,QAAI,SAAS,SAAS,QAAQ,SAAS,MAAM,SAAS,MAAM;AACxD,YAAM,gBAAgB,eAAe,mCAAmC;AAAA,IAC5E;AAEA,UAAM,WAAY,UAAU,QAAQ,SAAS,MAAM,MAC9C;AACL,QAAI,UAAU,UAAU;AACpB,cAAQ,QAAQ,SAAS,OAAO;AAChC,sBAAgB,QAAQ,SAAS,eAAe;AAChD,gBAAU,QAAQ,SAAS,SAAS;AACpC,uBAAiB,QAAQ,SAAS,gBAAgB;AAClD,qBAAe,QAAQ,SAAS,cAAc;AAAA,IAClD,WACS,cAAc;AACnB,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AACA,IAAM,oBAAoB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACJ;AACA,IAAM,sBAAsB,CAAC,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI;AAE5D,SAAS,mBAAmB,KAAK,UAAU;AACvC,QAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,oBAAkB,QAAQ,UAAQ;AAC9B,UAAM,OAAO,OAAO,yBAAyB,UAAU,IAAI;AAC3D,QAAI,CAAC,MAAM;AACP,YAAM,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,UAAM,OAAO,MAAM,KAAK,KAAK,IACvB;AAAA,MACE,MAAM;AACF,eAAO,KAAK,MAAM;AAAA,MACtB;AAAA;AAAA,MAEA,IAAI,KAAK;AACL,aAAK,MAAM,QAAQ;AAAA,MACvB;AAAA,IACJ,IACE;AAAA,MACE,MAAM;AACF,eAAO,KAAK,OAAO,KAAK,IAAI;AAAA,MAChC;AAAA,IACJ;AACJ,WAAO,eAAe,MAAM,MAAM,IAAI;AAAA,EAC1C,CAAC;AACD,MAAI,OAAO,iBAAiB,QAAQ;AACpC,sBAAoB,QAAQ,YAAU;AAClC,UAAM,OAAO,OAAO,yBAAyB,UAAU,MAAM;AAC7D,QAAI,CAAC,QAAQ,CAAC,KAAK,OAAO;AACtB,YAAM,gBAAgB,eAAe,gBAAgB;AAAA,IACzD;AACA,WAAO,eAAe,IAAI,OAAO,kBAAkB,IAAI,MAAM,IAAI,IAAI;AAAA,EACzE,CAAC;AACD,QAAM,UAAU,MAAM;AAElB,WAAO,IAAI,OAAO,iBAAiB;AACnC,wBAAoB,QAAQ,YAAU;AAElC,aAAO,IAAI,OAAO,iBAAiB,IAAI,MAAM,EAAE;AAAA,IACnD,CAAC;AAAA,EACL;AACA,SAAO;AACX;AAEA;AACI,EAAAR,kBAAiB;AACrB;AAEA,IAAI,6BAA6B;AAC7B,0BAAwB,OAAO;AACnC,OACK;AACD,0BAAwB,iBAAiB;AAC7C;AAEA,wBAAwB,YAAY;AAEpC,yBAAyB,uBAAuB;AAEhD,IAAK,MAAqE;AACtE,QAAM,SAAS,cAAc;AAC7B,SAAO,cAAc;AACrB,kBAAgB,OAAO,gCAAgC;AAC3D;AACA,IAAK,KAAwC;", "names": ["code", "src", "des", "RE_ARGS", "format", "isObject", "assign", "isString", "join", "code", "index", "context", "isLiteral", "parse", "code", "type", "warnMessages", "code", "errorMessages", "format", "baseCompile", "code", "format", "resolveValue", "msg", "source", "message", "VERSION", "initFeatureFlags", "code$1", "inc$1", "warnMessages", "getWarnMessage", "code", "inc", "errorMessages", "locale", "locales", "_context", "messages", "format", "options", "global", "composer"]}