/*!
  * message-compiler v9.14.5
  * (c) 2025 ka<PERSON><PERSON>
  * Released under the MIT License.
  */
const LOCATION_STUB={start:{line:1,column:1,offset:0},end:{line:1,column:1,offset:0}};function createPosition(e,t,n){return{line:e,column:t,offset:n}}function createLocation(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const assign=Object.assign,isString=e=>"string"==typeof e;function join(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}const CompileWarnCodes={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2},warnMessages={[CompileWarnCodes.USE_MODULO_SYNTAX]:"Use modulo before '{{0}}'."};function createCompileWarn(e,t,...n){const r={message:String(e),code:e};return t&&(r.location=t),r}const CompileErrorCodes={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17},errorMessages={[CompileErrorCodes.EXPECTED_TOKEN]:"Expected token: '{0}'",[CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]:"Invalid token in placeholder: '{0}'",[CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]:"Unterminated single quote in placeholder",[CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]:"Unknown escape sequence: \\{0}",[CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]:"Invalid unicode escape sequence: {0}",[CompileErrorCodes.UNBALANCED_CLOSING_BRACE]:"Unbalanced closing brace",[CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]:"Unterminated closing brace",[CompileErrorCodes.EMPTY_PLACEHOLDER]:"Empty placeholder",[CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]:"Not allowed nest placeholder",[CompileErrorCodes.INVALID_LINKED_FORMAT]:"Invalid linked format",[CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]:"Plural must have messages",[CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]:"Unexpected empty linked modifier",[CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]:"Unexpected empty linked key",[CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]:"Unexpected lexical analysis in token: '{0}'",[CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]:"unhandled codegen node type: '{0}'",[CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]:"unhandled mimifier node type: '{0}'"};function createCompileError(e,t,n={}){const{domain:r,messages:o,args:s}=n,c=new SyntaxError(String(e));return c.code=e,t&&(c.location=t),c.domain=r,c}function defaultOnError(e){throw e}const RE_HTML_TAG=/<\/?[\w\s="/.':;#-\/]+>/,detectHtmlTag=e=>RE_HTML_TAG.test(e),CHAR_SP=" ",CHAR_CR="\r",CHAR_LF="\n",CHAR_LS=String.fromCharCode(8232),CHAR_PS=String.fromCharCode(8233);function createScanner(e){const t=e;let n=0,r=1,o=1,s=0;const c=e=>t[e]===CHAR_CR&&t[e+1]===CHAR_LF,a=e=>t[e]===CHAR_PS,i=e=>t[e]===CHAR_LS,u=e=>c(e)||(e=>t[e]===CHAR_LF)(e)||a(e)||i(e),l=e=>c(e)||a(e)||i(e)?CHAR_LF:t[e];function E(){return s=0,u(n)&&(r++,o=0),c(n)&&n++,n++,o++,t[n]}return{index:()=>n,line:()=>r,column:()=>o,peekOffset:()=>s,charAt:l,currentChar:()=>l(n),currentPeek:()=>l(n+s),next:E,peek:function(){return c(n+s)&&s++,s++,t[n+s]},reset:function(){n=0,r=1,o=1,s=0},resetPeek:function(e=0){s=e},skipToPeek:function(){const e=n+s;for(;e!==n;)E();s=0}}}const EOF=void 0,DOT=".",LITERAL_DELIMITER="'",ERROR_DOMAIN$1="tokenizer";function createTokenizer(e,t={}){const n=!1!==t.location,r=createScanner(e),o=()=>r.index(),s=()=>createPosition(r.line(),r.column(),r.index()),c=s(),a=o(),i={currentType:14,offset:a,startLoc:c,endLoc:c,lastType:14,lastOffset:a,lastStartLoc:c,lastEndLoc:c,braceNest:0,inLinked:!1,text:""},u=()=>i,{onError:l}=t;function E(e,t,r){e.endLoc=s(),e.currentType=t;const o={type:t};return n&&(o.loc=createLocation(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const C=e=>E(e,14);function f(e,t){return e.currentChar()===t?(e.next(),t):(CompileErrorCodes.EXPECTED_TOKEN,s(),"")}function d(e){let t="";for(;e.currentPeek()===CHAR_SP||e.currentPeek()===CHAR_LF;)t+=e.currentPeek(),e.peek();return t}function p(e){const t=d(e);return e.skipToPeek(),t}function _(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function L(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=function(e){if(e===EOF)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function N(e){d(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function A(e,t=!0){const n=(t=!1,r="",o=!1)=>{const s=e.currentPeek();return"{"===s?"%"!==r&&t:"@"!==s&&s?"%"===s?(e.peek(),n(t,"%",!0)):"|"===s?!("%"!==r&&!o)||!(r===CHAR_SP||r===CHAR_LF):s===CHAR_SP?(e.peek(),n(!0,CHAR_SP,o)):s!==CHAR_LF||(e.peek(),n(!0,CHAR_LF,o)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function T(e,t){const n=e.currentChar();return n===EOF?EOF:t(n)?(e.next(),n):null}function m(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function k(e){return T(e,m)}function I(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function S(e){return T(e,I)}function P(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function h(e){return T(e,P)}function O(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function D(e){return T(e,O)}function y(e){let t="",n="";for(;t=h(e);)n+=t;return n}function R(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!A(e))break;t+=n,e.next()}else if(n===CHAR_SP||n===CHAR_LF)if(A(e))t+=n,e.next();else{if(N(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function g(e){return e!==LITERAL_DELIMITER&&e!==CHAR_LF}function U(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return b(e,t,4);case"U":return b(e,t,6);default:return CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE,s(),""}}function b(e,t,n){f(e,t);let r="";for(let o=0;o<n;o++){const t=D(e);if(!t){CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE,s(),e.currentChar();break}r+=t}return`\\${t}${r}`}function x(e){return"{"!==e&&"}"!==e&&e!==CHAR_SP&&e!==CHAR_LF}function M(e){p(e);const t=f(e,"|");return p(e),t}function v(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER,s()),e.next(),n=E(t,2,"{"),p(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(CompileErrorCodes.EMPTY_PLACEHOLDER,s()),e.next(),n=E(t,3,"}"),t.braceNest--,t.braceNest>0&&p(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),n=H(e,t)||C(t),t.braceNest=0,n;default:{let r=!0,o=!0,c=!0;if(N(e))return t.braceNest>0&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),n=E(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s(),t.braceNest=0,X(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=_(e.currentPeek());return e.resetPeek(),r}(e,t))return n=E(t,5,function(e){p(e);let t="",n="";for(;t=S(e);)n+=t;return e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),n}(e)),p(e),n;if(o=L(e,t))return n=E(t,6,function(e){p(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${y(e)}`):t+=y(e),e.currentChar()===EOF&&(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE,s()),t}(e)),p(e),n;if(c=function(e,t){const{currentType:n}=t;if(2!==n)return!1;d(e);const r=e.currentPeek()===LITERAL_DELIMITER;return e.resetPeek(),r}(e,t))return n=E(t,7,function(e){p(e),f(e,"'");let t="",n="";for(;t=T(e,g);)n+="\\"===t?U(e):t;const r=e.currentChar();return r===CHAR_LF||r===EOF?(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,s(),r===CHAR_LF&&(e.next(),f(e,"'")),n):(f(e,"'"),n)}(e)),p(e),n;if(!r&&!o&&!c)return n=E(t,13,function(e){p(e);let t="",n="";for(;t=T(e,x);)n+=t;return n}(e)),CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER,s(),n.value,p(e),n;break}}return n}function H(e,t){const{currentType:n}=t;let r=null;const o=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||o!==CHAR_LF&&o!==CHAR_SP||(CompileErrorCodes.INVALID_LINKED_FORMAT,s()),o){case"@":return e.next(),r=E(t,8,"@"),t.inLinked=!0,r;case".":return p(e),e.next(),E(t,9,".");case":":return p(e),e.next(),E(t,10,":");default:return N(e)?(r=E(t,1,M(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;d(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;d(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(p(e),H(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;d(e);const r=_(e.currentPeek());return e.resetPeek(),r}(e,t)?(p(e),E(t,12,function(e){let t="",n="";for(;t=k(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?_(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===CHAR_SP||!t)&&(t===CHAR_LF?(e.peek(),r()):A(e,!1))},o=r();return e.resetPeek(),o}(e,t)?(p(e),"{"===o?v(e,t)||r:E(t,11,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===CHAR_SP?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(8===n&&(CompileErrorCodes.INVALID_LINKED_FORMAT,s()),t.braceNest=0,t.inLinked=!1,X(e,t))}}function X(e,t){let n={type:14};if(t.braceNest>0)return v(e,t)||C(t);if(t.inLinked)return H(e,t)||C(t);switch(e.currentChar()){case"{":return v(e,t)||C(t);case"}":return CompileErrorCodes.UNBALANCED_CLOSING_BRACE,s(),e.next(),E(t,3,"}");case"@":return H(e,t)||C(t);default:{if(N(e))return n=E(t,1,M(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:o}=function(e){const t=d(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return o?E(t,0,R(e)):E(t,4,function(e){p(e);const t=e.currentChar();return"%"!==t&&(CompileErrorCodes.EXPECTED_TOKEN,s()),e.next(),"%"}(e));if(A(e))return E(t,0,R(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:c}=i;return i.lastType=e,i.lastOffset=t,i.lastStartLoc=n,i.lastEndLoc=c,i.offset=o(),i.startLoc=s(),r.currentChar()===EOF?E(i,14):X(r,i)},currentOffset:o,currentPosition:s,context:u}}const ERROR_DOMAIN="parser",KNOWN_ESCAPES=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function fromEscapeSequence(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function createParser(e={}){const t=!1!==e.location,{onError:n,onWarn:r}=e;function o(e,n,r){const o={type:e};return t&&(o.start=n,o.end=n,o.loc={start:r,end:r}),o}function s(e,n,r,o){o&&(e.type=o),t&&(e.end=n,e.loc&&(e.loc.end=r))}function c(e,t){const n=e.context(),r=o(3,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}function a(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:c}=n,a=o(5,r,c);return a.index=parseInt(t,10),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function i(e,t,n){const r=e.context(),{lastOffset:c,lastStartLoc:a}=r,i=o(4,c,a);return i.key=t,!0===n&&(i.modulo=!0),e.nextToken(),s(i,e.currentOffset(),e.currentPosition()),i}function u(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:c}=n,a=o(9,r,c);return a.value=t.replace(KNOWN_ESCAPES,fromEscapeSequence),e.nextToken(),s(a,e.currentOffset(),e.currentPosition()),a}function l(e){const t=e.context(),n=o(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:c}=n,a=o(8,r,c);return 12!==t.type?(CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,a.value="",s(a,r,c),{nextConsumeToken:t,node:a}):(null==t.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,getTokenCaption(t)),a.value=t.value||"",s(a,e.currentOffset(),e.currentPosition()),{node:a})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.key=function(e,t){const n=e.context(),r=o(7,n.offset,n.startLoc);return r.value=t,s(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.key=i(e,r.value||"");break;case 6:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.key=a(e,r.value||"");break;case 7:null==r.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(r)),n.key=u(e,r.value||"");break;default:{CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const c=e.context(),a=o(7,c.offset,c.startLoc);return a.value="",s(a,c.offset,c.startLoc),n.key=a,s(n,c.offset,c.startLoc),{nextConsumeToken:r,node:n}}}return s(n,e.currentOffset(),e.currentPosition()),{node:n}}function E(e){const t=e.context(),n=o(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null,E=null;do{const o=r||e.nextToken();switch(r=null,o.type){case 0:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.items.push(c(e,o.value||""));break;case 6:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.items.push(a(e,o.value||""));break;case 4:E=!0;break;case 5:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.items.push(i(e,o.value||"",!!E)),E&&(CompileWarnCodes.USE_MODULO_SYNTAX,t.lastStartLoc,getTokenCaption(o),E=null);break;case 7:null==o.value&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,getTokenCaption(o)),n.items.push(u(e,o.value||""));break;case 8:{const t=l(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return s(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function C(e){const t=e.context(),{offset:n,startLoc:r}=t,c=E(e);return 14===t.currentType?c:function(e,t,n,r){const c=e.context();let a=0===r.items.length;const i=o(1,t,n);i.cases=[],i.cases.push(r);do{const t=E(e);a||(a=0===t.items.length),i.cases.push(t)}while(14!==c.currentType);return a&&CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL,s(i,e.currentOffset(),e.currentPosition()),i}(e,n,r,c)}return{parse:function(n){const r=createTokenizer(n,assign({},e)),c=r.context(),a=o(0,c.offset,c.startLoc);return t&&a.loc&&(a.loc.source=n),a.body=C(r),e.onCacheKey&&(a.cacheKey=e.onCacheKey(n)),14!==c.currentType&&(CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS,c.lastStartLoc,n[c.offset]),s(a,r.currentOffset(),r.currentPosition()),a}}}function getTokenCaption(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function createTransformer(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}function traverseNodes(e,t){for(let n=0;n<e.length;n++)traverseNode(e[n],t)}function traverseNode(e,t){switch(e.type){case 1:traverseNodes(e.cases,t),t.helper("plural");break;case 2:traverseNodes(e.items,t);break;case 6:traverseNode(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function transform(e,t={}){const n=createTransformer(e);n.helper("normalize"),e.body&&traverseNode(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function optimize(e){const t=e.body;return 2===t.type?optimizeMessageNode(t):t.cases.forEach((e=>optimizeMessageNode(e))),e}function optimizeMessageNode(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=join(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function minify(e){switch(e.t=e.type,e.type){case 0:{const t=e;minify(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)minify(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)minify(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;minify(t.key),t.k=t.key,delete t.key,t.modifier&&(minify(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function createCodeGenerator(e,t){const{sourceMap:n,filename:r,breakLineCode:o,needIndent:s}=t,c=!1!==t.location,a={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:s,indentLevel:0};c&&e.loc&&(a.source=e.loc.source);function i(e,t){a.code+=e}function u(e,t=!0){const n=t?o:"";i(s?n+"  ".repeat(e):n)}return{context:()=>a,push:i,indent:function(e=!0){const t=++a.indentLevel;e&&u(t)},deindent:function(e=!0){const t=--a.indentLevel;e&&u(t)},newline:function(){u(a.indentLevel)},helper:e=>`_${e}`,needIndent:()=>a.needIndent}}function generateLinkedNode(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),generateNode(e,t.key),t.modifier?(e.push(", "),generateNode(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function generateMessageNode(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const o=t.items.length;for(let s=0;s<o&&(generateNode(e,t.items[s]),s!==o-1);s++)e.push(", ");e.deindent(r()),e.push("])")}function generatePluralNode(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const o=t.cases.length;for(let n=0;n<o&&(generateNode(e,t.cases[n]),n!==o-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}function generateResource(e,t){t.body?generateNode(e,t.body):e.push("null")}function generateNode(e,t){const{helper:n}=e;switch(t.type){case 0:generateResource(e,t);break;case 1:generatePluralNode(e,t);break;case 2:generateMessageNode(e,t);break;case 6:generateLinkedNode(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const generate=(e,t={})=>{const n=isString(t.mode)?t.mode:"normal",r=isString(t.filename)?t.filename:"message.intl",o=!!t.sourceMap,s=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",c=t.needIndent?t.needIndent:"arrow"!==n,a=e.helpers||[],i=createCodeGenerator(e,{mode:n,filename:r,sourceMap:o,breakLineCode:s,needIndent:c});i.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),i.indent(c),a.length>0&&(i.push(`const { ${join(a.map((e=>`${e}: _${e}`)),", ")} } = ctx`),i.newline()),i.push("return "),generateNode(i,e),i.deindent(c),i.push("}"),delete e.helpers;const{code:u,map:l}=i.context();return{ast:e,code:u,map:l?l.toJSON():void 0}};function baseCompile(e,t={}){const n=assign({},t),r=!!n.jit,o=!!n.minify,s=null==n.optimize||n.optimize,c=createParser(n).parse(e);return r?(s&&optimize(c),o&&minify(c),{ast:c,code:""}):(transform(c,n),generate(c,n))}export{CompileErrorCodes,CompileWarnCodes,ERROR_DOMAIN,LOCATION_STUB,baseCompile,createCompileError,createCompileWarn,createLocation,createParser,createPosition,defaultOnError,detectHtmlTag,errorMessages,warnMessages};
