# 多语言功能使用指南

## 🌍 概述

TaxAgent 管理系统已完全支持多语言功能，目前支持简体中文和英文。本指南将帮助开发者在所有页面和组件中正确使用多语言功能。

## 🚀 快速开始

### 1. 在组件中使用多语言

```vue
<template>
  <div>
    <!-- 使用 t 函数进行翻译，提供默认值 -->
    <h1>{{ t('user.title', '用户管理') }}</h1>
    
    <!-- 表单标签 -->
    <el-form-item :label="t('user.username', '用户名')">
      <el-input :placeholder="t('user.usernamePlaceholder', '请输入用户名')" />
    </el-form-item>
    
    <!-- 按钮 -->
    <el-button type="primary">{{ t('actions.save', '保存') }}</el-button>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
</script>
```

### 2. 使用页面国际化组合函数

```vue
<script setup>
import { usePageI18n, PAGE_TITLES, COMMON_BUTTONS } from '@/composables/usePageI18n'

const { getPageTitle, getButtonText } = usePageI18n()

// 获取页面标题
const pageTitle = getPageTitle(PAGE_TITLES.users.key, PAGE_TITLES.users.default)

// 获取按钮文本
const addButtonText = getButtonText(COMMON_BUTTONS.add.key, COMMON_BUTTONS.add.default)
</script>
```

## 📋 已实现的多语言功能

### ✅ 完全支持多语言的组件

1. **登录页面** (`/src/views/auth/Login.vue`)
   - 页面标题、表单标签、按钮文本
   - 表单验证消息
   - 成功/失败提示

2. **语言切换器** (`/src/components/LanguageSwitcher.vue`)
   - 支持中英文切换
   - 自动保存用户选择
   - 响应式设计

3. **头部导航** (`/src/layout/components/Header.vue`)
   - 用户下拉菜单
   - 系统消息

4. **侧边栏菜单** (`/src/layout/components/SidebarItem.vue`)
   - 动态菜单标题翻译

5. **面包屑导航** (`/src/layout/components/Breadcrumb.vue`)
   - 路径标题翻译

6. **仪表盘** (`/src/views/dashboard/index.vue`)
   - 统计卡片标题
   - 快捷操作按钮
   - 系统信息标签

### 🔄 需要更新的页面

以下页面需要按照本指南进行多语言更新：

1. **用户管理** (`/src/views/system/users/index.vue`)
2. **角色管理** (`/src/views/system/roles/index.vue`)
3. **权限管理** (`/src/views/system/permissions/index.vue`)
4. **资源管理** (`/src/views/system/resources/index.vue`)
5. **在线用户** (`/src/views/system/online-users/index.vue`)

## 🛠️ 更新步骤

### 步骤 1: 导入国际化函数

```javascript
import { useI18n } from 'vue-i18n'
// 或者使用组合函数
import { usePageI18n } from '@/composables/usePageI18n'

const { t } = useI18n()
// 或者
const { getPageTitle, getButtonText } = usePageI18n()
```

### 步骤 2: 更新模板中的文本

```vue
<!-- 之前 -->
<h1 class="page-title">用户管理</h1>

<!-- 之后 -->
<h1 class="page-title">{{ t('user.title', '用户管理') }}</h1>
```

### 步骤 3: 更新表单标签和占位符

```vue
<!-- 之前 -->
<el-form-item label="用户名">
  <el-input placeholder="请输入用户名" />
</el-form-item>

<!-- 之后 -->
<el-form-item :label="t('user.username', '用户名')">
  <el-input :placeholder="t('user.usernamePlaceholder', '请输入用户名')" />
</el-form-item>
```

### 步骤 4: 更新按钮文本

```vue
<!-- 之前 -->
<el-button type="primary">新增</el-button>

<!-- 之后 -->
<el-button type="primary">{{ t('actions.add', '新增') }}</el-button>
```

### 步骤 5: 更新表格列标题

```vue
<!-- 之前 -->
<el-table-column label="用户名" prop="username" />

<!-- 之后 -->
<el-table-column :label="t('user.username', '用户名')" prop="username" />
```

### 步骤 6: 更新 JavaScript 中的消息

```javascript
// 之前
ElMessage.success('保存成功')

// 之后
ElMessage.success(t('message.saveSuccess', '保存成功'))
```

## 📚 语言包结构

### 中文语言包 (`/src/locales/zh-CN.js`)

```javascript
export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    // ...
  },
  
  // 用户管理
  user: {
    title: '用户管理',
    username: '用户名',
    fullName: '姓名',
    // ...
  },
  
  // 操作按钮
  actions: {
    add: '新增',
    edit: '编辑',
    delete: '删除',
    // ...
  }
}
```

### 英文语言包 (`/src/locales/en-US.js`)

```javascript
export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    // ...
  },
  
  // User Management
  user: {
    title: 'User Management',
    username: 'Username',
    fullName: 'Full Name',
    // ...
  },
  
  // Action Buttons
  actions: {
    add: 'Add',
    edit: 'Edit',
    delete: 'Delete',
    // ...
  }
}
```

## 🎯 最佳实践

### 1. 始终提供默认值

```javascript
// ✅ 推荐：提供默认值
t('user.title', '用户管理')

// ❌ 不推荐：没有默认值
t('user.title')
```

### 2. 使用语义化的键名

```javascript
// ✅ 推荐：语义化键名
t('user.createSuccess', '用户创建成功')

// ❌ 不推荐：不明确的键名
t('msg1', '用户创建成功')
```

### 3. 统一的命名规范

- 页面标题：`[module].title`
- 表单标签：`[module].[fieldName]`
- 占位符：`[module].[fieldName]Placeholder`
- 按钮：`actions.[actionName]`
- 消息：`message.[messageType]`

### 4. 复用常用翻译

```javascript
import { COMMON_BUTTONS, COMMON_LABELS } from '@/composables/usePageI18n'

// 使用预定义的常用翻译
const { getButtonText } = usePageI18n()
const saveText = getButtonText(COMMON_BUTTONS.save.key, COMMON_BUTTONS.save.default)
```

## 🔧 工具函数

### 菜单标题翻译

```javascript
import { getMenuTitle } from '@/utils/menu'

// 获取菜单标题
const title = getMenuTitle('Users') // 返回 '用户管理' 或 'User Management'
```

### 表单验证规则翻译

```javascript
import { commonValidationRules } from '@/utils/i18n-helper'

const rules = {
  username: [
    commonValidationRules.required,
    commonValidationRules.username
  ]
}
```

## 🚀 部署注意事项

1. **构建时检查**：确保所有翻译键都有对应的翻译
2. **测试覆盖**：在两种语言下都要测试功能
3. **性能优化**：大型应用可考虑按需加载语言包

## 📞 支持

如果在实现多语言功能时遇到问题，请参考：

1. Vue I18n 官方文档：https://vue-i18n.intlify.dev/
2. Element Plus 国际化：https://element-plus.org/zh-CN/guide/i18n.html
3. 项目内的示例代码和组合函数
