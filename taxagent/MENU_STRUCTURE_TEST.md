# 菜单结构测试指南

## 🎯 测试目标

验证左侧菜单栏的以下功能：
1. ✅ 二级菜单结构正确显示
2. ✅ 菜单项点击后有选中效果
3. ✅ 子菜单正确嵌套在父菜单下
4. ✅ 多语言支持正常工作
5. ✅ 权限控制正确生效

## 📋 预期菜单结构

```
📊 仪表盘 (Dashboard)
⚙️ 系统管理 (System Management)
  ├── 👤 用户管理 (User Management)
  ├── 🌐 在线用户 (Online Users)
  ├── 👥 角色管理 (Role Management)
  ├── 🔑 权限管理 (Permission Management)
  └── 📁 资源管理 (Resource Management)
```

## 🧪 测试步骤

### 1. 基础菜单结构测试

#### 1.1 检查菜单显示
- [ ] 左侧菜单栏正常显示
- [ ] "仪表盘"作为独立菜单项显示
- [ ] "系统管理"作为父菜单显示，带有展开/收起图标
- [ ] 点击"系统管理"能展开/收起子菜单

#### 1.2 检查子菜单
- [ ] 用户管理显示在系统管理下
- [ ] 在线用户显示在系统管理下
- [ ] 角色管理显示在系统管理下
- [ ] 权限管理显示在系统管理下
- [ ] 资源管理显示在系统管理下
- [ ] 子菜单项有适当的缩进（左边距更大）

### 2. 菜单选中状态测试

#### 2.1 仪表盘选中状态
- [ ] 访问 `/dashboard` 时，"仪表盘"菜单项高亮显示
- [ ] 选中的菜单项有不同的背景色
- [ ] 选中的菜单项右侧有蓝色边框

#### 2.2 子菜单选中状态
- [ ] 访问 `/system/users` 时：
  - [ ] "系统管理"父菜单自动展开
  - [ ] "用户管理"子菜单项高亮显示
  - [ ] 子菜单项有正确的选中样式

- [ ] 访问 `/system/roles` 时：
  - [ ] "系统管理"父菜单保持展开
  - [ ] "角色管理"子菜单项高亮显示
  - [ ] 之前选中的"用户管理"取消高亮

### 3. 菜单导航测试

#### 3.1 点击导航
- [ ] 点击"仪表盘"跳转到 `/dashboard`
- [ ] 点击"用户管理"跳转到 `/system/users`
- [ ] 点击"在线用户"跳转到 `/system/online-users`
- [ ] 点击"角色管理"跳转到 `/system/roles`
- [ ] 点击"权限管理"跳转到 `/system/permissions`
- [ ] 点击"资源管理"跳转到 `/system/resources`

#### 3.2 URL 直接访问
- [ ] 直接访问 `/system/users` 时菜单状态正确
- [ ] 直接访问 `/system/roles` 时菜单状态正确
- [ ] 页面刷新后菜单状态保持正确

### 4. 多语言测试

#### 4.1 中文界面
- [ ] "仪表盘"显示正确
- [ ] "系统管理"显示正确
- [ ] "用户管理"显示正确
- [ ] "在线用户"显示正确
- [ ] "角色管理"显示正确
- [ ] "权限管理"显示正确
- [ ] "资源管理"显示正确

#### 4.2 英文界面
- [ ] 切换到英文后菜单文本更新
- [ ] "Dashboard"显示正确
- [ ] "System Management"显示正确
- [ ] "User Management"显示正确
- [ ] "Online Users"显示正确
- [ ] "Role Management"显示正确
- [ ] "Permission Management"显示正确
- [ ] "Resource Management"显示正确

### 5. 响应式设计测试

#### 5.1 桌面端
- [ ] 菜单完整显示，包含图标和文字
- [ ] 子菜单正确缩进
- [ ] 选中状态清晰可见

#### 5.2 移动端（如果支持）
- [ ] 菜单可以收起/展开
- [ ] 收起时只显示图标
- [ ] 展开时显示完整菜单

### 6. 权限控制测试

#### 6.1 有权限用户
- [ ] 能看到所有有权限的菜单项
- [ ] 能正常访问对应页面

#### 6.2 无权限用户
- [ ] 无权限的菜单项被隐藏
- [ ] 如果父菜单下所有子菜单都无权限，父菜单也被隐藏

## 🎨 样式检查

### 菜单项样式
- [ ] 菜单项高度适中（50px）
- [ ] 图标和文字对齐良好
- [ ] 鼠标悬停有反馈效果

### 选中状态样式
- [ ] 选中的菜单项背景色突出
- [ ] 选中的菜单项右侧有蓝色边框
- [ ] 选中的菜单项文字颜色为主题色

### 子菜单样式
- [ ] 子菜单有适当的左边距（60px）
- [ ] 子菜单背景色略有不同
- [ ] 子菜单选中状态清晰

## 🐛 常见问题排查

### 问题1：菜单不显示
- 检查路由配置是否正确
- 确认 SidebarItem 组件是否正确导入
- 查看浏览器控制台是否有错误

### 问题2：子菜单不显示
- 检查路由的 children 配置
- 确认 SidebarItem 组件支持递归渲染
- 验证权限检查逻辑

### 问题3：选中状态不正确
- 检查 activeMenu 计算逻辑
- 确认路径匹配规则
- 验证 Element Plus 菜单组件配置

### 问题4：多语言不工作
- 确认翻译键是否正确配置
- 检查 getMenuTitle 函数
- 验证语言包是否包含所需翻译

## ✅ 测试完成标准

当所有测试项都通过时，菜单功能应该：

1. **结构清晰**：二级菜单结构合理，层次分明
2. **交互流畅**：点击响应及时，导航准确
3. **状态正确**：选中状态准确反映当前页面
4. **样式美观**：视觉效果良好，符合设计规范
5. **多语言完整**：中英文切换正常
6. **权限有效**：按权限正确显示/隐藏菜单

## 🚀 快速测试流程

1. **访问** `http://localhost:3000`
2. **登录系统**
3. **检查菜单结构** - 确认"系统管理"下有5个子菜单
4. **点击各菜单项** - 验证导航和选中状态
5. **切换语言** - 验证多语言支持
6. **刷新页面** - 确认状态保持

如果所有测试都通过，说明菜单结构和功能已经完全正常！🎉
