{"name": "taxagent-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "migrate-sass": "node migrate-sass.js"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "nprogress": "^0.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "sass": "^1.77.0", "sass-embedded": "^1.77.0", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}}