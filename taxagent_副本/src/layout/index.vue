<template>
  <div class="layout-container">
    <!-- 侧边栏 -->
    <div 
      class="sidebar-container"
      :class="{ 'sidebar-collapsed': sidebarCollapsed }"
    >
      <Sidebar />
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <div class="header-container">
        <Header @toggle-sidebar="toggleSidebar" />
      </div>
      
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <Breadcrumb />
      </div>
      
      <!-- 页面内容 -->
      <div class="content-container">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive>
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>

    <!-- 开发工具 -->
    <DevTools />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Sidebar from './components/Sidebar.vue'
import Header from './components/Header.vue'
import Breadcrumb from './components/Breadcrumb.vue'
import DevTools from '@/components/DevTools.vue'

// 侧边栏折叠状态
const sidebarCollapsed = ref(false)

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}
</script>

<style lang="scss" scoped>
.layout-container {
  display: flex;
  height: 100vh;
  width: 100vw;
}

.sidebar-container {
  width: var(--sidebar-width);
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  
  &.sidebar-collapsed {
    width: var(--sidebar-collapsed-width);
  }
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header-container {
  height: var(--header-height);
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  flex-shrink: 0;
}

.breadcrumb-container {
  height: 50px;
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-lighter);
  display: flex;
  align-items: center;
  padding: 0 20px;
  flex-shrink: 0;
}

.content-container {
  flex: 1;
  overflow: auto;
  background: var(--el-bg-color-page);
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
