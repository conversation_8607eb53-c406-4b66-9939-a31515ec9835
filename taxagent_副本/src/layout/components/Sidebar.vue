<template>
  <div class="sidebar">
    <!-- Logo 区域 -->
    <div class="sidebar-logo">
      <div class="logo-container">
        <div class="logo-icon">T</div>
        <span class="logo-text">TaxAgent</span>
      </div>
    </div>
    
    <!-- 菜单区域 -->
    <div class="sidebar-menu">
      <el-menu
        :default-active="activeMenu"
        :collapse="collapsed"
        :unique-opened="true"
        background-color="var(--el-bg-color)"
        text-color="var(--el-text-color-primary)"
        active-text-color="var(--el-color-primary)"
        @select="handleMenuSelect"
      >
        <SidebarItem
          v-for="route in menuRoutes"
          :key="route.path"
          :route="route"
        />
      </el-menu>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import SidebarItem from './SidebarItem.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 接收父组件传递的折叠状态
const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  // 对于子路由，需要匹配完整路径
  return path
})

// 获取菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  const layoutRoute = routes.find(r => r.name === 'Layout')

  if (!layoutRoute || !layoutRoute.children) {
    return []
  }

  return layoutRoute.children.filter(child => {
    // 过滤掉没有 meta.title 的路由（不显示在菜单中）
    return child.meta?.title
  })
})

// 处理菜单选择
const handleMenuSelect = (index) => {
  if (index !== route.path) {
    router.push(index)
  }
}
</script>

<style lang="scss" scoped>
.sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-logo {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--el-border-color-light);
  
  .logo-container {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .logo-icon {
      width: 32px;
      height: 32px;
      background: linear-gradient(135deg, var(--el-color-primary) 0%, var(--el-color-primary-light-3) 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 700;
      font-size: 18px;
    }
    
    .logo-text {
      font-size: 18px;
      font-weight: 600;
      color: var(--el-text-color-primary);
    }
  }
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  
  :deep(.el-menu) {
    border-right: none;
    
    .el-menu-item {
      height: 50px;
      line-height: 50px;
      
      &:hover {
        background-color: var(--el-color-primary-light-9);
      }
      
      &.is-active {
        background-color: var(--el-color-primary-light-8);
        border-right: 3px solid var(--el-color-primary);
      }
    }
    
    .el-sub-menu {
      .el-sub-menu__title {
        height: 50px;
        line-height: 50px;

        &:hover {
          background-color: var(--el-color-primary-light-9);
        }
      }

      .el-menu-item {
        padding-left: 60px !important;
        background-color: var(--el-fill-color-lighter);

        &:hover {
          background-color: var(--el-color-primary-light-9);
        }

        &.is-active {
          background-color: var(--el-color-primary-light-8);
          border-right: 3px solid var(--el-color-primary);
          color: var(--el-color-primary);
          font-weight: 600;
        }
      }
    }
  }
}
</style>
