<template>
  <div class="dev-tools" v-if="isDev">
    <el-button
      class="dev-tools-toggle"
      type="primary"
      size="small"
      circle
      @click="visible = !visible"
    >
      <el-icon><Tools /></el-icon>
    </el-button>
    
    <el-drawer
      v-model="visible"
      title="开发工具"
      direction="rtl"
      size="400px"
    >
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 系统信息 -->
        <el-tab-pane label="系统信息" name="system">
          <div class="dev-section">
            <h4>应用信息</h4>
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="应用名称">{{ appName }}</el-descriptions-item>
              <el-descriptions-item label="版本">{{ appVersion }}</el-descriptions-item>
              <el-descriptions-item label="环境">{{ nodeEnv }}</el-descriptions-item>
              <el-descriptions-item label="Vue版本">{{ vueVersion }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="dev-section">
            <h4>用户信息</h4>
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="用户名">{{ userInfo?.username || '未登录' }}</el-descriptions-item>
              <el-descriptions-item label="角色">{{ userRoles }}</el-descriptions-item>
              <el-descriptions-item label="权限数量">{{ userPermissions.length }}</el-descriptions-item>
              <el-descriptions-item label="租户ID">{{ userInfo?.tenantId || '无' }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="dev-section">
            <h4>浏览器信息</h4>
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="User Agent">
                <div class="text-ellipsis" :title="userAgent">{{ userAgent }}</div>
              </el-descriptions-item>
              <el-descriptions-item label="语言">{{ navigator.language }}</el-descriptions-item>
              <el-descriptions-item label="时区">{{ Intl.DateTimeFormat().resolvedOptions().timeZone }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
        
        <!-- 路由信息 -->
        <el-tab-pane label="路由信息" name="router">
          <div class="dev-section">
            <h4>当前路由</h4>
            <el-descriptions :column="1" size="small" border>
              <el-descriptions-item label="路径">{{ $route.path }}</el-descriptions-item>
              <el-descriptions-item label="名称">{{ $route.name }}</el-descriptions-item>
              <el-descriptions-item label="参数">{{ JSON.stringify($route.params) }}</el-descriptions-item>
              <el-descriptions-item label="查询">{{ JSON.stringify($route.query) }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="dev-section">
            <h4>路由历史</h4>
            <el-timeline size="small">
              <el-timeline-item
                v-for="(route, index) in routeHistory"
                :key="index"
                :timestamp="route.timestamp"
                size="small"
              >
                <div>{{ route.path }}</div>
                <div class="route-name">{{ route.name }}</div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
        
        <!-- 性能监控 -->
        <el-tab-pane label="性能监控" name="performance">
          <div class="dev-section">
            <div class="section-header">
              <h4>性能指标</h4>
              <el-button size="small" @click="refreshPerformance">刷新</el-button>
            </div>
            
            <div v-if="performanceData.pageLoad">
              <h5>页面加载</h5>
              <el-descriptions :column="2" size="small" border>
                <el-descriptions-item label="DNS">{{ performanceData.pageLoad.dns }}ms</el-descriptions-item>
                <el-descriptions-item label="TCP">{{ performanceData.pageLoad.tcp }}ms</el-descriptions-item>
                <el-descriptions-item label="请求">{{ performanceData.pageLoad.request }}ms</el-descriptions-item>
                <el-descriptions-item label="响应">{{ performanceData.pageLoad.response }}ms</el-descriptions-item>
                <el-descriptions-item label="DOM">{{ performanceData.pageLoad.dom }}ms</el-descriptions-item>
                <el-descriptions-item label="总计">{{ performanceData.pageLoad.total }}ms</el-descriptions-item>
              </el-descriptions>
            </div>
            
            <div v-if="performanceData.longTasks">
              <h5>长任务</h5>
              <el-descriptions :column="1" size="small" border>
                <el-descriptions-item label="数量">{{ performanceData.longTasks.count }}</el-descriptions-item>
                <el-descriptions-item label="总时长">{{ performanceData.longTasks.totalDuration.toFixed(2) }}ms</el-descriptions-item>
                <el-descriptions-item label="平均时长">{{ performanceData.longTasks.averageDuration.toFixed(2) }}ms</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
          
          <div class="dev-section">
            <el-button size="small" @click="exportPerformanceReport">导出性能报告</el-button>
          </div>
        </el-tab-pane>
        
        <!-- 错误日志 -->
        <el-tab-pane label="错误日志" name="errors">
          <div class="dev-section">
            <div class="section-header">
              <h4>错误日志 ({{ errorLog.length }})</h4>
              <div>
                <el-button size="small" @click="clearErrorLog">清空</el-button>
                <el-button size="small" @click="exportErrorLog">导出</el-button>
              </div>
            </div>
            
            <div v-if="errorLog.length === 0" class="empty-state">
              <el-empty description="暂无错误日志" />
            </div>
            
            <el-timeline v-else size="small">
              <el-timeline-item
                v-for="(error, index) in errorLog.slice(0, 10)"
                :key="index"
                :timestamp="formatTime(error.timestamp)"
                type="danger"
                size="small"
              >
                <div class="error-item">
                  <div class="error-message">{{ error.error.message }}</div>
                  <div class="error-type">{{ error.error.type }}</div>
                  <el-button
                    size="small"
                    text
                    @click="showErrorDetail(error)"
                  >
                    详情
                  </el-button>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
        
        <!-- 工具 -->
        <el-tab-pane label="工具" name="tools">
          <div class="dev-section">
            <h4>缓存管理</h4>
            <div class="tool-buttons">
              <el-button size="small" @click="clearLocalStorage">清空 LocalStorage</el-button>
              <el-button size="small" @click="clearSessionStorage">清空 SessionStorage</el-button>
              <el-button size="small" @click="clearCookies">清空 Cookies</el-button>
            </div>
          </div>
          
          <div class="dev-section">
            <h4>状态管理</h4>
            <div class="tool-buttons">
              <el-button size="small" @click="showStoreState">查看 Store 状态</el-button>
              <el-button size="small" @click="resetAuthStore">重置认证状态</el-button>
            </div>
          </div>
          
          <div class="dev-section">
            <h4>模拟操作</h4>
            <div class="tool-buttons">
              <el-button size="small" @click="simulateError">模拟错误</el-button>
              <el-button size="small" @click="simulateNetworkError">模拟网络错误</el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
    
    <!-- 错误详情对话框 -->
    <el-dialog
      v-model="errorDetailVisible"
      title="错误详情"
      width="600px"
    >
      <pre class="error-detail">{{ errorDetailContent }}</pre>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, inject } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Tools } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { version } from 'vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 注入的工具
const performanceMonitor = inject('$performanceMonitor')
const errorHandler = inject('$errorHandler')

// 响应式数据
const visible = ref(false)
const activeTab = ref('system')
const errorDetailVisible = ref(false)
const errorDetailContent = ref('')
const routeHistory = ref([])
const performanceData = ref({})

// 计算属性
const isDev = computed(() => process.env.NODE_ENV === 'development')
const appName = computed(() => 'TaxAgent 管理系统')
const appVersion = computed(() => '1.0.0')
const nodeEnv = computed(() => process.env.NODE_ENV)
const vueVersion = computed(() => version)
const userAgent = computed(() => navigator.userAgent)
const userInfo = computed(() => authStore.userInfo)
const userRoles = computed(() => authStore.roles.join(', ') || '无')
const userPermissions = computed(() => authStore.permissions)
const errorLog = computed(() => errorHandler?.getErrorLog() || [])

// 监听路由变化
router.afterEach((to) => {
  routeHistory.value.unshift({
    path: to.path,
    name: to.name,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 限制历史记录数量
  if (routeHistory.value.length > 20) {
    routeHistory.value = routeHistory.value.slice(0, 20)
  }
})

// 刷新性能数据
const refreshPerformance = () => {
  if (performanceMonitor) {
    const report = performanceMonitor.generateReport()
    performanceData.value = report.metrics || {}
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString()
}

// 显示错误详情
const showErrorDetail = (error) => {
  errorDetailContent.value = JSON.stringify(error, null, 2)
  errorDetailVisible.value = true
}

// 清空错误日志
const clearErrorLog = () => {
  errorHandler?.clearErrorLog()
}

// 导出错误日志
const exportErrorLog = () => {
  errorHandler?.exportErrorLog()
}

// 导出性能报告
const exportPerformanceReport = () => {
  performanceMonitor?.exportReport()
}

// 清空缓存
const clearLocalStorage = () => {
  localStorage.clear()
  ElMessage.success('LocalStorage 已清空')
}

const clearSessionStorage = () => {
  sessionStorage.clear()
  ElMessage.success('SessionStorage 已清空')
}

const clearCookies = () => {
  document.cookie.split(";").forEach(cookie => {
    const eqPos = cookie.indexOf("=")
    const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
    document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
  })
  ElMessage.success('Cookies 已清空')
}

// 显示 Store 状态
const showStoreState = () => {
  console.log('Auth Store State:', authStore.$state)
  ElMessage.info('Store 状态已输出到控制台')
}

// 重置认证状态
const resetAuthStore = () => {
  authStore.clearAuth()
  ElMessage.success('认证状态已重置')
}

// 模拟错误
const simulateError = () => {
  throw new Error('这是一个模拟错误')
}

// 模拟网络错误
const simulateNetworkError = () => {
  fetch('/api/non-existent-endpoint')
    .catch(error => console.error('模拟网络错误:', error))
}

// 组件挂载时初始化
onMounted(() => {
  refreshPerformance()
})
</script>

<style lang="scss" scoped>
.dev-tools {
  position: fixed;
  top: 50%;
  right: 20px;
  z-index: 9999;
  transform: translateY(-50%);
}

.dev-tools-toggle {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dev-section {
  margin-bottom: 20px;
  
  h4, h5 {
    margin: 0 0 12px 0;
    color: var(--el-text-color-primary);
  }
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
}

.route-name {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.error-item {
  .error-message {
    font-weight: 500;
    margin-bottom: 4px;
  }
  
  .error-type {
    font-size: 12px;
    color: var(--el-text-color-regular);
    margin-bottom: 8px;
  }
}

.error-detail {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 400px;
  overflow-y: auto;
}

.tool-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 20px;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
}
</style>
