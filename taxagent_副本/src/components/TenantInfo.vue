<template>
  <div class="tenant-info" v-if="showTenantInfo">
    <el-card class="tenant-card">
      <template #header>
        <div class="tenant-header">
          <el-icon><OfficeBuilding /></el-icon>
          <span>租户信息</span>
        </div>
      </template>
      
      <div class="tenant-content">
        <div class="tenant-item">
          <span class="label">租户名称：</span>
          <span class="value">{{ tenantInfo.tenantName || '未知租户' }}</span>
        </div>
        
        <div class="tenant-item">
          <span class="label">租户编码：</span>
          <span class="value">{{ tenantInfo.tenantCode || '未知' }}</span>
        </div>
        
        <div class="tenant-item">
          <span class="label">管理范围：</span>
          <el-tag :type="getScopeType()" size="small">
            {{ getScopeText() }}
          </el-tag>
        </div>
        
        <div class="tenant-item" v-if="!isSuperAdmin">
          <span class="label">权限说明：</span>
          <div class="permission-tips">
            <el-alert
              :title="getPermissionTips()"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { OfficeBuilding } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useTenantPermission } from '@/utils/tenant'

// 定义属性
const props = defineProps({
  show: {
    type: Boolean,
    default: true
  }
})

const authStore = useAuthStore()
const tenantPermission = useTenantPermission()

// 是否显示租户信息
const showTenantInfo = computed(() => {
  return props.show && authStore.isAuthenticated
})

// 租户信息
const tenantInfo = computed(() => {
  return authStore.userInfo?.tenant || {}
})

// 是否为超级管理员
const isSuperAdmin = computed(() => {
  return tenantPermission.isSuperAdmin()
})

// 获取管理范围类型
const getScopeType = () => {
  if (tenantPermission.isSuperAdmin()) {
    return 'danger'
  } else if (tenantPermission.isTenantAdmin()) {
    return 'warning'
  } else {
    return 'info'
  }
}

// 获取管理范围文本
const getScopeText = () => {
  if (tenantPermission.isSuperAdmin()) {
    return '全平台管理'
  } else if (tenantPermission.isTenantAdmin()) {
    return '租户管理'
  } else {
    return '普通用户'
  }
}

// 获取权限提示
const getPermissionTips = () => {
  if (tenantPermission.isTenantAdmin()) {
    return '您是租户管理员，只能管理当前租户下的用户和数据'
  } else {
    return '您是普通用户，只能查看和操作自己的数据'
  }
}
</script>

<style lang="scss" scoped>
.tenant-info {
  margin-bottom: 20px;
}

.tenant-card {
  .tenant-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: var(--el-text-color-primary);
  }
}

.tenant-content {
  .tenant-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      min-width: 80px;
    }
    
    .value {
      color: var(--el-text-color-primary);
    }
    
    .permission-tips {
      flex: 1;
      margin-left: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tenant-content {
    .tenant-item {
      flex-direction: column;
      align-items: flex-start;
      
      .label {
        margin-bottom: 4px;
      }
      
      .permission-tips {
        margin-left: 0;
        width: 100%;
      }
    }
  }
}
</style>
