<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="showFallback">
    <slot name="fallback">
      <el-empty 
        description="您没有访问权限" 
        :image-size="100"
      />
    </slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePermission } from '@/composables/usePermission'

const props = defineProps({
  // 需要的权限
  permission: {
    type: [String, Array],
    default: null
  },
  // 需要的角色
  role: {
    type: [String, Array],
    default: null
  },
  // 权限检查模式：'all' 需要所有权限，'any' 需要任意一个权限
  mode: {
    type: String,
    default: 'all',
    validator: (value) => ['all', 'any'].includes(value)
  },
  // 是否显示无权限时的占位内容
  showFallback: {
    type: Boolean,
    default: false
  }
})

const { hasPermission, hasRole, hasAnyPermission, hasAnyRole } = usePermission()

// 检查是否有访问权限
const hasAccess = computed(() => {
  let permissionCheck = true
  let roleCheck = true

  // 检查权限
  if (props.permission) {
    if (typeof props.permission === 'string') {
      permissionCheck = hasPermission(props.permission)
    } else if (Array.isArray(props.permission)) {
      if (props.mode === 'any') {
        permissionCheck = hasAnyPermission(props.permission)
      } else {
        permissionCheck = props.permission.every(p => hasPermission(p))
      }
    }
  }

  // 检查角色
  if (props.role && permissionCheck) {
    if (typeof props.role === 'string') {
      roleCheck = hasRole(props.role)
    } else if (Array.isArray(props.role)) {
      if (props.mode === 'any') {
        roleCheck = hasAnyRole(props.role)
      } else {
        roleCheck = props.role.every(r => hasRole(r))
      }
    }
  }

  return permissionCheck && roleCheck
})
</script>

<style scoped>
/* 权限包装器样式 */
</style>
