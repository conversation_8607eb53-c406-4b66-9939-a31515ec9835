<template>
  <el-dropdown 
    trigger="click" 
    @command="handleLanguageChange"
    class="language-switcher"
  >
    <div class="language-trigger">
      <span class="language-flag">{{ currentLanguage.flag }}</span>
      <span class="language-text">{{ currentLanguage.name }}</span>
      <el-icon class="arrow-icon">
        <ArrowDown />
      </el-icon>
    </div>
    
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item 
          v-for="lang in SUPPORT_LOCALES" 
          :key="lang.value"
          :command="lang.value"
          :class="{ 'is-active': lang.value === currentLocale }"
        >
          <span class="language-flag">{{ lang.flag }}</span>
          <span class="language-name">{{ lang.name }}</span>
          <el-icon v-if="lang.value === currentLocale" class="check-icon">
            <Check />
          </el-icon>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { ArrowDown, Check } from '@element-plus/icons-vue'
import { SUPPORT_LOCALES, setLocale } from '@/locales'
import { ElMessage } from 'element-plus'

const { locale, t } = useI18n()

// 当前语言
const currentLocale = computed(() => locale.value)

// 当前语言信息
const currentLanguage = computed(() => {
  return SUPPORT_LOCALES.find(lang => lang.value === currentLocale.value) || SUPPORT_LOCALES[0]
})

// 处理语言切换
const handleLanguageChange = (langValue) => {
  if (langValue === currentLocale.value) {
    return
  }
  
  try {
    setLocale(langValue)

    ElMessage.success({
      message: t('message.operationSuccess'),
      duration: 2000
    })
    
    // 可选：刷新页面以确保所有组件都使用新语言
    // setTimeout(() => {
    //   window.location.reload()
    // }, 500)
    
  } catch (error) {
    console.error('Language switch failed:', error)
    ElMessage.error(t('message.operationFailed'))
  }
}
</script>

<style lang="scss" scoped>
.language-switcher {
  .language-trigger {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    user-select: none;
    
    &:hover {
      background-color: var(--el-fill-color-light);
    }
    
    .language-flag {
      margin-right: 6px;
      font-size: 16px;
    }
    
    .language-text {
      margin-right: 6px;
      font-size: 14px;
      color: var(--el-text-color-primary);
      white-space: nowrap;
    }
    
    .arrow-icon {
      font-size: 12px;
      color: var(--el-text-color-regular);
      transition: transform 0.3s ease;
    }
  }
  
  &.is-active .language-trigger .arrow-icon {
    transform: rotate(180deg);
  }
}

:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  
  .language-flag {
    margin-right: 8px;
    font-size: 16px;
  }
  
  .language-name {
    flex: 1;
    font-size: 14px;
  }
  
  .check-icon {
    margin-left: 8px;
    font-size: 14px;
    color: var(--el-color-primary);
  }
  
  &.is-active {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .language-switcher .language-trigger {
    padding: 6px 8px;
    
    .language-text {
      display: none;
    }
  }
}
</style>
