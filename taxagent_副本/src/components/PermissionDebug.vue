<template>
  <div class="permission-debug" v-if="showDebug">
    <el-card class="debug-card">
      <template #header>
        <div class="debug-header">
          <span>权限调试信息</span>
          <el-button @click="toggleDebug" size="small" type="danger">关闭</el-button>
        </div>
      </template>
      
      <div class="debug-content">
        <h4>当前用户信息</h4>
        <pre>{{ JSON.stringify(currentUser, null, 2) }}</pre>
        
        <h4>用户角色</h4>
        <el-tag v-for="role in currentRoles" :key="role" class="role-tag">
          {{ role }}
        </el-tag>
        <div v-if="!currentRoles.length" class="no-data">暂无角色</div>
        
        <h4>用户权限</h4>
        <el-tag v-for="permission in currentPermissions" :key="permission" class="permission-tag" type="success">
          {{ permission }}
        </el-tag>
        <div v-if="!currentPermissions.length" class="no-data">暂无权限</div>
        
        <h4>权限检查</h4>
        <div class="permission-checks">
          <div class="check-item">
            <span>platform:user:manage:</span>
            <el-tag :type="hasUserManage ? 'success' : 'danger'">
              {{ hasUserManage ? '有权限' : '无权限' }}
            </el-tag>
          </div>
          <div class="check-item">
            <span>platform:role:manage:</span>
            <el-tag :type="hasRoleManage ? 'success' : 'danger'">
              {{ hasRoleManage ? '有权限' : '无权限' }}
            </el-tag>
          </div>
          <div class="check-item">
            <span>super_admin 角色:</span>
            <el-tag :type="isSuperAdmin ? 'success' : 'danger'">
              {{ isSuperAdmin ? '是' : '否' }}
            </el-tag>
          </div>
        </div>
        
        <div class="debug-actions">
          <el-button @click="refreshPermissions" type="primary" size="small">
            刷新权限
          </el-button>
          <el-button @click="grantTestPermissions" type="warning" size="small">
            临时授权测试权限
          </el-button>
        </div>
      </div>
    </el-card>
  </div>
  
  <el-button 
    v-else
    @click="toggleDebug" 
    class="debug-toggle"
    size="small"
    type="info"
    circle
  >
    🐛
  </el-button>
</template>

<script setup>
import { ref, computed } from 'vue'
import { usePermission } from '@/composables/usePermission'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const showDebug = ref(false)
const authStore = useAuthStore()

const {
  currentUser,
  currentRoles,
  currentPermissions,
  hasPermission,
  isSuperAdmin
} = usePermission()

// 权限检查
const hasUserManage = computed(() => hasPermission('platform:user:manage'))
const hasRoleManage = computed(() => hasPermission('platform:role:manage'))

// 切换调试面板
const toggleDebug = () => {
  showDebug.value = !showDebug.value
}

// 刷新权限
const refreshPermissions = async () => {
  try {
    await authStore.refreshPermissions()
    ElMessage.success('权限已刷新')
  } catch (error) {
    ElMessage.error('刷新权限失败')
  }
}

// 临时授权测试权限（仅用于调试）
const grantTestPermissions = () => {
  // 临时添加测试权限到当前用户
  const testPermissions = [
    'platform:user:manage',
    'platform:role:manage',
    'platform:resource:manage'
  ]
  
  testPermissions.forEach(permission => {
    if (!authStore.permissions.includes(permission)) {
      authStore.permissions.push(permission)
    }
  })
  
  // 确保有超级管理员角色
  if (!authStore.roles.includes('super_admin')) {
    authStore.roles.push('super_admin')
  }
  
  ElMessage.success('已临时授权测试权限，请刷新页面查看效果')
}
</script>

<style lang="scss" scoped>
.permission-debug {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.debug-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debug-content {
  h4 {
    margin: 16px 0 8px 0;
    color: #409eff;
  }
  
  pre {
    background: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    font-size: 12px;
    max-height: 150px;
    overflow-y: auto;
  }
}

.role-tag, .permission-tag {
  margin: 2px 4px 2px 0;
}

.no-data {
  color: #999;
  font-style: italic;
}

.permission-checks {
  .check-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 8px 0;
    
    span {
      font-weight: 500;
    }
  }
}

.debug-actions {
  margin-top: 16px;
  text-align: center;
  
  .el-button {
    margin: 0 4px;
  }
}

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}
</style>
