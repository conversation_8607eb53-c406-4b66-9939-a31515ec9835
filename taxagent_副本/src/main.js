import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import App from './App.vue'
import router from './router'
import './styles/index.scss'

// 导入插件
import { errorPlugin } from './utils/error'
import { performancePlugin } from './utils/performance'

// 导入权限指令
import permissionDirectives from './directives/permission'

// 导入国际化
import i18n from './locales'

const app = createApp(App)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册权限指令
Object.keys(permissionDirectives).forEach(key => {
  app.directive(key, permissionDirectives[key])
})

// 注册插件
app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(ElementPlus)
app.use(errorPlugin)
app.use(performancePlugin)

// 全局属性
app.config.globalProperties.$APP_NAME = 'TaxAgent 管理系统'
app.config.globalProperties.$APP_VERSION = '1.0.0'

// 开发环境下的调试工具
if (process.env.NODE_ENV === 'development') {
  app.config.performance = true

  // 添加全局调试方法
  window.__APP__ = app
  window.__ROUTER__ = router
}

app.mount('#app')
