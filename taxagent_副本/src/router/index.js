import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useTenantPermission } from '@/utils/tenant'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置 NProgress
NProgress.configure({ showSpinner: false })

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        meta: {
          title: '首页',
          icon: 'Odometer'
        }
      },
      {
        path: 'system',
        name: 'System',
        redirect: '/system/users',
        meta: {
          title: '系统管理',
          icon: 'Setting'
        },
        children: [
          {
            path: 'users',
            name: 'Users',
            component: () => import('@/views/system/users/index.vue'),
            meta: {
              title: '用户管理',
              icon: 'User',
              permission: 'platform:user:manage'
            }
          },
          {
            path: 'online-users',
            name: 'OnlineUsers',
            component: () => import('@/views/system/online-users/index.vue'),
            meta: {
              title: '在线用户',
              icon: 'UserFilled',
              permission: 'platform:user:manage'
            }
          },
          {
            path: 'roles',
            name: 'Roles',
            component: () => import('@/views/system/roles/index.vue'),
            meta: {
              title: '角色管理',
              icon: 'Avatar',
              permission: 'platform:role:manage'
            }
          },
          {
            path: 'permissions',
            name: 'Permissions',
            component: () => import('@/views/system/permissions/index.vue'),
            meta: {
              title: '权限管理',
              icon: 'Key',
              permission: 'platform:role:manage'
            }
          },
          {
            path: 'resources',
            name: 'Resources',
            component: () => import('@/views/system/resources/index.vue'),
            meta: {
              title: '资源管理',
              icon: 'Menu',
              permission: 'platform:resource:manage'
            }
          }
        ]
      },

    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: { title: '页面不存在' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - TaxAgent 管理系统`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      // 尝试从本地存储恢复登录状态
      await authStore.checkAuth()

      if (!authStore.isAuthenticated) {
        // 如果访问的不是登录页，则跳转到登录页
        if (to.path !== '/login') {
          next('/login')
          return
        }
      }
    }

    // 检查基本权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission)) {
      ElMessage.error('您没有访问该页面的权限')
      next('/dashboard')
      return
    }

    // 检查租户权限
    const tenantPermission = useTenantPermission()

    // 系统管理功能只有超级管理员可以访问
    if (to.path.startsWith('/system') && !tenantPermission.canAccessSystemManagement()) {
      // 租户管理员可以访问部分系统管理功能
      const allowedPaths = ['/users', '/online-users']
      const isAllowed = allowedPaths.some(path => to.path.includes(path))

      if (!isAllowed || !tenantPermission.canAccessTenantManagement()) {
        ElMessage.error('您没有访问该功能的权限')
        next('/dashboard')
        return
      }
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }

  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
