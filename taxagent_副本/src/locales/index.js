import { createI18n } from 'vue-i18n'
import zhCN from './zh-CN'
import enUS from './en-US'

// 支持的语言列表
export const SUPPORT_LOCALES = [
  {
    name: '简体中文',
    value: 'zh-CN',
    flag: '🇨🇳'
  },
  {
    name: 'English',
    value: 'en-US',
    flag: '🇺🇸'
  }
]

// 获取浏览器默认语言
function getDefaultLocale() {
  const locale = navigator.language || navigator.userLanguage
  
  // 检查是否支持该语言
  const supportedLocale = SUPPORT_LOCALES.find(item => 
    item.value === locale || item.value.startsWith(locale.split('-')[0])
  )
  
  return supportedLocale ? supportedLocale.value : 'zh-CN'
}

// 从本地存储获取语言设置
function getStoredLocale() {
  return localStorage.getItem('locale') || getDefaultLocale()
}

// 保存语言设置到本地存储
export function setLocale(locale) {
  localStorage.setItem('locale', locale)
  i18n.global.locale.value = locale
  
  // 更新 HTML lang 属性
  document.documentElement.lang = locale
  
  // 更新页面标题
  updatePageTitle()
}

// 更新页面标题
function updatePageTitle() {
  try {
    const title = i18n.global.t('system.title')
    document.title = title
  } catch (error) {
    console.warn('Failed to update page title:', error)
    document.title = 'TaxAgent 管理系统'
  }
}

// 创建 i18n 实例
const i18n = createI18n({
  legacy: false, // 使用 Composition API
  locale: getStoredLocale(),
  fallbackLocale: 'zh-CN',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  },
  globalInjection: true, // 全局注入 $t 函数
  silentTranslationWarn: true, // 静默翻译警告
  silentFallbackWarn: true // 静默回退警告
})

// 设置初始语言
document.documentElement.lang = i18n.global.locale.value

export default i18n

// 导出常用函数
export const { t } = i18n.global
