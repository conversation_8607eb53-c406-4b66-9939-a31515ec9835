// 系统常量定义

// 用户状态
export const USER_STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
  LOCKED: 1,
  UNLOCKED: 0
}

// 用户状态文本映射
export const USER_STATUS_TEXT = {
  [USER_STATUS.ACTIVE]: '正常',
  [USER_STATUS.INACTIVE]: '禁用',
  [USER_STATUS.LOCKED]: '锁定',
  [USER_STATUS.UNLOCKED]: '解锁'
}

// 用户状态颜色映射
export const USER_STATUS_COLOR = {
  [USER_STATUS.ACTIVE]: 'success',
  [USER_STATUS.INACTIVE]: 'danger',
  [USER_STATUS.LOCKED]: 'warning',
  [USER_STATUS.UNLOCKED]: 'info'
}

// 角色类型
export const ROLE_TYPE = {
  PLATFORM: 'platform',
  TENANT: 'tenant',
  USER: 'user'
}

// 角色类型文本映射
export const ROLE_TYPE_TEXT = {
  [ROLE_TYPE.PLATFORM]: '平台角色',
  [ROLE_TYPE.TENANT]: '租户角色',
  [ROLE_TYPE.USER]: '用户角色'
}

// 资源类型
export const RESOURCE_TYPE = {
  MENU: 'menu',
  BUTTON: 'button',
  API: 'api'
}

// 资源类型文本映射
export const RESOURCE_TYPE_TEXT = {
  [RESOURCE_TYPE.MENU]: '菜单',
  [RESOURCE_TYPE.BUTTON]: '按钮',
  [RESOURCE_TYPE.API]: '接口'
}

// 资源类型颜色映射
export const RESOURCE_TYPE_COLOR = {
  [RESOURCE_TYPE.MENU]: 'primary',
  [RESOURCE_TYPE.BUTTON]: 'success',
  [RESOURCE_TYPE.API]: 'warning'
}

// 设备类型
export const DEVICE_TYPE = {
  PC: 'PC',
  MOBILE: 'Mobile',
  TABLET: 'Tablet'
}

// 设备类型颜色映射
export const DEVICE_TYPE_COLOR = {
  [DEVICE_TYPE.PC]: 'primary',
  [DEVICE_TYPE.MOBILE]: 'success',
  [DEVICE_TYPE.TABLET]: 'info'
}

// 权限编码前缀
export const PERMISSION_PREFIX = {
  PLATFORM: 'platform',
  TENANT: 'tenant',
  SYSTEM: 'system'
}

// 系统角色
export const SYSTEM_ROLES = {
  SUPER_ADMIN: 'super_admin',
  TENANT_ADMIN: 'tenant_admin',
  USER: 'user'
}

// 系统权限
export const SYSTEM_PERMISSIONS = {
  USER_MANAGE: 'platform:user:manage',
  ROLE_MANAGE: 'platform:role:manage',
  RESOURCE_MANAGE: 'platform:resource:manage',
  TENANT_MANAGE: 'platform:tenant:manage'
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZES: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 1000
}

// 表单验证规则
export const VALIDATION_RULES = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z0-9_]+$/
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    MAX_LENGTH: 32,
    PATTERN: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  PHONE: {
    PATTERN: /^1[3-9]\d{9}$/
  }
}

// HTTP 状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500
}

// 业务状态码
export const BUSINESS_CODE = {
  SUCCESS: 0,
  FAIL: -1,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  VALIDATION_ERROR: 422,
  SERVER_ERROR: 500
}

// 存储键名
export const STORAGE_KEYS = {
  TOKEN: 'taxagent_token',
  USER_INFO: 'taxagent_user_info',
  PERMISSIONS: 'taxagent_permissions',
  ROLES: 'taxagent_roles',
  THEME: 'taxagent_theme',
  LANGUAGE: 'taxagent_language'
}

// 主题配置
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
}

// 语言配置
export const LANGUAGE = {
  ZH_CN: 'zh-cn',
  EN_US: 'en-us'
}

// 日期格式
export const DATE_FORMAT = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIMESTAMP: 'YYYY-MM-DD HH:mm:ss.SSS'
}

// 文件大小限制（字节）
export const FILE_SIZE_LIMIT = {
  AVATAR: 2 * 1024 * 1024, // 2MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  IMAGE: 5 * 1024 * 1024 // 5MB
}

// 支持的文件类型
export const FILE_TYPES = {
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz']
}

// 默认头像
export const DEFAULT_AVATAR = '/images/default-avatar.png'

// 系统配置
export const SYSTEM_CONFIG = {
  APP_NAME: 'TaxAgent 管理系统',
  APP_VERSION: '1.0.0',
  COPYRIGHT: '© 2024 TaxAgent. All rights reserved.',
  COMPANY: 'TaxAgent Technology Co., Ltd.'
}
