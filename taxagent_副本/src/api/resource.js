import request from '@/utils/request'

export const resourceApi = {
  // 创建资源
  createResource(data) {
    return request({
      url: '/v1/sys/resources',
      method: 'post',
      data
    })
  },

  // 更新资源
  updateResource(resourceId, data) {
    return request({
      url: `/v1/sys/resources/${resourceId}`,
      method: 'put',
      data
    })
  },

  // 删除资源
  deleteResource(resourceId) {
    return request({
      url: `/v1/sys/resources/${resourceId}`,
      method: 'delete'
    })
  },

  // 根据ID获取资源
  getResourceById(resourceId) {
    return request({
      url: `/v1/sys/resources/${resourceId}`,
      method: 'get'
    })
  },

  // 获取所有资源
  getAllResources(params) {
    return request({
      url: '/v1/sys/resources',
      method: 'get',
      params
    })
  },

  // 获取资源树
  getResourceTree() {
    return request({
      url: '/v1/sys/resources/tree',
      method: 'get'
    })
  }
}
