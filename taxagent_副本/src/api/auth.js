import request from '@/utils/request'

export const authApi = {
  // 用户登录
  login(data) {
    return request({
      url: '/v1/auth/login',
      method: 'post',
      data
    })
  },

  // 用户登出
  logout() {
    return request({
      url: '/v1/auth/logout',
      method: 'post'
    })
  },

  // 获取当前用户权限
  getCurrentUserPermissions() {
    return request({
      url: '/v1/auth/permissions',
      method: 'get'
    })
  },

  // 根据用户ID获取用户权限
  getUserPermissions(userId) {
    return request({
      url: `/v1/auth/permissions/${userId}`,
      method: 'get'
    })
  }
}
