import request from '@/utils/request'

export const roleApi = {
  // 创建角色
  createRole(data) {
    return request({
      url: '/v1/sys/roles',
      method: 'post',
      data
    })
  },

  // 更新角色
  updateRole(roleId, data) {
    return request({
      url: `/v1/sys/roles/${roleId}`,
      method: 'put',
      data
    })
  },

  // 删除角色
  deleteRole(roleId) {
    return request({
      url: `/v1/sys/roles/${roleId}`,
      method: 'delete'
    })
  },

  // 根据ID获取角色
  getRoleById(roleId) {
    return request({
      url: `/v1/sys/roles/${roleId}`,
      method: 'get'
    })
  },

  // 获取所有角色
  getAllRoles() {
    return request({
      url: '/v1/sys/roles',
      method: 'get'
    })
  },

  // 获取角色的权限列表
  getRolePermissions(roleId) {
    return request({
      url: `/v1/sys/roles/${roleId}/permissions`,
      method: 'get'
    })
  },

  // 为角色分配权限
  assignPermissions(roleId, permCodes) {
    return request({
      url: `/v1/sys/roles/${roleId}/permissions`,
      method: 'post',
      data: { permCodes }
    })
  }
}
