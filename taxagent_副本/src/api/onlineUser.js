import request from '@/utils/request'

export const onlineUserApi = {
  // 获取在线用户列表
  getOnlineUsers() {
    return request({
      url: '/v1/online-users',
      method: 'get'
    })
  },

  // 获取指定用户的在线会话信息
  getOnlineUserSession(userId) {
    return request({
      url: `/v1/online-users/${userId}`,
      method: 'get'
    })
  },

  // 强制用户下线
  forceUserOffline(userId) {
    return request({
      url: `/v1/online-users/${userId}`,
      method: 'delete'
    })
  },

  // 批量强制用户下线
  batchForceUserOffline(userIds) {
    return request({
      url: '/v1/online-users/batch',
      method: 'delete',
      data: { userIds }
    })
  },

  // 检查用户是否在线
  isUserOnline(userId) {
    return request({
      url: `/v1/online-users/${userId}/status`,
      method: 'get'
    })
  }
}
