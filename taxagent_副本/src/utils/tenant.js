// 租户权限控制工具

import { useAuthStore } from '@/stores/auth'

// 租户权限控制类
export class TenantPermissionController {
  constructor() {
    // 不在构造函数中初始化 store，而是在需要时获取
    this.authStore = null
  }

  // 获取 auth store 实例
  getAuthStore() {
    if (!this.authStore) {
      this.authStore = useAuthStore()
    }
    return this.authStore
  }

  // 获取当前用户的租户ID
  getCurrentTenantId() {
    return this.getAuthStore().userInfo?.tenantId || null
  }

  // 检查是否为超级管理员
  isSuperAdmin() {
    return this.getAuthStore().hasRole('super_admin')
  }

  // 检查是否为管理员
  isPlatformAdmin() {
    return this.getAuthStore().hasRole('platform_admin')
  }

  // 检查是否为租户管理员
  isTenantAdmin() {
    return this.getAuthStore().hasRole('tenant_admin')
  }

  // 检查是否可以管理指定用户
  canManageUser(targetUser) {
    // 超级管理员可以管理所有用户
    if (this.isSuperAdmin()) {
      return true
    }

    // 租户管理员只能管理同租户下的用户
    if (this.isTenantAdmin()) {
      const currentTenantId = this.getCurrentTenantId()
      return targetUser.tenantId === currentTenantId
    }

    // 普通用户不能管理其他用户
    return false
  }

  // 检查是否可以查看指定用户
  canViewUser(targetUser) {
    // 超级管理员可以查看所有用户
    if (this.isSuperAdmin()) {
      return true
    }

    // 租户管理员和普通用户只能查看同租户下的用户
    const currentTenantId = this.getCurrentTenantId()
    return targetUser.tenantId === currentTenantId
  }

  // 过滤用户列表（根据租户权限）
  filterUserList(userList) {
    if (this.isSuperAdmin() || this.isPlatformAdmin()) {
      return userList
    }

    const currentTenantId = this.getCurrentTenantId()
    return userList.filter(user => user.tenantId === currentTenantId)
  }

  // 过滤角色列表（根据租户权限）
  filterRoleList(roleList) {
    if (this.isSuperAdmin()) {
      return roleList
    }

    // 租户管理员只能看到租户级别的角色
    return roleList.filter(role => 
      role.roleType === 'tenant' || role.roleType === 'user'
    )
  }

  // 检查是否可以分配指定角色
  canAssignRole(role) {
    // 超级管理员可以分配所有角色
    if (this.isSuperAdmin()) {
      return true
    }

    // 租户管理员不能分配平台级别的角色
    if (this.isTenantAdmin()) {
      return role.roleType !== 'platform'
    }

    // 普通用户不能分配角色
    return false
  }

  // 获取用户可见的权限列表
  getVisiblePermissions(permissionList) {
    if (this.isSuperAdmin()) {
      return permissionList
    }

    // 租户管理员只能看到租户相关的权限
    return permissionList.filter(permission => 
      !permission.permCode.startsWith('platform:')
    )
  }

  // 检查是否可以访问系统管理功能
  canAccessSystemManagement() {
    return this.isSuperAdmin() || this.isPlatformAdmin()
  }

  // 检查是否可以访问租户管理功能
  canAccessTenantManagement() {
    return this.isSuperAdmin() || this.isPlatformAdmin() || this.isTenantAdmin()
  }

  // 获取数据查询的租户过滤条件
  getTenantFilter() {
    if (this.isSuperAdmin()) {
      return {} // 不过滤
    }

    return {
      tenantId: this.getCurrentTenantId()
    }
  }
}

// 创建全局实例（延迟初始化）
let _tenantController = null
export const tenantController = {
  get instance() {
    if (!_tenantController) {
      _tenantController = new TenantPermissionController()
    }
    return _tenantController
  }
}

// 租户权限装饰器
export function withTenantPermission(target, propertyKey, descriptor) {
  const originalMethod = descriptor.value

  descriptor.value = function (...args) {
    const controller = new TenantPermissionController()
    
    // 在这里可以添加租户权限检查逻辑
    // 例如：检查当前用户是否有权限执行该操作
    
    return originalMethod.apply(this, args)
  }

  return descriptor
}

// Vue 组合式函数
export function useTenantPermission() {
  const controller = new TenantPermissionController()

  return {
    // 基本权限检查
    isSuperAdmin: () => controller.isSuperAdmin(),
    isTenantAdmin: () => controller.isTenantAdmin(),
    getCurrentTenantId: () => controller.getCurrentTenantId(),

    // 用户管理权限
    canManageUser: (user) => controller.canManageUser(user),
    canViewUser: (user) => controller.canViewUser(user),
    filterUserList: (userList) => controller.filterUserList(userList),

    // 角色管理权限
    canAssignRole: (role) => controller.canAssignRole(role),
    filterRoleList: (roleList) => controller.filterRoleList(roleList),

    // 权限管理
    getVisiblePermissions: (permissionList) => controller.getVisiblePermissions(permissionList),

    // 功能访问权限
    canAccessSystemManagement: () => controller.canAccessSystemManagement(),
    canAccessTenantManagement: () => controller.canAccessTenantManagement(),

    // 数据过滤
    getTenantFilter: () => controller.getTenantFilter()
  }
}
