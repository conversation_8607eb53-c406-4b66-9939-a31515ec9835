import dayjs from 'dayjs'

// 日期时间工具
export const dateUtils = {
  // 格式化日期时间
  formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!date) return ''
    return dayjs(date).format(format)
  },

  // 格式化日期
  formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) return ''
    return dayjs(date).format(format)
  },

  // 格式化时间
  formatTime(date, format = 'HH:mm:ss') {
    if (!date) return ''
    return dayjs(date).format(format)
  },

  // 相对时间
  fromNow(date) {
    if (!date) return ''
    return dayjs(date).fromNow()
  },

  // 计算时间差（秒）
  diffInSeconds(startDate, endDate = new Date()) {
    return dayjs(endDate).diff(dayjs(startDate), 'second')
  },

  // 格式化持续时间
  formatDuration(seconds) {
    if (!seconds || seconds < 0) return '0秒'
    
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60
    
    const parts = []
    if (hours > 0) parts.push(`${hours}小时`)
    if (minutes > 0) parts.push(`${minutes}分钟`)
    if (remainingSeconds > 0 || parts.length === 0) parts.push(`${remainingSeconds}秒`)
    
    return parts.join('')
  }
}

// 数据处理工具
export const dataUtils = {
  // 深拷贝
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj
    if (obj instanceof Date) return new Date(obj.getTime())
    if (obj instanceof Array) return obj.map(item => dataUtils.deepClone(item))
    if (typeof obj === 'object') {
      const clonedObj = {}
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = dataUtils.deepClone(obj[key])
        }
      }
      return clonedObj
    }
  },

  // 防抖
  debounce(func, wait) {
    let timeout
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout)
        func(...args)
      }
      clearTimeout(timeout)
      timeout = setTimeout(later, wait)
    }
  },

  // 节流
  throttle(func, limit) {
    let inThrottle
    return function executedFunction(...args) {
      if (!inThrottle) {
        func.apply(this, args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  },

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  },

  // 数组转树形结构
  arrayToTree(array, options = {}) {
    const {
      idKey = 'id',
      parentIdKey = 'parentId',
      childrenKey = 'children',
      rootValue = null
    } = options

    const tree = []
    const map = {}

    // 创建映射
    array.forEach(item => {
      map[item[idKey]] = { ...item, [childrenKey]: [] }
    })

    // 构建树形结构
    array.forEach(item => {
      const node = map[item[idKey]]
      const parentId = item[parentIdKey]

      if (parentId === rootValue || parentId === undefined || parentId === null) {
        tree.push(node)
      } else if (map[parentId]) {
        map[parentId][childrenKey].push(node)
      }
    })

    return tree
  },

  // 树形结构转数组
  treeToArray(tree, options = {}) {
    const { childrenKey = 'children' } = options
    const result = []

    const traverse = (nodes) => {
      nodes.forEach(node => {
        const { [childrenKey]: children, ...rest } = node
        result.push(rest)
        if (children && children.length > 0) {
          traverse(children)
        }
      })
    }

    traverse(tree)
    return result
  }
}

// 文件处理工具
export const fileUtils = {
  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // 获取文件扩展名
  getFileExtension(filename) {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2)
  },

  // 下载文件
  downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }
}

// 验证工具
export const validateUtils = {
  // 邮箱验证
  isEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return re.test(email)
  },

  // 手机号验证
  isPhone(phone) {
    const re = /^1[3-9]\d{9}$/
    return re.test(phone)
  },

  // URL验证
  isUrl(url) {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  // 身份证验证
  isIdCard(idCard) {
    const re = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
    return re.test(idCard)
  }
}
