// Element Plus 图标映射
// 用于解决图标名称不存在的问题

export const ICON_MAP = {
  // 常用图标映射
  'Dashboard': 'Odometer',        // 首页
  'Home': 'House',               // 首页
  'Menu': 'List',                // 菜单
  'Folder': 'FolderOpened',      // 文件夹
  'Globe': 'Connection',         // 全球/网络
  'Chart': 'TrendCharts',        // 图表
  'Report': 'Document',          // 报告
  'Analytics': 'DataAnalysis',   // 分析
  'Monitor': 'Monitor',          // 监控
  'Tools': 'Tools',              // 工具
  'Config': 'Setting',           // 配置
  'System': 'Setting',           // 系统
  'Management': 'Operation',     // 管理
  
  // 用户相关
  'User': 'User',
  'Users': 'User',
  'UserFilled': 'UserFilled',
  'Avatar': 'Avatar',
  'Profile': 'User',
  
  // 权限相关
  'Key': 'Key',
  'Lock': 'Lock',
  'Unlock': 'Unlock',
  'Shield': 'Shield',
  'Permission': 'Key',
  'Role': 'Avatar',
  
  // 操作相关
  'Plus': 'Plus',
  'Edit': 'Edit',
  'Delete': 'Delete',
  'Search': 'Search',
  'Refresh': 'Refresh',
  'Save': 'Select',
  'Cancel': 'Close',
  'Confirm': 'Check',
  'Back': 'Back',
  'Forward': 'Right',
  'Up': 'Top',
  'Down': 'Bottom',
  'Left': 'Back',
  'Right': 'Right',
  
  // 状态相关
  'Success': 'SuccessFilled',
  'Warning': 'WarningFilled',
  'Error': 'CircleCloseFilled',
  'Info': 'InfoFilled',
  'Question': 'QuestionFilled',
  
  // 文件相关
  'File': 'Document',
  'Folder': 'FolderOpened',
  'Upload': 'Upload',
  'Download': 'Download',
  'Export': 'Download',
  'Import': 'Upload',
  
  // 导航相关
  'Expand': 'ArrowDown',
  'Collapse': 'ArrowUp',
  'ArrowUp': 'ArrowUp',
  'ArrowDown': 'ArrowDown',
  'ArrowLeft': 'ArrowLeft',
  'ArrowRight': 'ArrowRight',
  
  // 其他常用
  'Close': 'Close',
  'More': 'MoreFilled',
  'Filter': 'Filter',
  'Sort': 'Sort',
  'View': 'View',
  'Hide': 'Hide',
  'Show': 'View',
  'Copy': 'CopyDocument',
  'Cut': 'Scissors',
  'Paste': 'DocumentCopy'
}

// 获取正确的图标名称
export function getIconName(iconName) {
  if (!iconName) return null
  
  // 如果映射中存在，返回映射的图标名称
  if (ICON_MAP[iconName]) {
    return ICON_MAP[iconName]
  }
  
  // 否则返回原始名称
  return iconName
}

// 检查图标是否存在
export function isValidIcon(iconName) {
  if (!iconName) return false
  
  // 检查是否在映射中或者是常用的 Element Plus 图标
  const validIcons = [
    ...Object.values(ICON_MAP),
    'House', 'User', 'UserFilled', 'Avatar', 'Key', 'Lock', 'Setting',
    'Plus', 'Edit', 'Delete', 'Search', 'Refresh', 'Close', 'Check',
    'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'More', 'View',
    'Document', 'FolderOpened', 'Upload', 'Download', 'Filter', 'Sort',
    'SuccessFilled', 'WarningFilled', 'CircleCloseFilled', 'InfoFilled',
    'Odometer', 'TrendCharts', 'DataAnalysis', 'Monitor', 'Tools',
    'Operation', 'Shield', 'Connection', 'List'
  ]
  
  return validIcons.includes(iconName) || validIcons.includes(ICON_MAP[iconName])
}

// 获取默认图标（当指定图标不存在时使用）
export function getDefaultIcon(resourceType = 'menu') {
  const defaultIcons = {
    menu: 'List',
    button: 'Operation',
    api: 'Connection',
    page: 'Document',
    component: 'Setting'
  }
  
  return defaultIcons[resourceType] || 'List'
}
