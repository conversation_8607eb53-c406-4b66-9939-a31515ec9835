// 性能监控工具

// 性能指标收集器
export class PerformanceMonitor {
  constructor() {
    this.metrics = new Map()
    this.observers = []
    this.isEnabled = process.env.NODE_ENV === 'development'
    
    if (this.isEnabled) {
      this.init()
    }
  }

  // 初始化性能监控
  init() {
    // 监听页面加载性能
    this.observePageLoad()
    
    // 监听长任务
    this.observeLongTasks()
    
    // 监听资源加载
    this.observeResources()
    
    // 监听用户交互
    this.observeUserInteraction()
  }

  // 监听页面加载性能
  observePageLoad() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.recordMetric('page_load', {
              dns: entry.domainLookupEnd - entry.domainLookupStart,
              tcp: entry.connectEnd - entry.connectStart,
              request: entry.responseStart - entry.requestStart,
              response: entry.responseEnd - entry.responseStart,
              dom: entry.domContentLoadedEventEnd - entry.responseEnd,
              load: entry.loadEventEnd - entry.loadEventStart,
              total: entry.loadEventEnd - entry.navigationStart
            })
          }
        }
      })
      
      observer.observe({ entryTypes: ['navigation'] })
      this.observers.push(observer)
    }
  }

  // 监听长任务
  observeLongTasks() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordMetric('long_task', {
            duration: entry.duration,
            startTime: entry.startTime,
            name: entry.name
          })
        }
      })
      
      try {
        observer.observe({ entryTypes: ['longtask'] })
        this.observers.push(observer)
      } catch (e) {
        // longtask 可能不被支持
        console.warn('Long task monitoring not supported')
      }
    }
  }

  // 监听资源加载
  observeResources() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'resource') {
            this.recordMetric('resource_load', {
              name: entry.name,
              duration: entry.duration,
              size: entry.transferSize,
              type: this.getResourceType(entry.name)
            })
          }
        }
      })
      
      observer.observe({ entryTypes: ['resource'] })
      this.observers.push(observer)
    }
  }

  // 监听用户交互
  observeUserInteraction() {
    const events = ['click', 'keydown', 'scroll']
    
    events.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        this.recordMetric('user_interaction', {
          type: eventType,
          timestamp: Date.now(),
          target: event.target.tagName
        })
      }, { passive: true })
    })
  }

  // 记录性能指标
  recordMetric(name, data) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    
    this.metrics.get(name).push({
      ...data,
      timestamp: Date.now()
    })
    
    // 限制存储数量
    const maxEntries = 100
    const entries = this.metrics.get(name)
    if (entries.length > maxEntries) {
      entries.splice(0, entries.length - maxEntries)
    }
  }

  // 获取资源类型
  getResourceType(url) {
    const extension = url.split('.').pop().toLowerCase()
    
    if (['js', 'mjs'].includes(extension)) return 'script'
    if (['css'].includes(extension)) return 'stylesheet'
    if (['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg'].includes(extension)) return 'image'
    if (['woff', 'woff2', 'ttf', 'otf'].includes(extension)) return 'font'
    
    return 'other'
  }

  // 获取性能指标
  getMetrics(name) {
    return this.metrics.get(name) || []
  }

  // 获取所有指标
  getAllMetrics() {
    const result = {}
    for (const [name, data] of this.metrics) {
      result[name] = data
    }
    return result
  }

  // 清空指标
  clearMetrics() {
    this.metrics.clear()
  }

  // 生成性能报告
  generateReport() {
    const metrics = this.getAllMetrics()
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      metrics: {}
    }

    // 页面加载性能
    if (metrics.page_load && metrics.page_load.length > 0) {
      const pageLoad = metrics.page_load[0]
      report.metrics.pageLoad = {
        dns: pageLoad.dns,
        tcp: pageLoad.tcp,
        request: pageLoad.request,
        response: pageLoad.response,
        dom: pageLoad.dom,
        load: pageLoad.load,
        total: pageLoad.total
      }
    }

    // 长任务统计
    if (metrics.long_task) {
      report.metrics.longTasks = {
        count: metrics.long_task.length,
        totalDuration: metrics.long_task.reduce((sum, task) => sum + task.duration, 0),
        averageDuration: metrics.long_task.length > 0 
          ? metrics.long_task.reduce((sum, task) => sum + task.duration, 0) / metrics.long_task.length 
          : 0
      }
    }

    // 资源加载统计
    if (metrics.resource_load) {
      const resources = metrics.resource_load
      const resourceTypes = {}
      
      resources.forEach(resource => {
        if (!resourceTypes[resource.type]) {
          resourceTypes[resource.type] = { count: 0, totalSize: 0, totalDuration: 0 }
        }
        resourceTypes[resource.type].count++
        resourceTypes[resource.type].totalSize += resource.size || 0
        resourceTypes[resource.type].totalDuration += resource.duration || 0
      })
      
      report.metrics.resources = {
        total: resources.length,
        types: resourceTypes
      }
    }

    // 用户交互统计
    if (metrics.user_interaction) {
      const interactions = metrics.user_interaction
      const interactionTypes = {}
      
      interactions.forEach(interaction => {
        if (!interactionTypes[interaction.type]) {
          interactionTypes[interaction.type] = 0
        }
        interactionTypes[interaction.type]++
      })
      
      report.metrics.userInteractions = {
        total: interactions.length,
        types: interactionTypes
      }
    }

    return report
  }

  // 导出性能报告
  exportReport() {
    const report = this.generateReport()
    const reportData = JSON.stringify(report, null, 2)
    const blob = new Blob([reportData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `performance-report-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  }

  // 销毁监控器
  destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

// 性能计时器
export class PerformanceTimer {
  constructor() {
    this.timers = new Map()
  }

  // 开始计时
  start(name) {
    this.timers.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    })
  }

  // 结束计时
  end(name) {
    const timer = this.timers.get(name)
    if (timer) {
      timer.endTime = performance.now()
      timer.duration = timer.endTime - timer.startTime
      return timer.duration
    }
    return null
  }

  // 获取计时结果
  getTime(name) {
    const timer = this.timers.get(name)
    return timer ? timer.duration : null
  }

  // 清除计时器
  clear(name) {
    if (name) {
      this.timers.delete(name)
    } else {
      this.timers.clear()
    }
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor()
export const performanceTimer = new PerformanceTimer()

// 性能装饰器
export function measurePerformance(name) {
  return function(target, propertyKey, descriptor) {
    const originalMethod = descriptor.value
    
    descriptor.value = async function(...args) {
      performanceTimer.start(`${name || propertyKey}`)
      
      try {
        const result = await originalMethod.apply(this, args)
        const duration = performanceTimer.end(`${name || propertyKey}`)
        
        if (process.env.NODE_ENV === 'development') {
          console.log(`⏱️ ${name || propertyKey} took ${duration.toFixed(2)}ms`)
        }
        
        return result
      } catch (error) {
        performanceTimer.end(`${name || propertyKey}`)
        throw error
      }
    }
    
    return descriptor
  }
}

// Vue 性能监控插件
export const performancePlugin = {
  install(app) {
    // 提供全局方法
    app.provide('$performanceMonitor', performanceMonitor)
    app.provide('$performanceTimer', performanceTimer)
    
    // 组件性能监控
    app.mixin({
      beforeCreate() {
        if (this.$options.name) {
          performanceTimer.start(`component:${this.$options.name}:create`)
        }
      },
      created() {
        if (this.$options.name) {
          const duration = performanceTimer.end(`component:${this.$options.name}:create`)
          if (duration && process.env.NODE_ENV === 'development') {
            console.log(`🔧 Component ${this.$options.name} created in ${duration.toFixed(2)}ms`)
          }
        }
      }
    })
  }
}
