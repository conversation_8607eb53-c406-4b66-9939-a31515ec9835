import Cookies from 'js-cookie'

const TOKEN_KEY = 'token'

// Token 相关操作
export const tokenUtils = {
  // 获取 token
  getToken() {
    return Cookies.get(TOKEN_KEY)
  },

  // 设置 token
  setToken(token, expires = 7) {
    return Cookies.set(TOKEN_KEY, token, { expires })
  },

  // 删除 token
  removeToken() {
    return Cookies.remove(TOKEN_KEY)
  }
}

// 权限检查工具
export const permissionUtils = {
  // 检查是否有指定权限
  hasPermission(permission, userPermissions = []) {
    if (!permission) return true
    return userPermissions.includes(permission)
  },

  // 检查是否有任一权限
  hasAnyPermission(permissions = [], userPermissions = []) {
    if (!permissions.length) return true
    return permissions.some(permission => userPermissions.includes(permission))
  },

  // 检查是否有所有权限
  hasAllPermissions(permissions = [], userPermissions = []) {
    if (!permissions.length) return true
    return permissions.every(permission => userPermissions.includes(permission))
  },

  // 检查是否有指定角色
  hasRole(role, userRoles = []) {
    if (!role) return true
    return userRoles.includes(role)
  },

  // 检查是否有任一角色
  hasAnyRole(roles = [], userRoles = []) {
    if (!roles.length) return true
    return roles.some(role => userRoles.includes(role))
  },

  // 检查是否有所有角色
  hasAllRoles(roles = [], userRoles = []) {
    if (!roles.length) return true
    return roles.every(role => userRoles.includes(role))
  }
}

// 用户信息工具
export const userUtils = {
  // 格式化用户显示名称
  formatUserDisplayName(user) {
    if (!user) return ''
    return user.fullName || user.username || ''
  },

  // 获取用户头像
  getUserAvatar(user) {
    if (!user) return ''
    return user.avatar || ''
  },

  // 检查用户状态
  isUserActive(user) {
    return user && user.isActivated === 1 && user.isLocked !== 1
  },

  // 格式化用户状态文本
  formatUserStatus(user) {
    if (!user) return '未知'
    if (user.isLocked === 1) return '已锁定'
    if (user.isActivated === 0) return '未激活'
    return '正常'
  }
}
