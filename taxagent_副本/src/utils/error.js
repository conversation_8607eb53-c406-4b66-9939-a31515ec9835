// 错误处理工具

import { ElMessage, ElNotification } from 'element-plus'
import { HTTP_STATUS, BUSINESS_CODE } from '@/constants'

// 错误类型枚举
export const ERROR_TYPES = {
  NETWORK: 'NETWORK',
  BUSINESS: 'BUSINESS',
  VALIDATION: 'VALIDATION',
  PERMISSION: 'PERMISSION',
  SYSTEM: 'SYSTEM'
}

// 自定义错误类
export class AppError extends Error {
  constructor(message, type = ERROR_TYPES.SYSTEM, code = null, data = null) {
    super(message)
    this.name = 'AppError'
    this.type = type
    this.code = code
    this.data = data
    this.timestamp = new Date().toISOString()
  }
}

// 网络错误类
export class NetworkError extends AppError {
  constructor(message, status = null, response = null) {
    super(message, ERROR_TYPES.NETWORK, status)
    this.response = response
  }
}

// 业务错误类
export class BusinessError extends AppError {
  constructor(message, code = BUSINESS_CODE.FAIL, data = null) {
    super(message, ERROR_TYPES.BUSINESS, code, data)
  }
}

// 验证错误类
export class ValidationError extends AppError {
  constructor(message, field = null, value = null) {
    super(message, ERROR_TYPES.VALIDATION)
    this.field = field
    this.value = value
  }
}

// 权限错误类
export class PermissionError extends AppError {
  constructor(message, permission = null) {
    super(message, ERROR_TYPES.PERMISSION)
    this.permission = permission
  }
}

// 错误处理器类
export class ErrorHandler {
  constructor() {
    this.errorLog = []
    this.maxLogSize = 100
  }

  // 处理错误
  handle(error, context = {}) {
    // 记录错误日志
    this.log(error, context)

    // 根据错误类型进行不同处理
    switch (error.type) {
      case ERROR_TYPES.NETWORK:
        this.handleNetworkError(error)
        break
      case ERROR_TYPES.BUSINESS:
        this.handleBusinessError(error)
        break
      case ERROR_TYPES.VALIDATION:
        this.handleValidationError(error)
        break
      case ERROR_TYPES.PERMISSION:
        this.handlePermissionError(error)
        break
      default:
        this.handleSystemError(error)
    }
  }

  // 处理网络错误
  handleNetworkError(error) {
    const { code, message } = error
    
    switch (code) {
      case HTTP_STATUS.UNAUTHORIZED:
        ElMessage.error('登录已过期，请重新登录')
        // 可以在这里触发登出逻辑
        break
      case HTTP_STATUS.FORBIDDEN:
        ElMessage.error('权限不足，无法访问')
        break
      case HTTP_STATUS.NOT_FOUND:
        ElMessage.error('请求的资源不存在')
        break
      case HTTP_STATUS.INTERNAL_SERVER_ERROR:
        ElMessage.error('服务器内部错误，请稍后重试')
        break
      default:
        ElMessage.error(message || '网络请求失败')
    }
  }

  // 处理业务错误
  handleBusinessError(error) {
    const { message, code } = error
    
    // 根据业务错误码进行不同处理
    switch (code) {
      case BUSINESS_CODE.VALIDATION_ERROR:
        ElMessage.warning(message || '数据验证失败')
        break
      default:
        ElMessage.error(message || '操作失败')
    }
  }

  // 处理验证错误
  handleValidationError(error) {
    const { message, field } = error
    ElMessage.warning(`${field ? `${field}: ` : ''}${message}`)
  }

  // 处理权限错误
  handlePermissionError(error) {
    const { message } = error
    ElMessage.error(message || '权限不足')
  }

  // 处理系统错误
  handleSystemError(error) {
    console.error('System Error:', error)
    ElMessage.error('系统错误，请联系管理员')
    
    // 在开发环境下显示详细错误信息
    if (process.env.NODE_ENV === 'development') {
      ElNotification.error({
        title: '系统错误',
        message: error.message,
        duration: 0
      })
    }
  }

  // 记录错误日志
  log(error, context = {}) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        type: error.type,
        code: error.code,
        stack: error.stack
      },
      context,
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    this.errorLog.unshift(logEntry)

    // 限制日志大小
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize)
    }

    // 在开发环境下打印到控制台
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Log')
      console.error('Error:', error)
      console.log('Context:', context)
      console.groupEnd()
    }
  }

  // 获取错误日志
  getErrorLog() {
    return [...this.errorLog]
  }

  // 清空错误日志
  clearErrorLog() {
    this.errorLog = []
  }

  // 导出错误日志
  exportErrorLog() {
    const logData = JSON.stringify(this.errorLog, null, 2)
    const blob = new Blob([logData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `error-log-${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 全局错误处理函数
export function handleError(error, context = {}) {
  errorHandler.handle(error, context)
}

// Promise 错误处理装饰器
export function withErrorHandling(fn, context = {}) {
  return async (...args) => {
    try {
      return await fn(...args)
    } catch (error) {
      handleError(error, { ...context, args })
      throw error
    }
  }
}

// Vue 错误处理插件
export const errorPlugin = {
  install(app) {
    // 全局错误处理
    app.config.errorHandler = (error, instance, info) => {
      handleError(error, {
        component: instance?.$options.name || 'Unknown',
        info
      })
    }

    // 全局警告处理
    app.config.warnHandler = (msg, instance, trace) => {
      console.warn('Vue Warning:', msg, trace)
    }

    // 提供全局方法
    app.provide('$handleError', handleError)
    app.provide('$errorHandler', errorHandler)
  }
}

// 异步组件错误处理
export function defineAsyncComponent(loader) {
  return {
    loader,
    errorComponent: {
      template: `
        <div class="error-component">
          <el-alert
            title="组件加载失败"
            type="error"
            description="请刷新页面重试"
            show-icon
          />
        </div>
      `
    },
    delay: 200,
    timeout: 3000
  }
}
