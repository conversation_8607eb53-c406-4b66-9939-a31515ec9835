<template>
  <div class="page-container">
    
    <div class="page-content">
      <!-- 工具栏 -->
      <el-card class="toolbar-card">
        <div class="toolbar">
          <div class="toolbar-left">
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              {{ t('resource.createResource', '新增资源') }}
            </el-button>

          </div>
          
          <div class="toolbar-right">
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              {{ t('actions.refresh', '刷新') }}
            </el-button>
          </div>
        </div>
      </el-card>
      
      <!-- 资源树表格 -->
      <el-card class="table-card">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="flatTableData"
          row-key="resourceId"
          stripe
          border
        >
          <el-table-column prop="resourceName" :label="t('resource.resourceName', '资源名称')" min-width="280">
            <template #default="{ row }">
              <div class="resource-name" :style="{ paddingLeft: getIndentLevel(row) * 24 + 'px' }">
                <!-- 树形连接线 -->
                <div class="tree-lines" v-if="getIndentLevel(row) > 0">
                  <div
                    v-for="level in getIndentLevel(row)"
                    :key="level"
                    class="tree-line"
                    :class="{ 'tree-line-last': level === getIndentLevel(row) }"
                  ></div>
                </div>

                <!-- 树形节点图标 -->
                <div class="tree-node" v-if="getIndentLevel(row) > 0">
                  <div class="tree-connector"></div>
                </div>

                <!-- 资源图标 -->
                <el-icon v-if="getDisplayIcon(row)" class="resource-icon" :class="{ 'button-icon': row._isButton }">
                  <component :is="getDisplayIcon(row)" />
                </el-icon>

                <!-- 资源名称 -->
                <span class="resource-text" :class="{ 'button-text': row._isButton }">
                  {{ row.resourceName }}
                  <el-tag v-if="row._isButton" size="small" type="info" class="resource-type-tag">按钮</el-tag>
                </span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="resourceCode" :label="t('resource.resourceCode', '资源编码')" min-width="150">
            <template #default="{ row }">
              <el-tag size="small">{{ row.resourceCode }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="resourceType" :label="t('resource.resourceType', '资源类型')" width="100">
            <template #default="{ row }">
              <el-tag
                :type="getResourceTypeColor(row.resourceType)"
                size="small"
              >
                {{ getResourceTypeText(row.resourceType) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="path" :label="t('resource.resourcePath', '路径')" min-width="150" />

          <el-table-column prop="sortOrder" :label="t('resource.sortOrder', '排序')" width="80" />

          <el-table-column prop="isVisible" :label="t('common.status', '状态')" width="80">
            <template #default="{ row }">
              <el-tag
                :type="row.isVisible ? 'success' : 'danger'"
                size="small"
              >
                {{ row.isVisible ? '是' : '否' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="createTime" :label="t('common.createTime', '创建时间')" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.createTime) }}
            </template>
          </el-table-column>

          <el-table-column :label="t('common.operation', '操作')" width="280" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEdit(row)"
                >
                  {{ t('actions.edit', '编辑') }}
                </el-button>
                <el-button
                  v-if="!row._isButton && row.resourceType !== 'button'"
                  type="success"
                  size="small"
                  @click="handleAddChild(row)"
                >
                  {{ t('actions.add', '新增子项') }}
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="handleDelete(row)"
                >
                  {{ t('actions.delete', '删除') }}
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
    
    <!-- 资源表单对话框 -->
    <ResourceForm
      v-model="formVisible"
      :form-data="formData"
      :form-type="formType"
      :parent-resource="parentResource"
      @success="handleFormSuccess"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Tools, Edit, Delete, Key, Avatar, User, Setting, Odometer, FolderOpened } from '@element-plus/icons-vue'
import { resourceApi } from '@/api'
import { dateUtils } from '@/utils/common'
import { getIconName, getDefaultIcon } from '@/utils/icons'
import ResourceForm from './components/ResourceForm.vue'

const { t } = useI18n()

// 表格引用
const tableRef = ref()

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref([])

// 扁平化的表格数据
const flatTableData = computed(() => {
  const flattened = flattenTreeData(tableData.value)

  // 调试：检查是否有重复的 resourceId
  const ids = flattened.map(item => item.resourceId)
  const duplicates = ids.filter((id, index) => ids.indexOf(id) !== index)
  if (duplicates.length > 0) {
    console.warn('发现重复的资源ID:', duplicates)
    console.log('扁平化数据:', flattened)
  }

  return flattened
})

// 表单对话框
const formVisible = ref(false)
const formType = ref('add') // add | edit | addChild
const formData = ref({})
const parentResource = ref(null)

// 格式化日期时间
const formatDateTime = (date) => {
  return dateUtils.formatDateTime(date)
}

// 获取显示图标
const getDisplayIcon = (row) => {
  if (!row.icon) {
    return getDefaultIcon(row.resourceType)
  }

  return getIconName(row.icon)
}

// 计算层级缩进
const getIndentLevel = (row) => {
  // 如果有 _level 属性（由 flattenTreeData 添加），直接使用
  if (typeof row._level === 'number') {
    return row._level
  }

  // 否则通过父级关系计算层级
  let level = 0
  let current = row

  while (current.parentId) {
    level++
    current = findResourceById(tableData.value, current.parentId)
    if (!current) break
  }

  return level
}

// 在树形数据中查找指定ID的资源
const findResourceById = (resources, id) => {
  for (const resource of resources) {
    if (resource.resourceId === id) {
      return resource
    }
    if (resource.children) {
      const found = findResourceById(resource.children, id)
      if (found) return found
    }
  }
  return null
}

// 将树形数据扁平化，并添加层级信息
const flattenTreeData = (treeData, level = 0) => {
  const result = []

  for (const item of treeData) {
    // 添加层级信息，移除 children 字段避免展开图标
    const { children, ...cleanItem } = item
    const flatItem = {
      ...cleanItem,
      _level: level,
      _hasChildren: (children && children.length > 0)
    }
    result.push(flatItem)

    // 递归处理子菜单节点
    if (children && children.length > 0) {
      const childrenFlat = flattenTreeData(children, level + 1)
      result.push(...childrenFlat)
    }

  }

  return result
}

// 获取资源类型颜色
const getResourceTypeColor = (type) => {
  const colorMap = {
    'menu': 'primary',
    'button': 'success',
    'api': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取资源类型文本
const getResourceTypeText = (type) => {
  const textMap = {
    'menu': '菜单',
    'button': '按钮',
    'api': '接口'
  }
  return textMap[type] || type
}

// 加载资源树
const loadResourceTree = async () => {
  try {
    loading.value = true
    const response = await resourceApi.getResourceTree()
    tableData.value = response.data || []

    // 如果后端没有返回数据，使用模拟数据进行测试
    if (!tableData.value.length) {
      console.warn('后端未返回数据，使用模拟数据')
      tableData.value = getMockResourceData()
    }
  } catch (error) {
    console.error('加载资源树失败:', error)
    ElMessage.error('加载资源树失败，使用模拟数据')
  } finally {
    loading.value = false
  }
}

// 刷新
const handleRefresh = () => {
  loadResourceTree()
}



// 新增资源
const handleAdd = () => {
  formType.value = 'add'
  formData.value = {}
  parentResource.value = null
  formVisible.value = true
}

// 编辑资源
const handleEdit = (row) => {
  formType.value = 'edit'
  formData.value = { ...row }
  parentResource.value = null
  formVisible.value = true
}

// 新增子资源
const handleAddChild = (row) => {
  formType.value = 'addChild'
  formData.value = {}
  parentResource.value = row
  formVisible.value = true
}

// 删除资源
const handleDelete = async (row) => {
  // 检查是否有子资源
  if (row.children && row.children.length > 0) {
    ElMessage.warning('该资源下还有子资源，请先删除子资源')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除资源 "${row.resourceName}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await resourceApi.deleteResource(row.resourceId)
    ElMessage.success('删除成功')
    loadResourceTree()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除资源失败:', error)
      ElMessage.error('删除资源失败')
    }
  }
}

// 表单提交成功
const handleFormSuccess = () => {
  formVisible.value = false
  loadResourceTree()
}

// 组件挂载时加载数据
onMounted(() => {
  loadResourceTree()
})
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--el-text-color-primary);
    margin: 0;
  }
}

.toolbar-card {
  margin-bottom: 20px;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .toolbar-left {
      display: flex;
      gap: 12px;
    }
  }
}

.table-card {
  .resource-name {
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
    position: relative;

    .tree-lines {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      display: flex;

      .tree-line {
        width: 24px;
        height: 100%;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 11px;
          top: 0;
          bottom: 0;
          width: 1px;
          background-color: var(--el-border-color-light);
        }

        &.tree-line-last::before {
          bottom: 50%;
        }

        &.tree-line-last::after {
          content: '';
          position: absolute;
          left: 11px;
          top: 50%;
          width: 12px;
          height: 1px;
          background-color: var(--el-border-color-light);
        }
      }
    }

    .tree-node {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-right: 4px;

      .tree-connector {
        width: 6px;
        height: 6px;
        border: 1px solid var(--el-border-color);
        border-radius: 50%;
        background-color: var(--el-bg-color);
      }
    }

    .resource-icon {
      color: var(--el-color-primary);
      font-size: 16px;
      flex-shrink: 0;

      &.button-icon {
        color: var(--el-color-success);
        font-size: 14px;
      }
    }

    .resource-text {
      font-weight: 500;
      color: var(--el-text-color-primary);
      display: flex;
      align-items: center;
      gap: 8px;

      &.button-text {
        color: var(--el-text-color-regular);
        font-weight: 400;
        font-size: 14px;
      }

      .resource-type-tag {
        font-size: 10px;
        height: 16px;
        line-height: 14px;
        padding: 0 4px;
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: nowrap;
    justify-content: flex-start;

    .el-button {
      margin: 0;
      white-space: nowrap;
      min-width: auto;
      padding: 5px 12px;
    }
  }
}

// 深度选择器，修改表格样式
:deep(.el-table) {
  .el-table__row {
    &:hover {
      background-color: var(--el-fill-color-light);
    }
  }

  .el-table__cell {
    padding: 12px 0;

    .cell {
      padding: 0 12px;
      line-height: 1.5;
    }
  }

  // 树形表格的展开图标样式
  .el-table__expand-icon {
    color: var(--el-color-primary);
    margin-right: 8px;

    &:hover {
      color: var(--el-color-primary-dark-2);
    }
  }

  // 固定列样式优化
  .el-table-fixed-column--right {
    box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
