<template>
  <el-dialog
    v-model="visible"
    :title="formType === 'add' ? t('user.createUser', '新增用户') : t('user.editUser', '编辑用户')"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item :label="t('user.username', '用户名')" prop="username">
        <el-input
          v-model="form.username"
          :placeholder="t('user.usernamePlaceholder', '请输入用户名')"
          :disabled="formType === 'edit'"
        />
      </el-form-item>

      <el-form-item :label="t('user.password', '密码')" prop="password" v-if="formType === 'add'">
        <el-input
          v-model="form.password"
          type="password"
          :placeholder="t('user.passwordPlaceholder', '请输入密码')"
          show-password
        />
      </el-form-item>

      <el-form-item :label="t('user.email', '邮箱')" prop="email">
        <el-input
          v-model="form.email"
          :placeholder="t('user.emailPlaceholder', '请输入邮箱')"
        />
      </el-form-item>

      <el-form-item :label="t('user.fullName', '姓名')" prop="fullName">
        <el-input
          v-model="form.fullName"
          :placeholder="t('user.fullNamePlaceholder', '请输入姓名')"
        />
      </el-form-item>

      <el-form-item :label="t('user.phone', '电话')" prop="phone">
        <el-input
          v-model="form.phone"
          :placeholder="t('user.phonePlaceholder', '请输入电话')"
        />
      </el-form-item>

      <el-form-item :label="t('user.status', '状态')" prop="isActivated">
        <el-radio-group v-model="form.isActivated">
          <el-radio :label="1">{{ t('status.active', '正常') }}</el-radio>
          <el-radio :label="0">{{ t('status.inactive', '禁用') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ t('common.cancel', '取消') }}</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? t('common.loading', '提交中...') : t('common.confirm', '确定') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { userApi } from '@/api'
import { validateUtils } from '@/utils/common'

const { t } = useI18n()

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  formType: {
    type: String,
    default: 'add' // add | edit
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const formRef = ref()

// 对话框显示状态
const visible = ref(false)

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive({
  username: '',
  password: '',
  email: '',
  fullName: '',
  phone: '',
  isActivated: 1
})

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: t('login.usernameRequired', '请输入用户名'), trigger: 'blur' },
    { min: 3, max: 50, message: t('login.usernameLength', '用户名长度在 3 到 50 个字符'), trigger: 'blur' }
  ],
  password: [
    { required: true, message: t('login.passwordRequired', '请输入密码'), trigger: 'blur' },
    { min: 8, max: 32, message: t('login.passwordLength', '密码长度在 8 到 32 个字符'), trigger: 'blur' }
  ],
  email: [
    { required: true, message: t('validation.email', '请输入邮箱'), trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (!validateUtils.isEmail(value)) {
          callback(new Error(t('validation.email', '请输入正确的邮箱格式')))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  fullName: [
    { max: 128, message: '姓名长度不能超过 128 个字符', trigger: 'blur' }
  ],
  phone: [
    { 
      validator: (rule, value, callback) => {
        if (value && !validateUtils.isPhone(value)) {
          callback(new Error('请输入正确的手机号格式'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
    if (props.formType === 'edit' && props.formData) {
      Object.assign(form, props.formData)
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    username: '',
    password: '',
    email: '',
    fullName: '',
    phone: '',
    isActivated: 1
  })
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    if (props.formType === 'add') {
      await userApi.createUser(form)
      ElMessage.success(t('message.createSuccess', '用户创建成功'))
    } else {
      await userApi.updateUser(props.formData.userId, form)
      ElMessage.success(t('message.updateSuccess', '用户更新成功'))
    }
    
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
