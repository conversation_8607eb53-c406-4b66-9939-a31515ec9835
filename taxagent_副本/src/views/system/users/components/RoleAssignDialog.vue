<template>
  <el-dialog
    v-model="visible"
    title="分配角色"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="user-info">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="用户名">{{ userData.username }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ userData.fullName }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ userData.email }}</el-descriptions-item>
      </el-descriptions>
    </div>
    
    <div class="role-selection">
      <h4>选择角色</h4>
      <el-checkbox-group v-model="selectedRoles" v-loading="loading">
        <el-checkbox
          v-for="role in roleList"
          :key="role.roleId"
          :label="role.roleId"
          :value="role.roleId"
        >
          {{ role.roleName }}
          <span class="role-description">{{ role.description }}</span>
        </el-checkbox>
      </el-checkbox-group>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { roleApi, userApi } from '@/api'
import { useTenantPermission } from '@/utils/tenant'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  userData: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 租户权限控制
const tenantPermission = useTenantPermission()

// 对话框显示状态
const visible = ref(false)

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 角色列表
const roleList = ref([])

// 选中的角色
const selectedRoles = ref([])

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadRoleList()
    initSelectedRoles()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 加载角色列表
const loadRoleList = async () => {
  try {
    loading.value = true
    const response = await roleApi.getAllRoles()

    // 根据租户权限过滤角色列表
    let roles = response.data || []
    roles = tenantPermission.filterRoleList(roles)

    roleList.value = roles
  } catch (error) {
    console.error('加载角色列表失败:', error)
    ElMessage.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

// 初始化选中的角色
const initSelectedRoles = () => {
  if (props.userData.roles) {
    selectedRoles.value = props.userData.roles.map(role => role.roleId)
  } else {
    selectedRoles.value = []
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交角色分配
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    await userApi.assignRoles(props.userData.userId, selectedRoles.value)
    
    ElMessage.success('角色分配成功')
    emit('success')
  } catch (error) {
    console.error('角色分配失败:', error)
    ElMessage.error('角色分配失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.user-info {
  margin-bottom: 20px;
}

.role-selection {
  h4 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }
  
  :deep(.el-checkbox-group) {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .el-checkbox {
      margin-right: 0;
      
      .el-checkbox__label {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        
        .role-description {
          font-size: 12px;
          color: var(--el-text-color-regular);
          margin-top: 2px;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
