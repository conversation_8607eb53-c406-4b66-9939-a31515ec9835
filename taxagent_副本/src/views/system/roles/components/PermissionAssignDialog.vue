<template>
  <el-dialog
    v-model="visible"
    title="分配权限"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="role-info">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="角色名称">{{ roleData.roleName }}</el-descriptions-item>
        <el-descriptions-item label="角色描述">{{ roleData.description }}</el-descriptions-item>
      </el-descriptions>
    </div>
    
    <div class="permission-selection">
      <h4>选择权限</h4>
      <div class="permission-tree" v-loading="loading">
        <el-tree
          ref="treeRef"
          :data="permissionTree"
          :props="treeProps"
          show-checkbox
          node-key="permCode"
          :default-checked-keys="selectedPermissions"
          @check="handleTreeCheck"
        >
          <template #default="{ node, data }">
            <div class="tree-node">
              <span class="node-label">{{ data.permName }}</span>
              <span class="node-code">{{ data.permCode }}</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="submitting" @click="handleSubmit">
          {{ submitting ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { permissionApi, roleApi } from '@/api'
import { dataUtils } from '@/utils/common'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  roleData: {
    type: Object,
    default: () => ({})
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 树形组件引用
const treeRef = ref()

// 对话框显示状态
const visible = ref(false)

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 权限列表和树形数据
const permissionList = ref([])
const permissionTree = ref([])

// 选中的权限
const selectedPermissions = ref([])

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'permName'
}

// 监听对话框显示状态
watch(() => props.modelValue, async (val) => {
  visible.value = val
  if (val) {
    await loadPermissionList()
    await initSelectedPermissions()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 加载权限列表
const loadPermissionList = async () => {
  try {
    loading.value = true
    const response = await permissionApi.getAllPermissions()
    permissionList.value = response.data || []

    // 构建权限树
    buildPermissionTree()

    // 等待DOM更新
    await nextTick()
  } catch (error) {
    console.error('加载权限列表失败:', error)
    ElMessage.error('加载权限列表失败')
  } finally {
    loading.value = false
  }
}

// 构建权限树形结构
const buildPermissionTree = () => {
  // 这里简化处理，实际应该根据权限的层级关系构建树形结构
  // 假设权限编码格式为：platform:user:manage, platform:role:manage 等
  const treeData = []
  const moduleMap = new Map()
  
  permissionList.value.forEach(permission => {
    const parts = permission.permCode.split(':')
    if (parts.length >= 2) {
      const moduleKey = parts[0]
      const actionKey = parts.slice(1).join(':')
      
      if (!moduleMap.has(moduleKey)) {
        const moduleNode = {
          permCode: moduleKey,
          permName: getModuleName(moduleKey),
          children: []
        }
        moduleMap.set(moduleKey, moduleNode)
        treeData.push(moduleNode)
      }
      
      moduleMap.get(moduleKey).children.push({
        permCode: permission.permCode,
        permName: permission.permName,
        description: permission.description
      })
    } else {
      // 顶级权限
      treeData.push({
        permCode: permission.permCode,
        permName: permission.permName,
        description: permission.description
      })
    }
  })
  
  permissionTree.value = treeData
}

// 获取模块名称
const getModuleName = (moduleKey) => {
  const moduleNames = {
    'platform': '平台管理',
    'tenant': '租户管理',
    'system': '系统管理'
  }
  return moduleNames[moduleKey] || moduleKey
}

// 初始化选中的权限
const initSelectedPermissions = async () => {
  if (!props.roleData || !props.roleData.roleId) {
    selectedPermissions.value = []
    return
  }

  try {
    const response = await roleApi.getRolePermissions(props.roleData.roleId)
    const rolePermissions = response.data || []

    // 提取权限编码
    const permCodes = rolePermissions.map(perm =>
      typeof perm === 'string' ? perm : perm.permCode
    )

    selectedPermissions.value = permCodes

    // 多次尝试设置选中状态，确保树形组件已完全渲染
    const setCheckedKeys = async (retries = 3) => {
      await nextTick()
      if (treeRef.value && permissionTree.value.length > 0) {
        try {
          treeRef.value.setCheckedKeys(permCodes)
          console.log('已设置角色权限选中状态:', permCodes)
        } catch (error) {
          console.warn('设置权限选中状态失败:', error)
          if (retries > 0) {
            setTimeout(() => setCheckedKeys(retries - 1), 100)
          }
        }
      } else if (retries > 0) {
        setTimeout(() => setCheckedKeys(retries - 1), 100)
      }
    }

    await setCheckedKeys()

  } catch (error) {
    console.error('获取角色权限失败:', error)
    ElMessage.error('获取角色权限失败')
    selectedPermissions.value = []
  }
}

// 树形选择变化
const handleTreeCheck = (data, checked) => {
  // 获取所有选中的权限编码
  const checkedKeys = treeRef.value.getCheckedKeys()
  const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
  selectedPermissions.value = [...checkedKeys, ...halfCheckedKeys]
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交权限分配
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 只获取叶子节点的权限编码
    const checkedKeys = treeRef.value.getCheckedKeys(true)
    
    await roleApi.assignPermissions(props.roleData.roleId, checkedKeys)
    
    ElMessage.success('权限分配成功')
    emit('success')
  } catch (error) {
    console.error('权限分配失败:', error)
    ElMessage.error('权限分配失败')
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.role-info {
  margin-bottom: 20px;
}

.permission-selection {
  h4 {
    margin: 0 0 16px 0;
    color: var(--el-text-color-primary);
  }
  
  .permission-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    padding: 8px;
    
    .tree-node {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      
      .node-label {
        flex: 1;
      }
      
      .node-code {
        font-size: 12px;
        color: var(--el-text-color-regular);
        background: var(--el-fill-color-light);
        padding: 2px 6px;
        border-radius: 2px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
