<template>
  <el-dialog
    v-model="visible"
    :title="formType === 'add' ? '新增权限' : '编辑权限'"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="formRules"
      label-width="80px"
    >
      <el-form-item label="权限名称" prop="permName">
        <el-input
          v-model="form.permName"
          placeholder="请输入权限名称"
        />
      </el-form-item>
      
      <el-form-item label="权限编码" prop="permCode">
        <el-input
          v-model="form.permCode"
          placeholder="请输入权限编码，如：platform:user:manage"
          :disabled="formType === 'edit'"
        />
        <div class="form-tip">
          权限编码格式：模块:资源:操作，如 platform:user:manage
        </div>
      </el-form-item>
      
      <el-form-item label="权限描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入权限描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          {{ loading ? '提交中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { permissionApi } from '@/api'

// 定义属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  formData: {
    type: Object,
    default: () => ({})
  },
  formType: {
    type: String,
    default: 'add' // add | edit
  }
})

// 定义事件
const emit = defineEmits(['update:modelValue', 'success'])

// 表单引用
const formRef = ref()

// 对话框显示状态
const visible = ref(false)

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive({
  permName: '',
  permCode: '',
  description: ''
})

// 表单验证规则
const formRules = {
  permName: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 50, message: '权限名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  permCode: [
    { required: true, message: '请输入权限编码', trigger: 'blur' },
    { min: 3, max: 100, message: '权限编码长度在 3 到 100 个字符', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z0-9_:]+$/, 
      message: '权限编码只能包含字母、数字、下划线和冒号', 
      trigger: 'blur' 
    },
    {
      validator: (rule, value, callback) => {
        if (value && !value.includes(':')) {
          callback(new Error('权限编码应包含冒号分隔符，格式：模块:资源:操作'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  description: [
    { max: 255, message: '权限描述长度不能超过 255 个字符', trigger: 'blur' }
  ]
}

// 监听对话框显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
    if (props.formType === 'edit' && props.formData) {
      Object.assign(form, props.formData)
    }
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    permName: '',
    permCode: '',
    description: ''
  })
  formRef.value?.clearValidate()
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    loading.value = true
    
    if (props.formType === 'add') {
      await permissionApi.createPermission(form)
      ElMessage.success('权限创建成功')
    } else {
      await permissionApi.updatePermission(props.formData.permId, form)
      ElMessage.success('权限更新成功')
    }
    
    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error.message) {
      ElMessage.error(error.message)
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.form-tip {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.dialog-footer {
  text-align: right;
}
</style>
