<template>
  <div class="error-page">
    <div class="error-content">
      <div class="error-image">
        <div class="error-number">404</div>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">404</h1>
        <p class="error-message">抱歉，您访问的页面不存在</p>
        <p class="error-description">
          请检查您输入的网址是否正确，或者点击下面的按钮返回首页
        </p>
        
        <div class="error-actions">
          <el-button type="primary" size="large" @click="goHome">
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button size="large" @click="goBack">
            <el-icon><Back /></el-icon>
            返回上页
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { House, Back } from '@element-plus/icons-vue'

const router = useRouter()

// 返回首页
const goHome = () => {
  router.push('/')
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.error-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--el-bg-color-page);
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
}

.error-image {
  margin-bottom: 40px;

  .error-number {
    font-size: 120px;
    font-weight: 900;
    color: var(--el-color-primary);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin: 0;
    line-height: 1;
  }
}

.error-info {
  .error-title {
    font-size: 72px;
    font-weight: 700;
    color: var(--el-color-primary);
    margin: 0 0 16px 0;
    line-height: 1;
  }
  
  .error-message {
    font-size: 24px;
    color: var(--el-text-color-primary);
    margin: 0 0 16px 0;
    font-weight: 500;
  }
  
  .error-description {
    font-size: 16px;
    color: var(--el-text-color-regular);
    margin: 0 0 40px 0;
    line-height: 1.6;
  }
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
  
  .el-button {
    .el-icon {
      margin-right: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-info {
    .error-title {
      font-size: 48px;
    }
    
    .error-message {
      font-size: 20px;
    }
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 200px;
    }
  }
}
</style>
