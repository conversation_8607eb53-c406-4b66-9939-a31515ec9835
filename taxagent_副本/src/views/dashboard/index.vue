<template>
  <div class="page-container">
    <!-- <div class="page-header">
      <h1 class="page-title">{{ t('dashboard.title', '首页') }}</h1>
    </div> -->
    
    <div class="page-content">
      <!-- 租户信息 -->
      <TenantInfo />

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon user-icon">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.totalUsers }}</div>
                <div class="stats-label">{{ t('dashboard.totalUsers', '总用户数') }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon online-icon">
                <el-icon><UserFilled /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.onlineUsers }}</div>
                <div class="stats-label">{{ t('dashboard.onlineUsers', '在线用户') }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon role-icon">
                <el-icon><Avatar /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.totalRoles }}</div>
                <div class="stats-label">{{ t('dashboard.totalRoles', '角色数量') }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon permission-icon">
                <el-icon><Key /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.totalPermissions }}</div>
                <div class="stats-label">{{ t('dashboard.totalPermissions', '权限数量') }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 快捷操作 -->
      <el-card class="quick-actions-card">
        <template #header>
          <span>{{ t('dashboard.quickActions', '快捷操作') }}</span>
        </template>
        
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-button 
              type="primary" 
              size="large" 
              class="quick-action-btn"
              @click="$router.push('/system/users')"
            >
              <el-icon><User /></el-icon>
              {{ t('menu.users', '用户管理') }}
            </el-button>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-button 
              type="success" 
              size="large" 
              class="quick-action-btn"
              @click="$router.push('/system/roles')"
            >
              <el-icon><Avatar /></el-icon>
              {{ t('menu.roles', '角色管理') }}
            </el-button>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-button 
              type="warning" 
              size="large" 
              class="quick-action-btn"
              @click="$router.push('/system/permissions')"
            >
              <el-icon><Key /></el-icon>
              {{ t('menu.permissions', '权限管理') }}
            </el-button>
          </el-col>
          
          <el-col :xs="24" :sm="12" :md="8" :lg="6">
            <el-button 
              type="info" 
              size="large" 
              class="quick-action-btn"
              @click="$router.push('/system/online-users')"
            >
              <el-icon><UserFilled /></el-icon>
              {{ t('menu.onlineUsers', '在线用户') }}
            </el-button>
          </el-col>
        </el-row>
      </el-card>
      
      <!-- 系统信息 -->
      <el-card class="system-info-card">
        <template #header>
          <span>{{ t('dashboard.systemInfo', '系统信息') }}</span>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item :label="t('dashboard.systemName', '系统名称')">{{ t('system.title', 'TaxAgent 管理系统') }}</el-descriptions-item>
          <el-descriptions-item :label="t('dashboard.version', '系统版本')">v1.0.0</el-descriptions-item>
          <el-descriptions-item :label="t('dashboard.currentUser', '当前用户')">{{ userInfo?.username }}</el-descriptions-item>
          <el-descriptions-item :label="t('dashboard.userRoles', '用户角色')">{{ userRoles }}</el-descriptions-item>
          <el-descriptions-item :label="t('dashboard.loginTime', '登录时间')">{{ loginTime }}</el-descriptions-item>
          <el-descriptions-item :label="t('dashboard.serverTime', '服务器时间')">{{ currentTime }}</el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { User, UserFilled, Avatar, Key } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { dateUtils } from '@/utils/common'
import TenantInfo from '@/components/TenantInfo.vue'

const authStore = useAuthStore()
const { t } = useI18n()

// 统计数据
const stats = ref({
  totalUsers: 0,
  onlineUsers: 0,
  totalRoles: 0,
  totalPermissions: 0
})

// 当前时间
const currentTime = ref('')

// 用户信息
const userInfo = computed(() => authStore.userInfo)

// 用户角色
const userRoles = computed(() => {
  return authStore.roles.join(', ') || '无'
})

// 登录时间
const loginTime = computed(() => {
  // 这里应该从用户信息中获取登录时间
  return dateUtils.formatDateTime(new Date())
})

// 定时器
let timer = null

// 更新当前时间
const updateCurrentTime = () => {
  currentTime.value = dateUtils.formatDateTime(new Date())
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用实际的API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      totalUsers: 156,
      onlineUsers: 23,
      totalRoles: 8,
      totalPermissions: 45
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

onMounted(() => {
  loadStats()
  updateCurrentTime()
  
  // 每秒更新时间
  timer = setInterval(updateCurrentTime, 1000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style lang="scss" scoped>
.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  .stats-content {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .stats-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      
      &.user-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      &.online-icon {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }
      
      &.role-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
      
      &.permission-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }
    
    .stats-info {
      flex: 1;
      
      .stats-number {
        font-size: 28px;
        font-weight: 600;
        color: var(--el-text-color-primary);
        line-height: 1;
        margin-bottom: 4px;
      }
      
      .stats-label {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }
    }
  }
}

.quick-actions-card,
.system-info-card {
  margin-top: 20px;
}

.quick-action-btn {
  width: 100%;
  height: 60px;
  margin-bottom: 16px;
  
  .el-icon {
    margin-right: 8px;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    font-weight: 500;
  }
}
</style>
