// CSS 变量定义
:root {
  // 主色调
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;
  
  // 布局尺寸
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 圆角
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  // 阴影
  --box-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
  --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
  --box-shadow-dark: 0 4px 16px rgba(0, 0, 0, 0.2);
  
  // 过渡动画
  --transition-base: all 0.3s ease;
  --transition-fast: all 0.2s ease;
}
