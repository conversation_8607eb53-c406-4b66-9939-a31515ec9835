// 通用样式类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

// 文本对齐
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 文本省略
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 间距工具类
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.m-1 { margin: var(--spacing-xs); }
.mt-1 { margin-top: var(--spacing-xs); }
.mr-1 { margin-right: var(--spacing-xs); }
.mb-1 { margin-bottom: var(--spacing-xs); }
.ml-1 { margin-left: var(--spacing-xs); }

.m-2 { margin: var(--spacing-sm); }
.mt-2 { margin-top: var(--spacing-sm); }
.mr-2 { margin-right: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.ml-2 { margin-left: var(--spacing-sm); }

.m-3 { margin: var(--spacing-md); }
.mt-3 { margin-top: var(--spacing-md); }
.mr-3 { margin-right: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-md); }
.ml-3 { margin-left: var(--spacing-md); }

.m-4 { margin: var(--spacing-lg); }
.mt-4 { margin-top: var(--spacing-lg); }
.mr-4 { margin-right: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.ml-4 { margin-left: var(--spacing-lg); }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

.p-1 { padding: var(--spacing-xs); }
.pt-1 { padding-top: var(--spacing-xs); }
.pr-1 { padding-right: var(--spacing-xs); }
.pb-1 { padding-bottom: var(--spacing-xs); }
.pl-1 { padding-left: var(--spacing-xs); }

.p-2 { padding: var(--spacing-sm); }
.pt-2 { padding-top: var(--spacing-sm); }
.pr-2 { padding-right: var(--spacing-sm); }
.pb-2 { padding-bottom: var(--spacing-sm); }
.pl-2 { padding-left: var(--spacing-sm); }

.p-3 { padding: var(--spacing-md); }
.pt-3 { padding-top: var(--spacing-md); }
.pr-3 { padding-right: var(--spacing-md); }
.pb-3 { padding-bottom: var(--spacing-md); }
.pl-3 { padding-left: var(--spacing-md); }

.p-4 { padding: var(--spacing-lg); }
.pt-4 { padding-top: var(--spacing-lg); }
.pr-4 { padding-right: var(--spacing-lg); }
.pb-4 { padding-bottom: var(--spacing-lg); }
.pl-4 { padding-left: var(--spacing-lg); }
