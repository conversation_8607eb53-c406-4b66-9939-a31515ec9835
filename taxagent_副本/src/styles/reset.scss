// CSS 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

// 清除默认样式
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: normal;
}

p {
  margin: 0;
}

a {
  text-decoration: none;
  color: inherit;
}

button {
  border: none;
  outline: none;
  background: none;
  cursor: pointer;
}

input, textarea {
  outline: none;
}

// 清除浮动
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}
