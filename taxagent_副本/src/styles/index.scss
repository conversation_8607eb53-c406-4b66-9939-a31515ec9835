// 全局样式文件
@use './variables.scss';
@use './reset.scss';
@use './common.scss';

// NProgress 样式覆盖
#nprogress .bar {
  background: var(--el-color-primary) !important;
  height: 3px !important;
}

#nprogress .peg {
  box-shadow: 0 0 10px var(--el-color-primary), 0 0 5px var(--el-color-primary) !important;
}

// 全局滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color);
}

// 表格样式优化
.el-table {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-fill-color-light);
    }
  }
}

// 卡片样式优化
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

// 按钮组样式
.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

// 搜索表单样式
.search-form {
  .el-form-item {
    margin-bottom: 16px;
  }
}

// 页面容器样式
.page-container {
  padding: 20px;
  
  .page-header {
    margin-bottom: 20px;
    
    .page-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin: 0;
    }
  }
  
  .page-content {
    .el-card + .el-card {
      margin-top: 20px;
    }
  }
}

// 工具栏样式
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .toolbar-left {
    display: flex;
    gap: 8px;
  }
  
  .toolbar-right {
    display: flex;
    gap: 8px;
  }
}

// 状态标签样式
.status-tag {
  &.active {
    color: var(--el-color-success);
  }
  
  &.inactive {
    color: var(--el-color-danger);
  }
  
  &.locked {
    color: var(--el-color-warning);
  }
}
