import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authApi } from '@/api/auth'
import Cookies from 'js-cookie'

export const useAuthStore = defineStore('auth', () => {
  // 状态 - 从本地存储恢复 userInfo
  const token = ref(Cookies.get('token') || '')
  const userInfo = ref((() => {
    try {
      const stored = localStorage.getItem('userInfo')
      console.log('🔍 从 localStorage 读取 userInfo:', stored)
      const parsed = stored ? JSON.parse(stored) : null
      console.log('🔍 解析后的 userInfo:', parsed)
      return parsed
    } catch (error) {
      console.error('❌ 解析 userInfo 失败:', error)
      return null
    }
  })())
  const permissions = ref([])
  const roles = ref([])
  const menus = ref([])
  const buttons = ref([])

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!userInfo.value)
  
  // 检查是否有指定权限
  const hasPermission = computed(() => (permission) => {
    if (!permission) return true
    return permissions.value.includes(permission)
  })
  
  // 检查是否有指定角色
  const hasRole = computed(() => (role) => {
    if (!role) return true
    return roles.value.includes(role)
  })

  // 登录
  const login = async (loginForm) => {
    try {
      const response = await authApi.login(loginForm)

      if (response.code != 200) {
        throw new Error(response.msg || '登录失败')
      }

      // 处理不同的响应格式
      let loginData = response

      // 如果响应有 data 字段，使用 data
      if (response.data) {
        loginData = response.data
      }

      // 如果没有 token，可能是直接返回的用户信息
      if (!loginData.token && response.token) {
        loginData = response
      }

      // 保存token
      if (loginData.token) {
        token.value = loginData.token
        Cookies.set('token', loginData.token, { expires: 7 })
      }

      // 保存用户信息
      if (loginData.userId || loginData.username) {
        const userData = {
          userId: loginData.userId,
          username: loginData.username,
          fullName: loginData.fullName || loginData.name,
          email: loginData.email,
          phone: loginData.phone,
          tenantId: loginData.tenantId,
          tenant: loginData.tenant
        }
        userInfo.value = userData
        // 持久化到 localStorage
        console.log('💾 保存 userInfo 到 localStorage:', userData)
        localStorage.setItem('userInfo', JSON.stringify(userData))
        console.log('✅ localStorage 保存完成，验证:', localStorage.getItem('userInfo'))
      }

      // 保存权限信息
      roles.value = loginData.roles?.map(role => role.roleName || role) || []
      
      // 从 permissions、menus 和 buttons 中收集所有权限码
      const permissionCodes = loginData.permissions?.map(perm => perm.permCode || perm) || []
      const menuCodes = loginData.menus?.map(menu => menu.resourceCode || menu.permCode || menu) || []
      const buttonCodes = loginData.buttons?.map(button => button.resourceCode || button.permCode || button) || []
      // 合并所有权限码并去重
      permissions.value = [...new Set([...permissionCodes, ...menuCodes, ...buttonCodes])]
      
      menus.value = loginData.menus || []
      buttons.value = loginData.buttons || []

      console.log('✅ 权限信息已保存:', {
        roles: roles.value,
        permissions: permissions.value
      })

      return response
    } catch (error) {
      console.error('❌ 登录失败:', error)
      throw error
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地状态
      clearAuth()
    }
  }

  // 清除认证信息
  const clearAuth = () => {
    console.log('🗑️ 清除认证信息')
    token.value = ''
    userInfo.value = null
    permissions.value = []
    roles.value = []
    menus.value = []
    buttons.value = []
    Cookies.remove('token')
    localStorage.removeItem('userInfo') // 清除持久化的用户信息
    console.log('✅ localStorage userInfo 已清除')
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) {
      clearAuth()
      return false
    }

    try {
      const response = await authApi.getCurrentUserPermissions()
      const { data } = response

      // 更新用户信息
      const userData = data.user_info
      userInfo.value = userData
      // 持久化到 localStorage
      if (userData) {
        console.log('💾 checkAuth 保存 userInfo 到 localStorage:', userData)
        localStorage.setItem('userInfo', JSON.stringify(userData))
        console.log('✅ localStorage 保存完成，验证:', localStorage.getItem('userInfo'))
      }
      
      roles.value = data.roles || []
      permissions.value = data.permissions || []
      menus.value = data.menus || []
      buttons.value = data.buttons || []

      return true
    } catch (error) {
      console.error('检查认证状态失败:', error)
      
      // 根据错误类型决定是否清除认证信息
      if (error.response?.status === 401) {
        // token 过期或无效，清除认证信息
        clearAuth()
      } else {
        // 网络错误等，保留 token，稍后重试
        console.warn('认证检查失败，但保留 token 以便重试')
      }
      
      return false
    }
  }

  // 刷新用户权限
  const refreshPermissions = async () => {
    try {
      const response = await authApi.getCurrentUserPermissions()
      const { data } = response

      roles.value = data.roles || []
      permissions.value = data.permissions || []
      menus.value = data.menus || []
      buttons.value = data.buttons || []

      return data
    } catch (error) {
      console.error('刷新权限失败:', error)
      throw error
    }
  }

  // 监听 userInfo 变化，自动保存到 localStorage
  watch(userInfo, (newUserInfo) => {
    if (newUserInfo) {
      console.log('👀 userInfo 变化，自动保存:', newUserInfo)
      localStorage.setItem('userInfo', JSON.stringify(newUserInfo))
    } else {
      console.log('👀 userInfo 清空，移除 localStorage')
      localStorage.removeItem('userInfo')
    }
  }, { deep: true })

  return {
    // 状态
    token,
    userInfo,
    permissions,
    roles,
    menus,
    buttons,
    
    // 计算属性
    isAuthenticated,
    hasPermission,
    hasRole,
    
    // 方法
    login,
    logout,
    clearAuth,
    checkAuth,
    refreshPermissions
  }
})
