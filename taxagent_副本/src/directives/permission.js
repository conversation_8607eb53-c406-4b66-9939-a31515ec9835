// 权限控制指令
import { useAuthStore } from '@/stores/auth'

// 权限检查函数
function checkPermission(el, binding) {
  const authStore = useAuthStore()
  const { value } = binding

  if (!value) {
    return true
  }

  let hasPermission = false

  if (typeof value === 'string') {
    // 单个权限
    hasPermission = authStore.hasPermission(value)
  } else if (Array.isArray(value)) {
    // 权限数组，默认需要所有权限
    hasPermission = value.every(permission => authStore.hasPermission(permission))
  } else if (typeof value === 'object') {
    // 对象形式，支持更复杂的权限控制
    const { permissions, roles, mode = 'all' } = value

    if (permissions) {
      if (mode === 'any') {
        hasPermission = permissions.some(permission => authStore.hasPermission(permission))
      } else {
        hasPermission = permissions.every(permission => authStore.hasPermission(permission))
      }
    }

    if (roles && hasPermission) {
      if (mode === 'any') {
        hasPermission = roles.some(role => authStore.hasRole(role))
      } else {
        hasPermission = roles.every(role => authStore.hasRole(role))
      }
    }
  }

  return hasPermission
}

// v-permission 指令
export const permission = {
  mounted(el, binding) {
    const hasPermission = checkPermission(el, binding)
    if (!hasPermission) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const hasPermission = checkPermission(el, binding)
    if (!hasPermission) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// v-permission-remove 指令（直接移除元素）
export const permissionRemove = {
  mounted(el, binding) {
    const hasPermission = checkPermission(el, binding)
    if (!hasPermission) {
      el.remove()
    }
  }
}

// v-role 指令
export const role = {
  mounted(el, binding) {
    const authStore = useAuthStore()
    const { value } = binding

    if (!value) {
      return
    }

    let hasRole = false

    if (typeof value === 'string') {
      hasRole = authStore.hasRole(value)
    } else if (Array.isArray(value)) {
      hasRole = value.some(role => authStore.hasRole(role))
    }

    if (!hasRole) {
      el.style.display = 'none'
    }
  },
  updated(el, binding) {
    const authStore = useAuthStore()
    const { value } = binding

    if (!value) {
      return
    }

    let hasRole = false

    if (typeof value === 'string') {
      hasRole = authStore.hasRole(value)
    } else if (Array.isArray(value)) {
      hasRole = value.some(role => authStore.hasRole(role))
    }

    if (!hasRole) {
      el.style.display = 'none'
    } else {
      el.style.display = ''
    }
  }
}

// 导出所有指令
export default {
  permission,
  permissionRemove,
  role
}
