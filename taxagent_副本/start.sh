#!/bin/bash

# TaxAgent 前端项目启动脚本

echo "🚀 TaxAgent 管理系统前端启动脚本"
echo "=================================="

# 检查 Node.js 版本
echo "📋 检查环境..."
node_version=$(node -v 2>/dev/null)
if [ $? -ne 0 ]; then
    echo "❌ 错误: 未找到 Node.js，请先安装 Node.js 16.0+"
    exit 1
fi

echo "✅ Node.js 版本: $node_version"

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已存在"

    # 检查是否需要更新 Sass
    echo "🔍 检查 Sass 版本..."
    current_sass=$(npm list sass --depth=0 2>/dev/null | grep sass@ | sed 's/.*sass@//' | sed 's/ .*//')
    if [ -n "$current_sass" ]; then
        echo "当前 Sass 版本: $current_sass"
        # 如果版本低于 1.77.0，建议更新
        if [ "$(printf '%s\n' "1.77.0" "$current_sass" | sort -V | head -n1)" != "1.77.0" ]; then
            echo "⚠️  建议更新 Sass 到最新版本以避免弃用警告"
            echo "运行: npm install sass@latest sass-embedded@latest --save-dev"
        fi
    fi
fi

# 启动开发服务器
echo "🔥 启动开发服务器..."
echo "访问地址: http://localhost:3000"
echo "按 Ctrl+C 停止服务器"
echo ""

npm run dev
